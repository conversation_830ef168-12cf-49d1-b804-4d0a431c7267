<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.gf.saas</groupId>
        <artifactId>gf-saas-module-app</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>gf-saas-module-app-api</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description> <!--  新增 description 为该模块的描述 -->
        app 模块 API，暴露给其它模块调用
    </description>

    <dependencies>  <!--  新增 gf-saas-common 依赖 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-common</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>
</project>
