package cn.gf.saas.module.app.controller.app.auth;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.module.app.util.AuthorizeUtils;
import cn.gf.saas.module.app.util.SecurityUtils;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import java.io.IOException;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - Test")
@RestController
@RequestMapping("/app/test")
@Validated
public class DemoTestController {
    @Resource
    private SecurityUtils securityUtils;

//    @GetMapping("/get")
//    @Operation(summary = "获取 test 信息")
//    public CommonResult<String> get() {
//        return success("true");
//    }
//
//    /**
//     * 生成 RSA 加密的登录信息
//     *
//     * @param account    账号
//     * @param credential 密码
//     * @param loginType  登录类型
//     * @param uuid       UUID
//     * @return RSA 加密后的登录信息
//     */
//    @GetMapping("/createRsaInfo")
//    @PermitAll
//    public String generateEncryptedLoginInfo(@RequestParam("account") String account,
//                                             @RequestParam("credential") String credential,
//                                             @RequestParam("loginType") String loginType,
//                                             @RequestParam("uuid") String uuid) {
//        // 构建明文信息
//        StringBuilder plainTextBuilder = new StringBuilder();
//        plainTextBuilder.append(account).append(",");
//        plainTextBuilder.append(credential).append(",");
//        plainTextBuilder.append(loginType);
//        if (StrUtil.isNotBlank(uuid)) {
//            plainTextBuilder.append(",").append(uuid);
//        }
//
//        // RSA 加密
//        return securityUtils.rsaEncrypt(plainTextBuilder.toString());
//    }

}
