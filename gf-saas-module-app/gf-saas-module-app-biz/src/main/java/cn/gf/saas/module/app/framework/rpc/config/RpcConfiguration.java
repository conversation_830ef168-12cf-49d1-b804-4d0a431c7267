package cn.gf.saas.module.app.framework.rpc.config;

import cn.gf.saas.module.activity.api.activity.ActivityApi;
import cn.gf.saas.module.activity.api.notice.NoticeApi;
import cn.gf.saas.module.infra.api.config.ConfigApi;
import cn.gf.saas.module.infra.api.websocket.WebSocketSenderApi;
import cn.gf.saas.module.mall.api.diy.DiyApi;
import cn.gf.saas.module.system.api.company.CompanyApi;
import cn.gf.saas.module.system.api.logger.LoginLogApi;
import cn.gf.saas.module.system.api.member.MemberApi;
import cn.gf.saas.module.system.api.sms.SmsCodeApi;
import cn.gf.saas.module.system.api.social.SocialClientApi;
import cn.gf.saas.module.system.api.tenant.TenantApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {SmsCodeApi.class, WebSocketSenderApi.class,
        ConfigApi.class, SocialClientApi.class, LoginLogApi.class, CompanyApi.class,
        TenantApi.class, MemberApi.class, DiyApi.class, ActivityApi.class, NoticeApi.class})
public class RpcConfiguration {
}
