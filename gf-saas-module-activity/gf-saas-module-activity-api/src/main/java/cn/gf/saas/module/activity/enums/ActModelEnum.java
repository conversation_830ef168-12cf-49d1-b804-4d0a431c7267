package cn.gf.saas.module.activity.enums;

import cn.gf.saas.framework.common.core.IntArrayValuable;
import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 活动模块（知识竞赛/答题闯关/调查问卷等）
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ActModelEnum implements IntArrayValuable {

    KNOWLEDGE(1,  "知识竞赛"),
    PKGAME(2,  "PK答题"),
    QUESTIONNAIRE(3,  "调查问卷"),
    KNOWLEDGEGAME(4,  "答题闯关"),
    WISDOMGODDESS(5,  "智趣答题"),
    READINGACTIVITY(6,  "读书活动"),
    POETRYTALENT(7,  "诗词秀才"),
    VOTE(8,  "投票调查"),
    PRIZES(9,  "奖品领取"),
    LEARNINGGARDEN(10,  "学习园地"),
    STAFFCOLLEGE(11,  "职工学院"),
    LANTERN(12,  "猜灯谜"),
    GYM(13,  "场馆预定"),
    ADVICE(14,  "建言献策"),
    PROPOSAL(15,  "职工提案"),
    LOTTERY(16,  "幸运抽奖"),
    ;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ActModelEnum::getStatus).toArray();

    /**
     * 验证场景的编号
     */
    private final Integer status;
    /**
     * 描述
     */
    private final String description;

    public static ActModelEnum getCodeByScene(Integer scene) {
        return ArrayUtil.firstMatch(sceneEnum -> sceneEnum.getStatus().equals(scene),
                values());
    }

    @Override
    public int[] array() {
        return ARRAYS;
    }

}

