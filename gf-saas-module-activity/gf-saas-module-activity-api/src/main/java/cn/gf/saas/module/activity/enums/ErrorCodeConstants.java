package cn.gf.saas.module.activity.enums;

import cn.gf.saas.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {
    // ========== 我的活动相关 错误日志 1_006_000_000 ==========
    ErrorCode ACTIVITY_NOT_EXISTS = new ErrorCode(1_006_000_000, "活动不存在");
    ErrorCode ACTIVITY_IS_PUBLISH_CANNOT_DELETE = new ErrorCode(1_006_000_001, "活动已被发布，无法删除！");
    ErrorCode ACTIVITY_NOT_UNPUBLISH = new ErrorCode(1_006_000_002, "有用户参与，不能取消发布");

    ErrorCode ACTIVITY_NOT_PUBLISH_FOR_NO_QUESTIONS = new ErrorCode(1_006_000_003, "未设置题目，无法发布");

    ErrorCode ACTIVITY_NOT_PUBLISH_FOR_NO_BANNER = new ErrorCode(1_006_000_004, "未设置banner，无法发布");

    ErrorCode ACTIVITY_NOT_PUBLISH_FOR_NO_JOINTYPE = new ErrorCode(1_006_000_005, "未设置参与人员类型，无法发布");

    ErrorCode ACTIVITY_NOT_PUBLISH_FOR_NO_ACTSET = new ErrorCode(1_006_000_006, "未保存第二步活动设置，无法发布");
    // ========== 轮播图相关 错误日志 1_006_001_000 ==========
    ErrorCode MARKET_SLIDER_NOT_EXISTS = new ErrorCode(1_006_001_000, "活动市场轮播图不存在");
    // ========== 推文相关 错误日志 1_006_002_000 ==========
    ErrorCode MARKET_CONTENT_NOT_EXISTS = new ErrorCode(1_006_002_000, "活动市场推文不存在");
    ErrorCode MARKET_CONTENT_CONTAIN_PUBLISH = new ErrorCode(1_006_002_001, "包含已发布的推文");
    // ========== 活动市场应用相关 错误日志 1_006_003_000 ==========
    ErrorCode MARKET_APPLICATION_NOT_EXISTS = new ErrorCode(1_006_003_000, "活动市场应用不存在");
    // ========== 活动市场模板相关 错误日志 1_006_004_000 ==========
    ErrorCode MARKET_TEMPLATE_NOT_EXISTS = new ErrorCode(1_006_004_000, "活动市场模板不存在");
    ErrorCode MARKET_TEMPLATE_CONTAIN_ENABLE = new ErrorCode(1_006_004_001, "包含已启用的模板");
    // ========== 活动市场模板分类相关 错误日志 1_006_005_000 ==========
    ErrorCode MARKET_TEMPLATE_CATEGORY_NOT_EXISTS = new ErrorCode(1_006_005_000, "活动市场分类不存在");
    ErrorCode MARKET_TEMPLATE_CATEGORY_EXISTS = new ErrorCode(1_006_005_001, "存在已绑定模板的分类");
    // ========== 题库相关 错误日志 1_006_006_000 ==========
    ErrorCode UEM_KNOWLEDGE_NOT_EXISTS = new ErrorCode(1_006_005_000, "题库不存在");
    ErrorCode UEM_KNOWLEDGE_QUESTION_NOT_EXISTS = new ErrorCode(1_006_005_001, "题库题目不存在");

    ErrorCode KNOWLEDGE_QUESTION_NOT_EXISTS = new ErrorCode(1_006_005_002, "常规题题库不存在");

    ErrorCode KNOWLEDGE_USER_NOT_EXISTS = new ErrorCode(1_006_005_003, "选择人员不存在");

    ErrorCode KNOWLEDGE_QUESTION_FILE_NOT_EXISTS = new ErrorCode(1_006_005_004, "题目文件不存在");

    ErrorCode UEM_KNOWLEDGE_QUESTION_FILE_NOT_EXISTS = new ErrorCode(1_006_005_005, "题库题目文件不存在");

    ErrorCode KNOWLEDGE_FILE_NOT_EXISTS = new ErrorCode(1_006_005_006, "知识竞赛banner不存在");
    ErrorCode KNOWLEDGE_EXISTS_SHARE_BANNER = new ErrorCode(1_006_005_007, "已存在分享海报");
    // ========== 用户端相关 错误日志 1_006_007_000 ==========
    ErrorCode APP_USER_NOT_EXISTS = new ErrorCode(1_006_007_000, "用户端人员不存在");
    // ========== 调查问卷相关 错误日志 1_006_007_000 ==========
    ErrorCode QUESTIONNAIRE_NOT_EXISTS = new ErrorCode(1_006_008_000, "调查问卷不存在");
    ErrorCode QUESTIONNAIRE_QUESTION_NOT_EXISTS = new ErrorCode(1_006_008_001, "调查问卷题目不存在");

    ErrorCode QUESTIONNAIRE_USER_NOT_EXISTS = new ErrorCode(1_006_008_002, "调查问卷选择人员不存在");

    ErrorCode QUESTIONNAIRE_EXAM_NOT_EXISTS = new ErrorCode(1_006_008_003, "调查问卷答题不存在");

    ErrorCode QUESTIONNAIRE_INDEX_CANT_UP = new ErrorCode(1_006_008_004, "已经是第一题，不能再上移");
    ErrorCode QUESTIONNAIRE_INDEX_CANT_DOWN = new ErrorCode(1_006_008_004, "已经是最后一题，不能再下移");
    // ========== PK答题相关 错误日志 1_006_009_000 ==========
    ErrorCode QUIZ_GAME_NOT_EXISTS = new ErrorCode(1_006_009_000, "PK答题不存在");
    ErrorCode QUIZ_GAME_QUESTION_NOT_EXISTS = new ErrorCode(1_006_009_001, "pk答题题库不存在");

    ErrorCode QUIZ_GAME_QUESTION_OPTIONS_NOT_EXISTS = new ErrorCode(1_006_009_001, "PK答题选项不存在");

    ErrorCode QUIZ_GAME_OPT_USER_NOT_EXISTS = new ErrorCode(1_006_009_002, "pk答题选择人员不存在");

    // ========== 答题闯关相关 错误日志 1_006_010_000 ==========
    ErrorCode KNOWLEDGE_GAME_SELECT_DEPARTMENT = new ErrorCode(1_006_010_000, "请选择部门");
    ErrorCode KNOWLEDGE_GAME_EXPIRATION_DATE_ERROR = new ErrorCode(1_006_010_001, "竞赛有效期应大于答题结束时间");
    ErrorCode KNOWLEDGE_GAME_START_TIME_ERROR = new ErrorCode(1_006_010_002, "竞赛开始时间小于结束时间");
    ErrorCode KNOWLEDGE_GAME_IS_PUBLISH = new ErrorCode(1_006_010_003, "已发布活动不允许删除");
    ErrorCode KNOWLEDGE_GAME_RELE_CHINATAX_POINTS = new ErrorCode(1_006_010_004, "该活动已被国税局积分关联，禁止修改参与人员。");
    ErrorCode KNOWLEDGE_GAME_RELE_STAFF_COLLEGE = new ErrorCode(1_006_010_005, "活动已被职工学院关联，禁止修改参与人员。");
    ErrorCode KNOWLEDGE_GAME_ADD_LEVEL = new ErrorCode(1_006_010_006, "请添加关卡");
    ErrorCode KNOWLEDGE_GAME_ADD_USER_OR_UPDATE = new ErrorCode(1_006_010_007, "请添加参与用户或修改参与类别");
    ErrorCode KNOWLEDGE_GAME_DATA_EXCEPTION = new ErrorCode(1_006_010_008, "该条数据异常，请重新创建一条数据");
    ErrorCode KNOWLEDGE_GAME_LEVEL_ADD_QUESTION = new ErrorCode(1_006_010_009, "关卡%s请添加题目");
    ErrorCode KNOWLEDGE_GAME_LEVEL_NUM_ERROR = new ErrorCode(1_006_010_010, "关卡不能超过20个");
    ErrorCode KNOWLEDGE_GAME_LEVEL_QUESTIONS_NOT_EXIST = new ErrorCode(1_006_010_011, "[%s]题型不存在！");
    ErrorCode KNOWLEDGE_GAME_LEVEL_QUESTIONS_NUM_NOT_ENOUGH = new ErrorCode(1_006_010_012, "[%s]题型数量不足，请重新填写题目数量");
    ErrorCode KNOWLEDGE_GAME_LEVEL_QUESTIONS_VALUE_NOT_NULL = new ErrorCode(1_006_010_013, "[%s]题型分值不能为空");
    ErrorCode KNOWLEDGE_GAME_LEVEL_QUESTIONS_NUM_NOT_NULL = new ErrorCode(1_006_010_014, "[%s]题型数量不能为空");
    ErrorCode KNOWLEDGE_GAME_NOT_EXIST = new ErrorCode(1_006_010_015, "活动不存在");
    ErrorCode KNOWLEDGE_GAME_LEVEL_USED_DATA = new ErrorCode(1_006_010_016, "已有活动数据不允许删除");
    ErrorCode KNOWLEDGE_GAME_LEVEL_NOT_EXIST = new ErrorCode(1_006_010_017, "关卡不存在");
    ErrorCode KNOWLEDGE_GAME_LEVEL_RANDOM_SETTING_NOT_NULL = new ErrorCode(1_006_010_018, "未获取到随机题设置信息");
    ErrorCode KNOWLEDGE_GAME_LEVEL_RANDOM_SETTING_NUM = new ErrorCode(1_006_010_019, "请填写题目数量");
    ErrorCode KNOWLEDGE_GAME_LEVEL_RANDOM_SETTING_SCORE = new ErrorCode(1_006_010_020, "请填写分值");
    ErrorCode KNOWLEDGE_GAME_LEVEL_USED_USER_NOT_DELETE = new ErrorCode(1_006_010_021, "关卡已有人答题，无法删除");
    ErrorCode KNOWLEDGE_GAME_LEVEL_SCORE_IS_ERROR = new ErrorCode(1_006_010_022, "关卡过关分数不能大于题目的总分数");

    // ========== 智趣答题相关 错误日志 1_006_011_000 ==========
    ErrorCode WISDOM_GODDESS_START_TIME_ERROR = new ErrorCode(1_006_011_001, "智趣答题开始时间小于结束时间");
    ErrorCode WISDOM_GODDESS_CLASSIFY_NOT_EXIST = new ErrorCode(1_006_011_002, "分类不存在");
    ErrorCode WISDOM_GODDESS_CLASSIFY_NUM_ERROR = new ErrorCode(1_006_011_003, "分类数量不能超过20个");
    ErrorCode WISDOM_GODDESS_NOT_EXIST = new ErrorCode(1_006_011_004, "活动不存在");
    ErrorCode WISDOM_GODDESS_ADD_USER_OR_UPDATE = new ErrorCode(1_006_011_005, "请添加参与用户或修改参与类别");
    ErrorCode WISDOM_GODDESS_NOT_ADD_CLASS = new ErrorCode(1_006_011_006, "此活动未添加分类题目,发布失败");
    ErrorCode WISDOM_GODDESS_CLASSIFY_USED_USER_NOT_DELETE = new ErrorCode(1_006_010_006, "分类已有人答题，无法删除");
    ErrorCode WISDOM_GODDESS_CLASSIFY_ADD_QUESTION = new ErrorCode(1_006_010_009, "%s请添加题目");
    ErrorCode WISDOM_GODDESS_NOT_CLASS = new ErrorCode(1_006_011_010, "请完善活动分类配置！");

    // ========== 读书活动相关 错误日志 1_006_012_000 ==========
    ErrorCode READING_ACT_START_TIME_ERROR = new ErrorCode(1_006_012_001, "读书活动开始时间小于结束时间");
    ErrorCode READING_ACT_NOT_EXIST = new ErrorCode(1_006_012_002, "活动不存在");
    ErrorCode READING_ACT_NOT_UPDATE_END_TIME = new ErrorCode(1_006_012_003, "该读书活动有人签到或读书，不能修改结束时间！");
    ErrorCode READING_ACT_ADD_BOOK = new ErrorCode(1_006_012_004, "请添加至少存在一本书籍才能发布");
    ErrorCode READING_ACT_NOT_DELETE = new ErrorCode(1_006_012_005, "读书活动已发布，无法删除");
    ErrorCode READING_ACT_USER_READ_NOT_DELETE = new ErrorCode(1_006_012_006, "已有活动数据，无法删除");
    ErrorCode READING_ACT_EXPIRE_TIME_ERROR = new ErrorCode(1_006_012_007, "读书活动有效期时间不能小于结束时间");
    ErrorCode READING_ACT_HOME_IMG_MAX_NUM = new ErrorCode(1_006_012_008, "首页轮播图仅限5张");

    // ========== 诗词秀才相关 错误日志 1_006_013_000 ==========
    ErrorCode POETRY_TALENT_API_ERROR = new ErrorCode(1_006_013_001, "诗词秀才API服务异常");
    ErrorCode POETRY_TALENT_NOT_EXIST = new ErrorCode(1_006_013_002, "活动不存在");
    ErrorCode POETRY_TALENT_START_TIME_ERROR = new ErrorCode(1_006_013_003, "诗词秀才开始时间小于结束时间");
    ErrorCode POETRY_TALENT_EXPIRE_TIME_ERROR = new ErrorCode(1_006_013_004, "诗词秀才有效期时间大于等于活动结束时间");
    ErrorCode POETRY_TALENT_LEVEL_NUM_ERROR = new ErrorCode(1_006_013_005, "关卡不能超过100个");
    ErrorCode POETRY_TALENT_LEVEL_RANDOM_LETTER_NUM_ERROR = new ErrorCode(1_006_013_006, "随机字数量不能超过50个");
    ErrorCode POETRY_TALENT_LEVEL_RANDOM_LETTER_NUM_MORE = new ErrorCode(1_006_013_007, "不得超出诗词字数总上限");
    ErrorCode POETRY_TALENT_RANG_ERROR = new ErrorCode(1_006_013_008, "请配置活动范围");

    // ========== 投票调查相关 错误日志 1_006_014_000 ==========
    ErrorCode SURVEY_START_TIME_ERROR = new ErrorCode(1_006_014_001, "投票调查开始时间小于结束时间");
    ErrorCode SURVEY_EXPIRE_TIME_ERROR = new ErrorCode(1_006_014_002, "投票调查有效期时间大于等于活动结束时间");
    ErrorCode SURVEY_NOT_EXIST = new ErrorCode(1_006_014_003, "活动不存在");
    ErrorCode SURVEY_NOT_DELETE = new ErrorCode(1_006_014_004, "已参加投票不能删除");
    ErrorCode SURVEY_RANG_ERROR = new ErrorCode(1_006_014_005, "请配置活动范围");

    // ========== 奖品领取相关 错误日志 1_006_015_000 ==========
    ErrorCode PRIZES_START_TIME_ERROR = new ErrorCode(1_006_015_001, "奖品领取开始时间小于结束时间");
    ErrorCode PRIZES_PURCHASE_LIMIT_ERROR = new ErrorCode(1_006_015_002, "奖品领取开始时间小于结束时间");
    ErrorCode PRIZES_NOT_EXIST = new ErrorCode(1_006_015_003, "活动不存在");
    ErrorCode PRIZES_NOT_EXISTS = new ErrorCode(1_006_015_004, "奖品不存在");
    ErrorCode PRIZES_NOT_DELETE = new ErrorCode(1_006_015_005, "奖品已被领取，无法删除");
    ErrorCode PRIZES_SUN_ERROR = new ErrorCode(1_006_015_006, "奖品总量小于已领数量，请重新设置");
    ErrorCode PRIZES_NUM_ERROR = new ErrorCode(1_006_015_007, "奖品数量不能超过50个");

    // ========== 学习园地相关 错误日志 1_006_016_000 ==========
    ErrorCode LEARNING_GARDEN_SECTION_ERROR = new ErrorCode(1_006_016_001, "分类已被使用无法删除");

    // ========== 新闻资讯相关 错误日志 1_006_010_000 ==========
    ErrorCode NOTICE_NOT_EXISTS = new ErrorCode(1_006_012_000, "新闻资讯/通知公告不存在");

    // ========== 职工学院相关 错误日志 1_006_017_000 ==========
    /** 职工学院开始时间小于结束时间 */
    ErrorCode STAFF_COLLEGE_START_TIME_ERROR = new ErrorCode(1_006_017_001, "职工学院开始时间小于结束时间");
    /** 职工学院不存在 */
    ErrorCode STAFF_COLLEGE_NOT_EXIST = new ErrorCode(1_006_017_002, "职工学院课程不存在");
    /** 职工学院未配置活动范围 */
    ErrorCode STAFF_COLLEGE_RANG_ERROR = new ErrorCode(1_006_017_003, "请配置活动范围");
    /** 职工学院视频已存在活动数据，无法删除 */
    ErrorCode STAFF_COLLEGE_VIDEO_HAS_DATA = new ErrorCode(1_006_017_004, "视频已存在活动数据，无法删除");
    /** 职工学院视频数量不能超过20个 */
    ErrorCode STAFF_COLLEGE_VIDEO_NUM_ERROR = new ErrorCode(1_006_017_005, "视频数量不能超过20个");

    // ========== 猜灯谜活动相关 错误日志 1_006_018_000 ==========
    /** 猜灯谜活动开始时间小于结束时间 */
    ErrorCode LANTERN_ACT_START_TIME_ERROR = new ErrorCode(1_006_018_001, "猜灯谜活动开始时间小于结束时间");
    /** 猜灯谜活动不存在 */
    ErrorCode LANTERN_ACT_NOT_EXIST = new ErrorCode(1_006_018_002, "猜灯谜活动不存在");
    /** 猜灯谜活动未添加分类 */
    ErrorCode LANTERN_ACT_NO_CLASSIFY = new ErrorCode(1_006_018_003, "此活动未添加分类,发布失败");
    /** 猜灯谜分类不存在 */
    ErrorCode LANTERN_ACT_CLASSIFY_NOT_EXIST = new ErrorCode(1_006_018_004, "分类不存在");
    /** 猜灯谜分类数量超限 */
    ErrorCode LANTERN_ACT_CLASSIFY_NUM_ERROR = new ErrorCode(1_006_018_005, "分类数量不能超过20个");
    /** 猜灯谜分类已有人答题，无法删除 */
    ErrorCode LANTERN_ACT_CLASSIFY_USED_USER_NOT_DELETE = new ErrorCode(1_006_018_006, "分类已有人答题，无法删除");
    /** 猜灯谜活动需要添加用户或更新参与类型 */
    ErrorCode LANTERN_ACT_ADD_USER_OR_UPDATE = new ErrorCode(1_006_018_007, "请添加用户或更新参与类型");
    /** 猜灯谜活动未添加分类题目 */
    ErrorCode LANTERN_ACT_NO_CLASSIFY_QUESTION = new ErrorCode(1_006_018_008, "此活动未添加分类题目,发布失败");
    /** 猜灯谜活动未添加分类 */
    ErrorCode LANTERN_ACT_NO_ADD_CLASSIFY = new ErrorCode(1_006_018_009, "此活动未添加分类");

    // ========== 场馆预定时间段相关 错误日志 1_006_019_000 ==========
    ErrorCode GYM_SCHEDULED_TIME_DETAIL_NOT_EXISTS = new ErrorCode(1_006_019_000, "场馆预定时间段不存在");
    ErrorCode GYM_SCHEDULED_TIME_DETAIL_HAS_SCHEDULE = new ErrorCode(1_006_019_001, "时间段已被预定，无法删除");

    // ========== 场馆预定记录相关 错误日志 1_006_020_000 ==========
    ErrorCode GYM_SCHEDULE_NOT_EXISTS = new ErrorCode(1_006_020_000, "场馆预定记录不存在");

    // ========== 场馆预定相关 错误日志 1_006_021_000 ==========
    ErrorCode GYM_NOT_EXISTS = new ErrorCode(1_006_021_000, "场馆不存在");
    ErrorCode GYM_HAS_SCHEDULE = new ErrorCode(1_006_021_001, "场馆已被预定，无法删除");

    // ========== 建言献策相关 错误日志 1_006_022_000 ==========
    ErrorCode ADVICE_NOT_EXISTS = new ErrorCode(1_006_022_000, "建言献策不存在");

    // ========== 职工提案相关 错误日志 1_006_023_000 ==========
    ErrorCode PROPOSAL_NOT_EXISTS = new ErrorCode(1_006_023_000, "职工提案不存在");
    ErrorCode PROPOSAL_STATUS_ERROR = new ErrorCode(1_006_023_001, "提案状态错误，无法执行该操作");
    ErrorCode PROPOSAL_APPROVAL_ERROR = new ErrorCode(1_006_023_002, "提案审批失败");

    // ========== 幸运抽奖相关 错误日志 1_006_024_000 ==========
    ErrorCode LOTTERY_NOT_EXISTS = new ErrorCode(1_006_024_000, "幸运抽奖活动不存在");
    ErrorCode LOTTERY_TOTAL_LESS_THAN_DAILY = new ErrorCode(1_006_024_001, "每人共可抽奖次数不能小于每人每天可抽奖次数");
    ErrorCode LOTTERY_USER_COUNT_LESS_THAN_PRIZE_COUNT = new ErrorCode(1_006_024_002, "预抽奖人数不能小于奖项数量总和");
    ErrorCode LOTTERY_PRIZE_NOT_EXISTS = new ErrorCode(1_006_024_003, "奖项不存在");
    ErrorCode LOTTERY_PRIZE_COUNT_EXCEED_LIMIT = new ErrorCode(1_006_024_004, "最多支持新增10个奖品");
    ErrorCode LOTTERY_NO_PRIZES = new ErrorCode(1_006_024_005, "未添加任何奖项");
    ErrorCode LOTTERY_NO_USERS = new ErrorCode(1_006_024_006, "未添加参与人员");
    ErrorCode LOTTERY_AREA_RATIO_ERROR = new ErrorCode(1_006_024_007, "奖项面积占比应全为0或之和小于等于1");
    ErrorCode LOTTERY_PRIZE_COUNT_LESS_THAN_USER_COUNT = new ErrorCode(1_006_024_008, "面积占比之和等于1时，奖项数应大于等于参与人数");
    ErrorCode LOTTERY_ALREADY_STARTED = new ErrorCode(1_006_024_009, "活动已开始，无法取消发布");
    ErrorCode LOTTERY_ALREADY_PUBLISHED = new ErrorCode(1_006_024_010, "已发布，无法删除！");
    ErrorCode LOTTERY_ALREADY_PUBLISHED_CANNOT_MODIFY = new ErrorCode(1_006_024_011, "已发布不可修改");
    ErrorCode LOTTERY_PRIZE_NUMBER_CANNOT_BE_ZERO = new ErrorCode(1_006_024_012, "奖项数量不能为0");
    ErrorCode LOTTERY_PRIZE_AMOUNT_CANNOT_BE_ZERO = new ErrorCode(1_006_024_013, "奖项金额不能为0");
    ErrorCode LOTTERY_WHITELIST_EXCEED_PRIZE_COUNT = new ErrorCode(1_006_024_014, "奖项需大于白名单数量,请修改奖项数量！");
}
