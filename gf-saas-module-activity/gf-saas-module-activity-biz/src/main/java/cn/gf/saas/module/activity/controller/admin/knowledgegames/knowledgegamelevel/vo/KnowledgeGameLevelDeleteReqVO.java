package cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegamelevel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 答题闯关-关卡删除 Request VO")
@Data
public class KnowledgeGameLevelDeleteReqVO {

    @Schema(description = "关卡ids")
    @NotNull(message = "关卡ids不能为空")
    private List<Long> ids;

    @Schema(description = "活动id")
    @NotNull(message = "活动id不能为空")
    private Integer gameId;
}
