package cn.gf.saas.module.activity.controller.admin.readingact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * readingactuser 表的请求实体类
 */
@Data
public class ReadingActUserReq {

    /**
     * 活动ID
     */
    @Schema(description = "活动ID")
    private Integer actId;

    @Schema(description = "参与人员IDs")
    private List<String> userIds;
}    