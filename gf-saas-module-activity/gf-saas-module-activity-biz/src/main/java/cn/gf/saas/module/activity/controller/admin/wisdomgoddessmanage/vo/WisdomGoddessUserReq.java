package cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * wisdomgoddessuser 表的请求实体类
 */
@Data
public class WisdomGoddessUserReq {

    /**
     * 活动ID
     */
    @Schema(description = "活动ID")
    private Integer actId;

    @Schema(description = "参与人员IDs")
    private List<String> userIds;
}    