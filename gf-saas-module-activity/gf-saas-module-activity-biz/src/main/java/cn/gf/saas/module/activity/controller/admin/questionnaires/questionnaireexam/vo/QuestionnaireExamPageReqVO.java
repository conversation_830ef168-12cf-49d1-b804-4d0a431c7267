package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireexam.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 查看答卷分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QuestionnaireExamPageReqVO extends PageParam {

    @Schema(description = "问卷ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19631")
    private Integer questionnaireId;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "手机号")
    private String mobilePhone;



}