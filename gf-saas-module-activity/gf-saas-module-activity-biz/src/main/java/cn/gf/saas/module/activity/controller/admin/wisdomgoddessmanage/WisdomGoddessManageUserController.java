package cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameUserAddReqVO;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameUserPageReqVO;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameUserRespVO;
import cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo.WisdomGoddessUserPageReq;
import cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo.WisdomGoddessUserReq;
import cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo.WisdomGoddessUserResp;
import cn.gf.saas.module.activity.dal.dataobject.appuser.AppUserDO;
import cn.gf.saas.module.activity.dal.dataobject.knowledgegame.KnowledgeGameUserDO;
import cn.gf.saas.module.activity.dal.dataobject.wisdomgoddess.WisdomGoddessUserDO;
import cn.gf.saas.module.activity.service.appuser.AppUserService;
import cn.gf.saas.module.activity.service.wisdomgoddessmanage.WisdomGoddessManageUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 智趣答题")
@RestController
@RequestMapping("/activity/wisdom-goddess-user")
@Validated
public class WisdomGoddessManageUserController {

    @Resource
    private WisdomGoddessManageUserService wisdomGoddessManageUserService;
    @Resource
    private AppUserService userService;

    @PostMapping("/add")
    @Operation(summary = "指定人员-添加")
    public CommonResult<Boolean> add(@Valid @RequestBody WisdomGoddessUserReq createReqVO) {
        return success(wisdomGoddessManageUserService.add(createReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除选择人员")
    @Parameter(name = "actId", description = "编号", required = true)
    public CommonResult<Boolean> deleteWisdomGoddessUser(@RequestBody List<String> userIds, @RequestParam("actId") Integer actId) {
        wisdomGoddessManageUserService.deleteWisdomGoddessUser(userIds, actId);
        return success(true);
    }


    @GetMapping("/page")
    @Operation(summary = "获得选择人员分页")
    public CommonResult<PageResult<WisdomGoddessUserResp>> getWisdomGoddessUserPage(@Valid WisdomGoddessUserPageReq pageReqVO) {
        PageResult<WisdomGoddessUserDO> pageResult = wisdomGoddessManageUserService.getWisdomGoddessUserPage(pageReqVO);
        PageResult<WisdomGoddessUserResp> list = BeanUtils.toBean(pageResult, WisdomGoddessUserResp.class);
        if(list.getTotal() == 0){
            return success(list);
        }
        List<AppUserDO> users = userService.getAppUsersByIds( pageResult.getList().stream().map(WisdomGoddessUserDO::getUserId).collect(Collectors.toList()));

        for (WisdomGoddessUserResp item:list.getList()) {
            AppUserDO user = users.stream().filter(c->c.getUserId().equals(item.getUserId())).findFirst().orElse(null);
            if(user != null) {
                item.setDepartLevel(user.getDepartLevel1());
                item.setPhone(user.getMobilePhone());
                item.setName(user.getRealName());
            }
        }
        return success(list);
    }
}
