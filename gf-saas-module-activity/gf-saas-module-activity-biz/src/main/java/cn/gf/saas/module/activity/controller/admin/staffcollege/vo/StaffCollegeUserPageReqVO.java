package cn.gf.saas.module.activity.controller.admin.staffcollege.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 职工学院参与用户分页 Request VO")
@Data
public class StaffCollegeUserPageReqVO extends PageParam {
    @Schema(description = "课程编号", example = "1001")
    private Integer courseId;

    @Schema(description = "参与用户ID", example = "u123")
    private String userId;
} 