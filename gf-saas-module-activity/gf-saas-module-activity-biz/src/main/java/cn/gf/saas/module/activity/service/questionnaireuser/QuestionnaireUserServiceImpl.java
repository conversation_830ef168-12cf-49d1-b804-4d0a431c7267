package cn.gf.saas.module.activity.service.questionnaireuser;

import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.gf.saas.module.activity.controller.admin.activity.vo.*;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireuser.vo.QuestionnaireUserPageReqVO;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireuser.vo.QuestionnaireUserSaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.knowledgeuser.KnowledgeUserDO;
import cn.gf.saas.module.activity.dal.dataobject.questionnaireuser.QuestionnaireUserDO;
import cn.gf.saas.module.activity.dal.mysql.questionnaireuser.QuestionnaireUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static cn.gf.saas.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.gf.saas.module.activity.enums.ErrorCodeConstants.*;

/**
 * 调查问卷选择人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionnaireUserServiceImpl implements QuestionnaireUserService {

    @Resource
    private QuestionnaireUserMapper questionnaireUserMapper;

    @Override
    public Boolean createQuestionnaireUser(QuestionnaireUserSaveReqVO createReqVO) {
        List<QuestionnaireUserDO> users = new ArrayList<>();
        for (String userId :
                createReqVO.getUserIds()) {
            QuestionnaireUserDO questionnaireUser = new QuestionnaireUserDO();
            questionnaireUser.setQuestionnaireId(createReqVO.getQuestionnaireId());
            questionnaireUser.setUserId(userId);
            questionnaireUser.setJoinTime(LocalDateTime.now());
            questionnaireUser.setJoinType((short) 2);
            Boolean isExist = questionnaireUserMapper.exists(new LambdaQueryWrapperX<QuestionnaireUserDO>()
                    .eq(QuestionnaireUserDO::getUserId,userId)
                    .eq(QuestionnaireUserDO::getQuestionnaireId,createReqVO.getQuestionnaireId()));
            if(isExist) continue;
            users.add(questionnaireUser);
        }
        if(users.isEmpty()){
            return true;
        }
        Boolean success = questionnaireUserMapper.insertBatch(users);
        // 返回
        return success;
    }

    @Override
    public void deleteQuestionnaireUser(List<String> ids,Integer qId) {
        // 删除
        questionnaireUserMapper.delete(new LambdaQueryWrapperX<QuestionnaireUserDO>()
                .in(QuestionnaireUserDO::getUserId,ids)
                .eq(QuestionnaireUserDO::getQuestionnaireId,qId));
    }

    private void validateQuestionnaireUserExists(Integer id) {
        if (questionnaireUserMapper.selectById(id) == null) {
            throw exception(QUESTIONNAIRE_USER_NOT_EXISTS);
        }
    }

    @Override
    public QuestionnaireUserDO getQuestionnaireUser(Integer id) {
        return questionnaireUserMapper.selectById(id);
    }

    @Override
    public PageResult<QuestionnaireUserDO> getQuestionnaireUserPage(QuestionnaireUserPageReqVO pageReqVO) {
        return questionnaireUserMapper.selectPage(pageReqVO);
    }

}