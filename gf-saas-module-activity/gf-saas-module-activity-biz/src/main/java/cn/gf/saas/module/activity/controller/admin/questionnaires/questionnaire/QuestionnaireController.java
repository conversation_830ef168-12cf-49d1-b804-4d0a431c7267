package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaire;

import cn.gf.saas.framework.apilog.core.annotation.ApiAccessLog;
import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageParam;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.framework.excel.core.util.ExcelUtils;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaire.vo.QuestionnairePageReqVO;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaire.vo.QuestionnaireRespVO;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaire.vo.QuestionnaireSaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.questionnaire.QuestionnaireDO;
import cn.gf.saas.module.activity.service.questionnaire.QuestionnaireService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.gf.saas.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 调查问卷")
@RestController
@RequestMapping("/activity/questionnaire")
@Validated
public class QuestionnaireController {

    @Resource
    private QuestionnaireService questionnaireService;

    @PostMapping("/create")
    @Operation(summary = "创建调查问卷")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire:create')")
    public CommonResult<Integer> createQuestionnaire(@Valid @RequestBody QuestionnaireSaveReqVO createReqVO) {
        return success(questionnaireService.createQuestionnaire(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新调查问卷")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire:update')")
    public CommonResult<Boolean> updateQuestionnaire(@Valid @RequestBody QuestionnaireSaveReqVO updateReqVO) {
        questionnaireService.updateQuestionnaire(updateReqVO);
        return success(true);
    }


    @GetMapping("/get")
    @Operation(summary = "获得调查问卷")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire:query')")
    public CommonResult<QuestionnaireRespVO> getQuestionnaire(@RequestParam("id") Integer id) {
        QuestionnaireDO questionnaire = questionnaireService.getQuestionnaire(id);
        return success(BeanUtils.toBean(questionnaire, QuestionnaireRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得调查问卷分页")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire:query')")
    public CommonResult<PageResult<QuestionnaireRespVO>> getQuestionnairePage(@Valid QuestionnairePageReqVO pageReqVO) {
        PageResult<QuestionnaireDO> pageResult = questionnaireService.getQuestionnairePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, QuestionnaireRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出调查问卷 Excel")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportQuestionnaireExcel(@Valid QuestionnairePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<QuestionnaireDO> list = questionnaireService.getQuestionnairePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "调查问卷.xls", "数据", QuestionnaireRespVO.class,
                        BeanUtils.toBean(list, QuestionnaireRespVO.class));
    }

}