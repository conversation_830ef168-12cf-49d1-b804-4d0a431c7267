package cn.gf.saas.module.activity.controller.admin.poetrytalent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 诗词秀才选择人员新增/修改 Request VO")
@Data
public class PoetryTalentUserAddReqVO {

    @Schema(description = "诗词秀才ID", example = "1164")
    private Integer gameId;

    @Schema(description = "参与人员IDs")
    private List<String> userIds;

}