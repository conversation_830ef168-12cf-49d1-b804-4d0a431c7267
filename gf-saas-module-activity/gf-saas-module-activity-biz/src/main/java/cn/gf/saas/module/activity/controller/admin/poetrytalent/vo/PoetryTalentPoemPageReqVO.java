package cn.gf.saas.module.activity.controller.admin.poetrytalent.vo;


import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Schema(description = "管理后台 - 诗词秀才诗词分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PoetryTalentPoemPageReqVO extends PageParam {

    @Schema(description = "诗人/词人")
    @Size(max = 10, message = "诗人/词人长度不能超过10个")
    private String author;

    @Schema(description = "诗词名")
    @Size(max = 20, message = "诗词名长度不能超过20个")
    private String name;

    @Schema(description = "格律")
    private String rule;

    @Schema(description = "类型")
    private Long typeId;
}
