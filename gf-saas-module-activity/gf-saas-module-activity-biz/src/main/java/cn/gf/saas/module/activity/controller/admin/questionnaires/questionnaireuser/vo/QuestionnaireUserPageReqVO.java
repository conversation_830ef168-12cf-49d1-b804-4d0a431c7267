package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireuser.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 调查问卷选择人员分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QuestionnaireUserPageReqVO extends PageParam {

    @Schema(description = "知识竞赛活动ID", example = "25734")
    private Integer questionnaireId;

    @Schema(description = "参与人员ID", example = "16950")
    private String userId;

    @Schema(description = "加入时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] joinTime;

    @Schema(description = "加入途径，1自主报名，2被选择", example = "2")
    private Short joinType;

}