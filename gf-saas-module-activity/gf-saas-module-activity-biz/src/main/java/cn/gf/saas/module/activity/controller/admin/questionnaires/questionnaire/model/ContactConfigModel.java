package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaire.model;

import cn.gf.saas.module.activity.enums.QuestionTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文件客户端的配置
 * 不同实现的客户端，需要不同的配置，通过子类来定义
 *
 * <AUTHOR>
 */
@Data
public class ContactConfigModel {
    @JsonProperty("organizer")
    @Schema(description = "主办方", example = "工福科技")
    private String organizer;
    @JsonProperty("contactName")
    @Schema(description = "联系人", example = "李大嘴")
    private String contactName;
    @JsonProperty("contactPhone")
    @Schema(description = "联系电话", example = "13279744145")
    private String contactPhone;
    @JsonProperty("address")
    @Schema(description = "活动地点", example = "西港发展中心")
    private String address;


}