package cn.gf.saas.module.activity.controller.admin.lottery.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 幸运抽奖选择人员 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LotteryUserRespVO {

    @Schema(description = "参与人员ID", example = "6469")
    @ExcelProperty("参与人员ID")
    private String userId;

    @Schema(description = "姓名", example = "吴清焕")
    @ExcelProperty("姓名")
    private String name;

    @Schema(description = "手机号码", example = "13276711014")
    @ExcelProperty("手机号码")
    private String phone;

    @Schema(description = "组织", example = "研发一部")
    @ExcelProperty("组织")
    private String departLevel;
}
