package cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Date;

import static cn.gf.saas.framework.common.util.date.DateUtils.*;

@Schema(description = "管理后台 - 答题闯关新增 Request VO")
@Data
public class KnowledgeGameCreateReqVO {

    @Schema(description = "活动名称")
    @NotBlank(message = "活动名称不能为空")
    @Size(max = 50, message = "标题长度不大于50个字")
    private String title;

    @Schema(description = "主题类型 Id：0 默认 1 五一")
    private Integer themeTypeId;

    @Schema(description = "活动开始时间")
    @NotNull(message = "活动开始时间不能为空")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime startTime;

    @Schema(description = "活动结束时间")
    @NotNull(message = "活动结束时间不能为空")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime endTime;

    @Schema(description = "有效期")
    @NotNull(message = "有效期不能为空")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime validDate;

    @Schema(description = "活动规则")
    @NotBlank(message = "活动规则不能为空")
    @Size(max = 2000, message = "活动规则长度不大于500个字")
    private String rules;

    @Schema(description = "是否显示标题")
    private Boolean isShowTitle;

    @Schema(description = "重新闯关次数，0 表示不限")
    private Integer redoTime;

    @Schema(description = "每日闯关次数，0 表示不限")
    private Integer dayredotime;

    @Schema(description = "得分规则-关卡取值")
    @NotNull(message = "得分规则-关卡取值不能为空")
    private Integer getValueType;

    @Schema(description = "得分规则-活动取值")
    @NotNull(message = "得分规则-活动取值不能为空")
    private Boolean saveTopScore;

    @Schema(description = "暂停闯关")
    private Boolean isUserPause;

    @Schema(description = "弹出公告")
    private Integer isCompletednotice;

    @Schema(description = "公告标题")
    @Size(max = 50, message = "公告标题长度不大于50个字")
    private String completednoticetitle;

    @Schema(description = "公告内容")
    @Size(max = 200, message = "公告内容长度不大于500个字")
    private String completednotice;

    @Schema(description = "是否启用排行榜")
    private Boolean isShowRank;

    @Schema(description = "显示排名数量  -1表示不显示排名,允许设置负数,0表示全部显示")
    private Integer rankLimit;

    @Schema(description = "显示答案方式 true 不显示正确答案 false 显示正确答案")
    private Boolean notdisplayanswer;

}
