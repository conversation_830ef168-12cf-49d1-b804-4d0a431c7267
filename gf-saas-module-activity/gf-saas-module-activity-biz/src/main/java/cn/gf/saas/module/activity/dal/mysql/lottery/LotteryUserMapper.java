package cn.gf.saas.module.activity.dal.mysql.lottery;

import cn.gf.saas.framework.mybatis.core.mapper.BaseMapperX;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryUserDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 参与用户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("uemtraining")
public interface LotteryUserMapper extends BaseMapperX<LotteryUserDO> {

    /**
     * 批量插入
     *
     * @param list 数据列表
     * @return 插入成功的数量
     */
    default Boolean insertBatch(List<LotteryUserDO> list) {
        if (list == null || list.isEmpty()) {
            return true;
        }
        return this.insertBatchSomeColumn(list) > 0;
    }

}
