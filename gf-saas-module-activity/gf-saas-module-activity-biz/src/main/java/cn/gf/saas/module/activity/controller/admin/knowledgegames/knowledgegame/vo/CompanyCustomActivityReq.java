package cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;
import java.time.LocalDateTime;

import static cn.gf.saas.framework.common.util.date.DateUtils.*;

@Data
public class CompanyCustomActivityReq {

    /**
     * 主键，自增
     */
    @Schema(description = "主键，自增")
    private Integer id;

    /**
     * 公司 ID
     */
    @Schema(description = "公司 ID")
    private String companyId;

    /**
     * 活动标题
     */
    @Schema(description = "活动标题")
    private String actTitle;

    /**
     * 活动类型
     */
    @Schema(description = "活动类型")
    private Integer actType;

    /**
     * 活动 id
     */
    @Schema(description = "活动 id")
    private String actId;

    /**
     * 活动开始时间
     */
    @Schema(description = "活动开始时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime actBeginTime;

    /**
     * 活动结束时间
     */
    @Schema(description = "活动结束时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime actEndTime;

    /**
     * 活动有效期
     */
    @Schema(description = "活动有效期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime actValidTime;

    /**
     * 活动参与类型
     */
    @Schema(description = "活动参与类型")
    private Integer actJoinType;

    /**
     * 活动横幅图片
     */
    @Schema(description = "活动横幅图片")
    private String banner;

    /**
     * 是否发布，默认未发布
     */
    @Schema(description = "是否发布，默认未发布")
    private Boolean published;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createdTime;

    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String createdUser;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime modifiedTime;

    /**
     * 修改用户
     */
    @Schema(description = "修改用户")
    private String modifiedUser;

    /**
     * 用于标识是否启用（该字段在数据库中不存在）
     */
    @Schema(description = "用于标识是否启用（该字段在数据库中不存在）")
    private Boolean enabled;
}