package cn.gf.saas.module.activity.controller.admin.lottery;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryRespVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotterySaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryDO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryExtendsDO;
import cn.gf.saas.module.activity.dal.mysql.lottery.LotteryExtendsMapper;
import cn.gf.saas.module.activity.service.lottery.LotteryService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 幸运抽奖")
@RestController
@RequestMapping("/activity/lottery")
@Validated
@Slf4j
public class LotteryController {

    @Resource
    private LotteryService lotteryService;

    @Resource
    private LotteryExtendsMapper lotteryExtendsMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @PostMapping("/create")
    @Operation(summary = "创建幸运抽奖")
    public CommonResult<Integer> createLottery(@Valid @RequestBody LotterySaveReqVO createReqVO) {
        return success(lotteryService.createLottery(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新幸运抽奖")
    public CommonResult<Boolean> updateLottery(@Valid @RequestBody LotterySaveReqVO updateReqVO) {
        lotteryService.updateLottery(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得幸运抽奖")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<LotteryRespVO> getLottery(@RequestParam("id") Integer id) {
        LotteryDO lottery = lotteryService.getLottery(id);
        if (lottery == null) {
            return success(null);
        }

        // 获取扩展信息
        LotteryExtendsDO lotteryExtends = lotteryExtendsMapper.selectById(id);

        // 转换为响应VO
        LotteryRespVO respVO = BeanUtils.toBean(lottery, LotteryRespVO.class);
        
        // 设置扩展字段
        if (lotteryExtends != null) {
            respVO.setPreUserCount(lotteryExtends.getPreUserCount());
            respVO.setLotteryOfDays(lotteryExtends.getLotteryOfDays());
            respVO.setLotteryOfTotal(lotteryExtends.getLotteryOfTotal());
        }

        // 解析JSON配置获取receiveMsg
        if (lottery.getSettingConfig() != null) {
            try {
                Map<String, Object> config = objectMapper.readValue(lottery.getSettingConfig(), Map.class);
                respVO.setReceiveMsg((String) config.get("receiveMsg"));
            } catch (JsonProcessingException e) {
                log.error("解析JSON配置失败", e);
                respVO.setReceiveMsg("恭喜你,成功领取奖品！");
            }
        }

        return success(respVO);
    }
}
