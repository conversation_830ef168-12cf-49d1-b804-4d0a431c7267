package cn.gf.saas.module.activity.service.lottery;

import cn.gf.saas.module.activity.controller.admin.lottery.vo.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 奖项 Service 接口
 *
 * <AUTHOR>
 */
public interface LotteryPrizeService {

    /**
     * 创建奖项
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createPrize(@Valid LotteryPrizeSaveReqVO createReqVO);

    /**
     * 更新奖项
     *
     * @param updateReqVO 更新信息
     */
    void updatePrize(@Valid LotteryPrizeSaveReqVO updateReqVO);

    /**
     * 获得剩余占比
     *
     * @param lotteryId 活动ID
     * @return 剩余占比
     */
    BigDecimal getRemainingRatio(Integer lotteryId);

    /**
     * 获得奖项列表
     *
     * @param lotteryId 活动ID
     * @return 奖项列表
     */
    List<LotteryPrizeRespVO> getPrizeList(Integer lotteryId);

    /**
     * 获得奖项详情
     *
     * @param id 奖项ID
     * @return 奖项详情
     */
    LotteryPrizeDetailRespVO getPrizeDetail(Integer id);

    /**
     * 批量删除奖项
     *
     * @param ids 奖项ID列表
     */
    void deletePrizes(List<Integer> ids);

}
