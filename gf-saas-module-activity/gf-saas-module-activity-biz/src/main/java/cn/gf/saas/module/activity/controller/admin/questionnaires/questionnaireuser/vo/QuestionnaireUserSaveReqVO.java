package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireuser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 调查问卷选择人员新增/修改 Request VO")
@Data
public class QuestionnaireUserSaveReqVO {

    @Schema(description = "调查问卷活动ID", example = "1164")
    private Integer questionnaireId;

    @Schema(description = "参与人员IDs", example = "6469")
    private List<String> userIds;

}