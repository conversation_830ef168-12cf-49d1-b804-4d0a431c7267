package cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledge.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 题库 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UemKnowledgeRespVO {

    @Schema(description = "题库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20207")
    @ExcelProperty("题库ID")
    private Integer knowledgeId;

    @Schema(description = "标题")
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "单位id", example = "23077")
    @ExcelProperty("单位id")
    private String companyId;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String createUser;

}