package cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledgequestion.vo;

import cn.gf.saas.module.activity.enums.QuestionTypeEnum;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 题库题目 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UemKnowledgeQuestionRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31860")
    @ExcelProperty("id")
    private Integer recordId;

    @Schema(description = "题库id", example = "32654")
    @ExcelProperty("题库id")
    private Integer knowledgeId;

    @Schema(description = "题目序号")
    @ExcelProperty("题目序号")
    private Integer questionIndex;

    @Schema(description = "题目类型 多选/单选/判断题", example = "2")
    @ExcelProperty("题目类型 多选/单选/判断题")
    private QuestionTypeEnum questionType;

    @Schema(description = "题目")
    @ExcelProperty("题目")
    private String question;

    @Schema(description = "结果解析")
    @ExcelProperty("结果解析")
    private String resultExplain;

    @Schema(description = "answer1")
    @ExcelProperty("answer1")
    private String answer1;

    @Schema(description = "answer2")
    @ExcelProperty("answer2")
    private String answer2;

    @Schema(description = "结果3")
    @ExcelProperty("结果3")
    private String answer3;

    @Schema(description = "结果4")
    @ExcelProperty("结果4")
    private String answer4;

    @Schema(description = "结果5")
    @ExcelProperty("结果5")
    private String answer5;

    @Schema(description = "结果6")
    @ExcelProperty("结果6")
    private String answer6;

    @Schema(description = "结果7")
    @ExcelProperty("结果7")
    private String answer7;

    @Schema(description = "结果8")
    @ExcelProperty("结果8")
    private String answer8;

    @Schema(description = "结果9")
    @ExcelProperty("结果9")
    private String answer9;

    @Schema(description = "结果10")
    @ExcelProperty("结果10")
    private String answer10;

    @Schema(description = "答案")
    @ExcelProperty("答案")
    private String result;

    @Schema(description = "题目编号")
    @ExcelProperty("题目编号")
    private Long questionNumber;

    @Schema(description = "题目标题")
    @ExcelProperty("题目标题")
    private String title;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("选项类型 0=文字，1=图片，2=音频，3=视频")
    private Short answer1type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("选项类型 0=文字，1=图片，2=音频，3=视频")
    private Short answer2type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("选项类型 0=文字，1=图片，2=音频，3=视频")
    private Short answer3type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("选项类型 0=文字，1=图片，2=音频，3=视频")
    private Short answer4type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("选项类型 0=文字，1=图片，2=音频，3=视频")
    private Short answer5type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("选项类型 0=文字，1=图片，2=音频，3=视频")
    private Short answer6type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("选项类型 0=文字，1=图片，2=音频，3=视频")
    private Short answer7type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("选项类型 0=文字，1=图片，2=音频，3=视频")
    private Short answer8type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("选项类型 0=文字，1=图片，2=音频，3=视频")
    private Short answer9type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("选项类型 0=文字，1=图片，2=音频，3=视频")
    private Short answer10type;

    @Schema(description = "分值")
    @ExcelProperty("分值")
    private Integer score;

    @Schema(description = "原答案")
    @ExcelProperty("原答案")
    private String oldAnswer;

}