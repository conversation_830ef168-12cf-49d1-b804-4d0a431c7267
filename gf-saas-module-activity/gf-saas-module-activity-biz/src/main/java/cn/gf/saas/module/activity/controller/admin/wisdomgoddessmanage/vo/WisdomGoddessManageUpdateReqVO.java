package cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.gf.saas.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Data
public class WisdomGoddessManageUpdateReqVO {

    @Schema(description = "主键")
    private Integer id;

    /**
     * 活动名称
     */
    @Schema(description = "活动名称")
    @NotBlank(message = "活动名称不能为空")
    @Size(max = 50, message = "活动名称长度不大于50个字")
    private String actName;

    /**
     * 主题类型Id
     */
    @Schema(description = "主题类型Id")
    private Integer actType;

    /**
     * 活动开始时间
     */
    @Schema(description = "活动开始时间")
    @NotNull(message = "活动开始时间不能为空")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @Schema(description = "活动结束时间")
    @NotNull(message = "活动结束时间不能为空")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime endTime;

    /**
     * 参与类型，0集团人员 1全部人员，2参与部门，3选择人员
     */
    @Schema(description = "参与类型，0集团人员 1全部人员，2参与部门，3选择人员")
    private Integer joinType;

    /**
     * 每道题可重复答题次数
     */
    @Schema(description = "每道题可重复答题次数")
    private Integer answerCount;

    /**
     * 字段显示隐藏配置json:{"OrganizerIsShow":true,"AnswerCount":true,"IndexMyPrizeBtn":true,"IndexRankBtn":true,"IndexPrizeBtn":true,"IndexIntroTab":true,"IndexGameRuleTab":true}
     */
    @Schema(description = "字段显示隐藏配置json")
    private ConfigInfo fieldHiddenConfig;

    /**
     * 活动背景
     */
    @Schema(description = "活动背景")
    private String intro;

    /**
     * 游戏获奖规则
     */
    @Schema(description = "游戏获奖规则")
    private String gameRuleDesc;

    /**
     * 总榜单显示条数
     */
    @Schema(description = "总榜单显示条数")
    private Integer rankShowNum;

    /**
     * 部门榜单显示条数
     */
    @Schema(description = "部门榜单显示条数")
    private Integer departRankShowNum;

    /**
     * 单位id
     */
    @Schema(description = "单位id")
    private String companyId;

    /**
     * 视频封面图片
     */
    @Schema(description = "视频封面图片")
    private String videoCoverPhoto;

    /**
     * 开始答题头部图
     */
    @Schema(description = "开始答题头部图")
    private String startAnswerPhoto;

    /**
     * 取值方式
     */
    @Schema(description = "取值方式")
    @NotNull(message = "取值方式不能为空")
    private Integer getValueType;

    /**
     * 每题分数
     */
    @Schema(description = "每题分数")
    private Integer oneScore;

    @Schema(description = "轮播图")
    private List<String> imageUrls;

}
