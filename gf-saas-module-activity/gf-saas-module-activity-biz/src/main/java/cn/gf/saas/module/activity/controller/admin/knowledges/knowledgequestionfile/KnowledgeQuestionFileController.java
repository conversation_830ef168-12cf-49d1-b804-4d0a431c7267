package cn.gf.saas.module.activity.controller.admin.knowledges.knowledgequestionfile;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.gf.saas.module.activity.controller.admin.knowledges.knowledgequestionfile.vo.KnowledgeQuestionFileRespVO;
import cn.gf.saas.module.activity.controller.admin.knowledges.knowledgequestionfile.vo.KnowledgeQuestionFileSaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.knowledgequestionfile.KnowledgeQuestionFileDO;
import cn.gf.saas.module.activity.service.knowledgequestionfile.KnowledgeQuestionFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 题目文件")
@RestController
@RequestMapping("/activity/knowledge-question-file")
@Validated
public class KnowledgeQuestionFileController {

    @Resource
    private KnowledgeQuestionFileService knowledgeQuestionFileService;

    @PostMapping("/create")
    @Operation(summary = "创建题目文件")
    //@PreAuthorize("@ss.hasPermission('activity:knowledge-question-file:create')")
    public CommonResult<Integer> createKnowledgeQuestionFile(@Valid @RequestBody KnowledgeQuestionFileSaveReqVO createReqVO) {
        return success(knowledgeQuestionFileService.createKnowledgeQuestionFile(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新题目文件")
    //@PreAuthorize("@ss.hasPermission('activity:knowledge-question-file:update')")
    public CommonResult<Boolean> updateKnowledgeQuestionFile(@Valid @RequestBody KnowledgeQuestionFileSaveReqVO updateReqVO) {
        knowledgeQuestionFileService.updateKnowledgeQuestionFile(updateReqVO);
        return success(true);
    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除题目文件")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('activity:knowledge-question-file:delete')")
//    public CommonResult<Boolean> deleteKnowledgeQuestionFile(@RequestParam("id") Integer id) {
//        knowledgeQuestionFileService.deleteKnowledgeQuestionFile(id);
//        return success(true);
//    }
//
    @GetMapping("/get")
    @Operation(summary = "获得题目文件")
    @Parameter(name = "knowledgeId", description = "活动ID", required = true, example = "1024")
    @Parameter(name = "questionIndex", description = "题目序号", required = true, example = "1")
//    @PreAuthorize("@ss.hasPermission('activity:knowledge-question-file:query')")
    public CommonResult<KnowledgeQuestionFileRespVO> getKnowledgeQuestionFile(@RequestParam("knowledgeId") Integer knowledgeId, @RequestParam("questionIndex") Integer questionIndex) {
        KnowledgeQuestionFileDO knowledgeQuestionFile = knowledgeQuestionFileService.getOne(
                new LambdaQueryWrapperX<KnowledgeQuestionFileDO>()
                        .eq(KnowledgeQuestionFileDO::getKnowledgeId,knowledgeId)
                        .eq(KnowledgeQuestionFileDO::getQuestionIndex,questionIndex).last("limit 1"));
        return success(BeanUtils.toBean(knowledgeQuestionFile, KnowledgeQuestionFileRespVO.class));
    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得题目文件分页")
//    @PreAuthorize("@ss.hasPermission('activity:knowledge-question-file:query')")
//    public CommonResult<PageResult<KnowledgeQuestionFileRespVO>> getKnowledgeQuestionFilePage(@Valid KnowledgeQuestionFilePageReqVO pageReqVO) {
//        PageResult<KnowledgeQuestionFileDO> pageResult = knowledgeQuestionFileService.getKnowledgeQuestionFilePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, KnowledgeQuestionFileRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出题目文件 Excel")
//    @PreAuthorize("@ss.hasPermission('activity:knowledge-question-file:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportKnowledgeQuestionFileExcel(@Valid KnowledgeQuestionFilePageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<KnowledgeQuestionFileDO> list = knowledgeQuestionFileService.getKnowledgeQuestionFilePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "题目文件.xls", "数据", KnowledgeQuestionFileRespVO.class,
//                        BeanUtils.toBean(list, KnowledgeQuestionFileRespVO.class));
//    }

}