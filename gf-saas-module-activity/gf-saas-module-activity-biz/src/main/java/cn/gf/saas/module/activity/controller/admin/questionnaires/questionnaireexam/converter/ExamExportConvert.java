package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireexam.converter;

import cn.gf.saas.framework.common.util.json.JsonUtils;
import cn.gf.saas.module.activity.dal.dataobject.questionnaire.QuestionnaireExamResultModel;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.var;

import java.util.List;

/**
 * Excel Json 转换器
 *
 * <AUTHOR>
 */
public class ExamExportConvert implements Converter<List<QuestionnaireExamResultModel>> {

    @Override
    public Class<?> supportJavaTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public WriteCellData<List<Object>> convertToExcelData(List<QuestionnaireExamResultModel> value, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) {
        String str = "";
        for (int i = 0; i < value.stream().count(); i++) {
            str+=("第"+(i+1)+"题:")+ (value.get(i).getAnswer().isEmpty()?"无":value.get(i).getAnswer());
            if(i!=value.stream().count()-1)str+="\n";
        }
        // 生成 Excel 小表格
        return new WriteCellData<>(str);
    }

}
