package cn.gf.saas.module.activity.controller.admin.staffcollege.vo.video;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 职工学院视频响应 VO
 */
@Data
public class StaffCollegeVideoRespVO {
    @Schema(description = "记录编号")
    private Integer videoId;

    @Schema(description = "活动编号")
    private Integer courseId;

    @Schema(description = "视频名称")
    private String title;

    @Schema(description = "视频地址url")
    private String vid;

    @Schema(description = "视频封面图")
    private String coverImg;

    @Schema(description = "教程介绍")
    private String remark;

    @Schema(description = "视频时长")
    private Integer videoTotalTime;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "过期时间")
    private LocalDateTime endTime;

    @Schema(description = "是否用第一帧做封面")
    private Boolean videoFirstFrame;

    @Schema(description = "是否能拖转")
    private Boolean noDrag;

    @Schema(description = "是否能倍数播放")
    private Boolean isMultiple;
} 