package cn.gf.saas.module.activity.controller.admin.poetrytalent.vo;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@Schema(description = "创建诗词秀才关卡请求对象")
public class PoetryTalentLevelCreateReqVO {

    @Schema(description = "关卡名称")
    @Size(max = 6, message = "关卡名称长度不能超过6")
    @NotBlank(message = "关卡名称不能为空")
    private String name;

    @Schema(description = "活动ID")
    @NotNull(message = "活动ID不能为空")
    private Integer activityId;

    @Schema(description = "关卡时间（秒）")
    @NotNull(message = "关卡时间不能为空")
    private Integer limitTime;

    @Schema(description = "诗词ID")
    private String poetryId;

    @Schema(description = "诗词ID列表")
    private String poetryIdList;

    @Schema(description = "子诗词列表")
    private List<SubPoetry> subPoetry;

    @Schema(description = "随机字数量")
    private Integer randomLetterCount;

    @Schema(description = "每关重试次数")
    @NotNull(message = "每关重试次数不能为空")
    private Integer restartTimes;

    @Schema(description = "答案模型")
    private List<List<Object>> answerModels;

    @Schema(description = "填字设置:  随机 RANDOM，自定义 CUSTOMIZE")
    private String settingType;

    @Schema(description = "排序")
    @NotNull(message = "排序不能为空")
    private Integer sort;


    @Data
    @Schema(description = "子诗词对象")
    public static class SubPoetry {

        @Schema(description = "ID")
        private Integer id;

        @Schema(description = "创建时间")
        private String createTime;

        @Schema(description = "修改时间")
        private String modifyTime;

        @Schema(description = "名称")
        private String name;

        @Schema(description = "作者")
        private String author;

        @Schema(description = "朝代")
        private String dynasty;

        @Schema(description = "类型ID")
        private Integer typeId;

        @Schema(description = "类型名称")
        private String typeName;

        @Schema(description = "规则")
        private String rule;

        @Schema(description = "等级")
        private Integer level;

        @Schema(description = "内容")
        private String content;

        @Schema(description = "索引")
        private Integer idx;
    }
}

