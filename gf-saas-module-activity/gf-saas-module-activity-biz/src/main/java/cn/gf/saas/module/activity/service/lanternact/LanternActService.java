package cn.gf.saas.module.activity.service.lanternact;

import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.activity.controller.admin.lanternact.vo.*;

import javax.validation.Valid;

/**
 * 猜灯谜活动服务接口
 */
public interface LanternActService {

    /**
     * 创建猜灯谜活动
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer create(@Valid LanternActCreateReqVO createReqVO);

    /**
     * 更新猜灯谜活动
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid LanternActUpdateReqVO updateReqVO);

    /**
     * 获得猜灯谜活动
     *
     * @param id 编号
     * @return 猜灯谜活动
     */
    LanternActRespVO get(Integer id);

    /**
     * 获得猜灯谜活动分页
     *
     * @param pageReqVO 分页查询
     * @return 猜灯谜活动分页
     */
    PageResult<LanternActRespVO> getPage(LanternActPageReqVO pageReqVO);

}
