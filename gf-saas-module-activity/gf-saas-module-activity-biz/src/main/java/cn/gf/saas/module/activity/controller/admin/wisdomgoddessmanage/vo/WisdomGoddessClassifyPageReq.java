package cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import cn.gf.saas.module.activity.dal.dataobject.activity.RandomSettingModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * wisdomgoddessclassify 表的请求实体类
 */
@Data
public class WisdomGoddessClassifyPageReq extends PageParam {

    /**
     * 关联智趣女神活动ID
     */
    @Schema(description = "关联智趣答题活动ID")
    @NotNull(message = "智趣答题ID不能为空")
    private Integer actID;
}    