package cn.gf.saas.module.activity.controller.admin.staffcollege;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.activity.controller.admin.staffcollege.vo.*;
import cn.gf.saas.module.activity.service.staffcollege.StaffCollegeUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 职工学院参与用户")
@RestController
@RequestMapping("/activity/staffcollege/user")
@Validated
public class StaffCollegeUserController {
    @Resource
    private StaffCollegeUserService staffCollegeUserService;

    @PostMapping("/add")
    @Operation(summary = "职工学院-参与用户-批量添加")
    public CommonResult<Boolean> add(@Valid @RequestBody StaffCollegeUserAddReqVO createReqVO) {
        return success(staffCollegeUserService.add(createReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "职工学院-参与用户-批量删除")
    public CommonResult<Boolean> delete(@RequestBody List<String> userIds, @RequestParam("courseId") Integer courseId) {
        return success(staffCollegeUserService.delete(userIds, courseId));
    }

    @GetMapping("/page")
    @Operation(summary = "职工学院-参与用户-分页")
    public CommonResult<PageResult<StaffCollegeUserRespVO>> page(@Valid StaffCollegeUserPageReqVO pageReqVO) {
        return success(staffCollegeUserService.getUserPage(pageReqVO));
    }
} 