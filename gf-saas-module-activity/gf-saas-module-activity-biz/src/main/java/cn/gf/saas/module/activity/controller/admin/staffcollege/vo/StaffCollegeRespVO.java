package cn.gf.saas.module.activity.controller.admin.staffcollege.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 职工学院课程响应 VO
 */
@Data
public class StaffCollegeRespVO {
    @Schema(description = "活动编号")
    private Integer courseId;

    @Schema(description = "活动名称")
    private String courseName;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "参与方式：0集团人员，1公司人员，3指定人员, 6不限人员")
    private Integer joinType;

    @Schema(description = "封面")
    private List<String> banners;

    @Schema(description = "截至是否显示")
    private Boolean showByEndTime;

    @Schema(description = "是否显示排行榜")
    private Boolean showRank;

    @Schema(description = "游戏规则")
    private String rule;

    @Schema(description = "课程详情")
    private String remark;

    @Schema(description = "是否显示规则")
    private Boolean showRule;

    @Schema(description = "是否显示观看总进度 1：显示 0：隐藏")
    private Boolean showProgressBar;

    @Schema(description = "文案配置-教程简介")
    private String courseIntroTitle;
} 