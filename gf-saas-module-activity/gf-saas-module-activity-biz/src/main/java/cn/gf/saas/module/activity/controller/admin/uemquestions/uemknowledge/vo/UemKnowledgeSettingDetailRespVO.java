package cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledge.vo;

import cn.gf.saas.module.activity.enums.QuestionTypeEnum;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 题库 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UemKnowledgeSettingDetailRespVO {

    // 题库Id
    private Integer knowledgeId;

    // 题目类型
    private QuestionTypeEnum questionType;

    // 设置的题目数量
    private Integer questionTotal;

    // 题库题目数量
    private Integer questionCount;

    // 分值
    private Integer questionScore;

    // 排序
    private Integer sort;


}