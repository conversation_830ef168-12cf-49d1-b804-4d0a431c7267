package cn.gf.saas.module.activity.controller.admin.readingact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ReadingActBookDeleteReq {

    @Schema(description = "actId")
    @NotNull(message = "actId不能为空")
    private Integer actId;

    @Schema(description = "图书bookIds")
    @NotNull(message = "bookIds不能为空")
    private List<Long> bookIds;
}
