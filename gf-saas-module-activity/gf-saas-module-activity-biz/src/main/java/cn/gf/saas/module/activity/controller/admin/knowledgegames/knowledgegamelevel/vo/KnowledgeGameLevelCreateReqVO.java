package cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegamelevel.vo;

import cn.gf.saas.module.activity.dal.dataobject.activity.RandomSettingModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.sql.Timestamp;
import java.util.List;

@Schema(description = "管理后台 - 答题闯关-关卡新增 Request VO")
@Data
public class KnowledgeGameLevelCreateReqVO {

    @Schema(description = "活动id")
    @NotNull(message = "活动id不能为空")
    private Integer gameId;

    @Schema(description = "标题")
    @NotBlank(message = "标题不能为空")
    @Size(max = 50, message = "标题长度不大于50个字")
    private String title;

    @Schema(description = "过关分数")
    @NotNull(message = "过关分数不能为空")
    private Integer passScore;

    @Schema(description = "每题时间（秒）")
    @NotNull(message = "每题时间不能为空")
    private Integer questionTime;

    @Schema(description = "重做次数")
    @NotNull(message = "重做次数不能为空")
    private Integer redoTime;

    @Schema(description = "是否是随机题")
    private Boolean isRandom;

    @Schema(description = "题库Ids")
    private List<Integer> libknowledgeIds;

    @Schema(description = "随机题设置")
    private List<RandomSettingModel> randomSetting;

}
