package cn.gf.saas.module.activity.controller.admin.readingact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * readingact 表的请求实体类
 */
@Data
public class ReadingActReq {
    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    private Integer id;

    /**
     * 单位ID
     */
    @Schema(description = "单位ID")
    private String companyId;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 标题图片
     */
    @Schema(description = "标题图片")
    private String titleImg;

    /**
     * 活动内容
     */
    @Schema(description = "活动内容")
    private String content;

    /**
     * 参与类型，0集团人员 1全部人员，2参与部门，3选择人员，4自主报名
     */
    @Schema(description = "参与类型，0集团人员 1全部人员，2参与部门，3选择人员，4自主报名")
    private Integer joinType;

    /**
     * 所在一级部门
     */
    @Schema(description = "所在一级部门")
    private String departLevel1;

    /**
     * 是否自动签到
     */
    @Schema(description = "是否自动签到")
    private Boolean isAutoCheckIn;

    /**
     * 活动开始时间
     */
    @Schema(description = "活动开始时间")
    private LocalDateTime beginTime;

    /**
     * 活动结束时间
     */
    @Schema(description = "活动结束时间")
    private LocalDateTime endTime;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    /**
     * 发布人
     */
    @Schema(description = "发布人")
    private String publishUserId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String craeteUserId;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Boolean deleted;

    /**
     * 活动开始前N天短信提醒:格式 ：1,2,3
     */
    @Schema(description = "活动开始前N天短信提醒:格式 ：1,2,3")
    private String smsAlterNotice;

    /**
     * 活动提醒短信模板编号：SMS_196618968
     */
    @Schema(description = "活动提醒短信模板编号：SMS_196618968")
    private String smsAlterModelCode;

    /**
     * 连续N天未参与活动短信提醒,格式 ：1,2,3
     */
    @Schema(description = "连续N天未参与活动短信提醒,格式 ：1,2,3")
    private String smsRemindNotice;

    /**
     * 积分榜用户统计排名显示
     */
    @Schema(description = "积分榜用户统计排名显示")
    private Boolean statisticpointsUserEnabled;

    /**
     * 积分榜单位统计排名显示
     */
    @Schema(description = "积分榜单位统计排名显示")
    private Boolean statisticpointsCompanyEnabled;

    /**
     * 读书榜用户排名显示
     */
    @Schema(description = "读书榜用户排名显示")
    private Boolean statisticreadUserEnabled;

    /**
     * 读书榜单位排名显示
     */
    @Schema(description = "读书榜单位排名显示")
    private Boolean statisticreadCompanyEnabled;

    /**
     * 节点统计类型，阅读时长 = 1 / 签到积分 = 2
     */
    @Schema(description = "节点统计类型，阅读时长 = 1 / 签到积分 = 2")
    private Integer checkpointType;

    /**
     * 活动海报
     */
    @Schema(description = "活动海报")
    private String activityPoster;

    /**
     * 开启用户积分类型（对应readingactIntegrate表type字段值）例: "1,2"表示即开启积分也开启小红花功能,为空表示不启用积分
     */
    @Schema(description = "开启用户积分类型（对应readingactIntegrate表type字段值）例: \"1,2\"表示即开启积分也开启小红花功能,为空表示不启用积分")
    private String integrateType;

    /**
     * 是否弹出电子徽章：1是 0否
     */
    @Schema(description = "是否弹出电子徽章：1是 0否")
    private Boolean isPopupMedal;

    /**
     * 弹出电子徽章条件:{"TotalReadDuration":"累计阅读时长：单位分钟","TotalSignInDays":"累计签到天数：单位天","ContinuitySignInDays":"连续签到天数：单位天"}
     */
    @Schema(description = "弹出电子徽章条件:{\"TotalReadDuration\":\"累计阅读时长：单位分钟\",\"TotalSignInDays\":\"累计签到天数：单位天\",\"ContinuitySignInDays\":\"连续签到天数：单位天\"}")
    private String popupMedalCondition;

    /**
     * 图书分类是否显示：0不显示 1显示。默认0
     */
    @Schema(description = "图书分类是否显示：0不显示 1显示。默认0")
    private Boolean categoryIsShow;

    /**
     * 阅读记录是否显示：0不显示 1显示。默认1
     */
    @Schema(description = "阅读记录是否显示：0不显示 1显示。默认1")
    private Boolean readHistoryEnabled;

    /**
     * 多个配置集合json字段(IsShowBookSquare是否显示书香广场入口,IsShowShare是否显示分享按钮,IsBookSquareDesensitize书香广场姓名是否脱敏)
     */
    @Schema(description = "多个配置集合json字段(IsShowBookSquare是否显示书香广场入口,IsShowShare是否显示分享按钮,IsBookSquareDesensitize书香广场姓名是否脱敏)")
    private ConfigInfo fieldConfig;
}    