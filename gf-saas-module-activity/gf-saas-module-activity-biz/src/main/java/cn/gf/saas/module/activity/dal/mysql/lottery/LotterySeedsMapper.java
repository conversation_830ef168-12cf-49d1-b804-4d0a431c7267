package cn.gf.saas.module.activity.dal.mysql.lottery;

import cn.gf.saas.framework.mybatis.core.mapper.BaseMapperX;
import cn.gf.saas.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotterySeedsDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 抽奖种子 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("uemtraining")
public interface LotterySeedsMapper extends BaseMapperX<LotterySeedsDO> {

    /**
     * 根据活动ID查询种子列表
     *
     * @param lotteryId 活动ID
     * @return 种子列表
     */
    default List<LotterySeedsDO> selectByLotteryId(Integer lotteryId) {
        return selectList(new LambdaQueryWrapperX<LotterySeedsDO>()
                .eq(LotterySeedsDO::getLotteryId, lotteryId));
    }

    /**
     * 根据活动ID和奖项ID删除种子
     *
     * @param lotteryId 活动ID
     * @param prizesId 奖项ID
     */
    default void deleteByLotteryIdAndPrizesId(Integer lotteryId, Integer prizesId) {
        delete(new LambdaQueryWrapperX<LotterySeedsDO>()
                .eq(LotterySeedsDO::getLotteryId, lotteryId)
                .eq(LotterySeedsDO::getPrizesId, prizesId));
    }

    /**
     * 根据活动ID删除所有种子
     *
     * @param lotteryId 活动ID
     */
    default void deleteByLotteryId(Integer lotteryId) {
        delete(new LambdaQueryWrapperX<LotterySeedsDO>()
                .eq(LotterySeedsDO::getLotteryId, lotteryId));
    }

    /**
     * 批量插入
     *
     * @param list 数据列表
     * @return 插入成功的数量
     */
    default Boolean insertBatch(List<LotterySeedsDO> list) {
        if (list == null || list.isEmpty()) {
            return true;
        }
        return this.insertBatchSomeColumn(list) > 0;
    }
}
