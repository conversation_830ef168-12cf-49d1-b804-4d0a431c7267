package cn.gf.saas.module.activity.controller.admin.poetrytalent;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.activity.controller.admin.poetrytalent.vo.*;
import cn.gf.saas.module.activity.service.poetrytalent.PoetryTalentLevelService;
import cn.gf.saas.module.activity.service.poetrytalent.PoetryTalentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 诗词秀才-关卡")
@RestController
@RequestMapping("/activity/poetry-talent-level")
@Validated
public class PoetryTalentLevelController {

    @Resource
    private PoetryTalentLevelService poetryTalentLevelService;

    @PostMapping("/create")
    @Operation(summary = "创建关卡")
    public CommonResult create(@Valid @RequestBody PoetryTalentLevelCreateReqVO createReqVO) {
        poetryTalentLevelService.save(createReqVO);
        return success();
    }

    @PutMapping("/update")
    @Operation(summary = "更新关卡")
    public CommonResult update(@Valid @RequestBody PoetryTalentLevelUpdateReqVO createReqVO) {
        poetryTalentLevelService.update(createReqVO);
        return success();
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除关卡")
    public CommonResult delete(@Valid  @RequestBody PoetryTalentLevelDeleteReqVO reqVO) {
        poetryTalentLevelService.delete(reqVO);
        return success();
    }

    @GetMapping("/page")
    @Operation(summary = "获取关卡分页")
    public CommonResult<PageResult<PoetryTalentLevelInfoRespVO>> page(@Valid PoetryTalentLevelPageReqVO pageReqVO) {
        return success(poetryTalentLevelService.getPage(pageReqVO));
    }
}
