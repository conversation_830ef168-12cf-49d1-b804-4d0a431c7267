package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireexam.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 调查问卷答题新增/修改 Request VO")
@Data
public class QuestionnaireExamSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4130")
    private Integer recordId;

    @Schema(description = "题目id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6758")
    @NotNull(message = "题目id不能为空")
    private Integer questionnaireId;

    @Schema(description = "答题人", example = "17982")
    private String userId;

    @Schema(description = "答案")
    private String result;

    @Schema(description = "答题时间")
    private LocalDateTime createdTime;

}