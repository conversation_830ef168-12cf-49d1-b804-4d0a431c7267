package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireuser.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 调查问卷选择人员 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuestionnaireUserRespVO {

    @Schema(description = "参与人员ID", example = "6469")
    @ExcelProperty("参与人员ID")
    private String userId;

    @Schema(description = "姓名", example = "吴清焕")
    private String name;

    @Schema(description = "手机号码", example = "13276711014")
    private String phone;

    @Schema(description = "组织", example = "研发一部")
    private String departLevel;

}