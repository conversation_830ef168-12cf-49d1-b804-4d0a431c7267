package cn.gf.saas.module.activity.controller.admin.staffcollege.vo.video;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 职工学院视频批量删除请求 VO
 */
@Data
public class StaffCollegeVideoDeleteReqVO {
    @Schema(description = "视频ID列表")
    @NotEmpty(message = "视频ID列表不能为空")
    private List<Integer> videoIds;
} 