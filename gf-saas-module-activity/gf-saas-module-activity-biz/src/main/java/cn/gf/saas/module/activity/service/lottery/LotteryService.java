package cn.gf.saas.module.activity.service.lottery;

import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotterySaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryDO;

import javax.validation.Valid;

/**
 * 幸运抽奖 Service 接口
 *
 * <AUTHOR>
 */
public interface LotteryService {

    /**
     * 创建幸运抽奖
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createLottery(@Valid LotterySaveReqVO createReqVO);

    /**
     * 更新幸运抽奖
     *
     * @param updateReqVO 更新信息
     */
    void updateLottery(@Valid LotterySaveReqVO updateReqVO);

    /**
     * 获得幸运抽奖
     *
     * @param id 编号
     * @return 幸运抽奖
     */
    LotteryDO getLottery(Integer id);

}
