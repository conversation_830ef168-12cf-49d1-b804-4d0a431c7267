package cn.gf.saas.module.activity.controller.admin.lottery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.gf.saas.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "管理后台 - 幸运抽奖新增/修改 Request VO")
@Data
public class LotterySaveReqVO {

    @Schema(description = "活动ID", example = "1024")
    private Integer id;

    @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "活动名称不能为空")
    private String name;

    @Schema(description = "活动开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "活动开始时间不能为空")
    private LocalDateTime beginTime;

    @Schema(description = "活动结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "活动结束时间不能为空")
    private LocalDateTime endTime;

    @Schema(description = "领奖开始时间")
    private LocalDateTime receiveBeginTime;

    @Schema(description = "领奖结束时间")
    private LocalDateTime receiveEndTime;

    @Schema(description = "是否显示中奖金额", example = "true")
    private Boolean isShowAmount = true;

    @Schema(description = "抽奖模式", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "抽奖模式不能为空")
    private Integer lotteryType = 0;

    @Schema(description = "是否自定义抽奖人数", example = "true")
    private Boolean isLiveLottery = true;

    @Schema(description = "领取奖品文案", example = "恭喜你,成功领取奖品！")
    private String receiveMsg;

    @Schema(description = "活动说明")
    private String info;

    @Schema(description = "预抽奖人数")
    private Integer preUserCount;

    @Schema(description = "每人每天可抽奖")
    private Integer lotteryOfDays;

    @Schema(description = "每人共可抽奖")
    private Integer lotteryOfTotal;

    @Schema(description = "未中奖提示")
    private String losingToast;
}
