package cn.gf.saas.module.activity.controller.admin.poetrytalent;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameCreateReqVO;
import cn.gf.saas.module.activity.controller.admin.poetrytalent.vo.PoetryTalentCreateReqVO;
import cn.gf.saas.module.activity.controller.admin.poetrytalent.vo.PoetryTalentInfoRespVO;
import cn.gf.saas.module.activity.controller.admin.poetrytalent.vo.PoetryTalentUpdateReqVO;
import cn.gf.saas.module.activity.service.knowledgegame.game.KnowledgeGameService;
import cn.gf.saas.module.activity.service.poetrytalent.PoetryTalentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 诗词秀才")
@RestController
@RequestMapping("/activity/poetry-talent")
@Validated
public class PoetryTalentController {

    @Resource
    private PoetryTalentService poetryTalentService;

    @PostMapping("/create")
    @Operation(summary = "创建诗词秀才")
    public CommonResult<Integer> create(@Valid @RequestBody PoetryTalentCreateReqVO createReqVO) {
        return success(poetryTalentService.save(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新诗词秀才")
    public CommonResult update(@Valid @RequestBody PoetryTalentUpdateReqVO createReqVO) {
        poetryTalentService.update(createReqVO);
        return success();
    }

    @GetMapping("/get")
    @Operation(summary = "获取诗词秀才")
    public CommonResult<PoetryTalentInfoRespVO> get(Integer id) {
        return success(poetryTalentService.getDetail(id));
    }
}
