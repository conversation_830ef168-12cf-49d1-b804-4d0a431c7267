package cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "弹出配置 请求对象")
public class PopupConfigReq {

    @Schema(description = "是否显示团队排名")
    @JSONField(name = "isShowTeamRank")
    private boolean isShowTeamRank;

    @Schema(description = "是否显示步数主页")
    @JSONField(name = "isShowStepHome")
    private boolean isShowStepHome;

    @Schema(description = "是否隐藏提示")
    @JSONField(name = "isHideTip")
    private boolean isHideTip;

    @Schema(description = "是否隐藏半场得分")
    @JSONField(name = "isHideHalfScore")
    private boolean isHideHalfScore;

    @Schema(description = "是否隐藏标题")
    @JSONField(name = "isHideTitle")
    private boolean isHideTitle;

    @Schema(description = "是否隐藏等级标题")
    @JSONField(name = "isHideLevelTitle")
    private boolean isHideLevelTitle;

    @Schema(description = "是否显示游戏图片")
    @JSONField(name = "isShowPlayImg")
    private boolean isShowPlayImg;

    @Schema(description = "是否显示答题时间")
    @JSONField(name = "isShowAnswerTime")
    private boolean isShowAnswerTime;

    @Schema(description = "是否显示组织排名")
    @JSONField(name = "isShowOrganizeRank")
    private boolean isShowOrganizeRank;

    // 其他非布尔字段保持不变
    @Schema(description = "通过或失败提示信息")
    private String passfailTips;

    @Schema(description = "编辑地址提示信息")
    private String editAddressTip;

    @Schema(description = "通过文本信息")
    private String passText;

    @Schema(description = "再次尝试文本信息")
    private String againText;

    @Schema(description = "结束按钮文本信息")
    private String endButtonText;

    @Schema(description = "三局游戏文本信息")
    private String threeGameText;

    @Schema(description = "通过弹窗图片链接")
    private String passedPopImg;

    @Schema(description = "失败弹窗图片链接")
    private String failedPopImg;

    /**
     * 初始化对象
     */
    public static PopupConfigReq initPopupConfig() {
        return new PopupConfigReq()
                .setShowTeamRank(false)
                .setShowStepHome(false)
                .setHideTip(false)
                .setHideHalfScore(false)
                .setHideTitle(false)
                .setHideLevelTitle(false)
                .setShowPlayImg(false)
                .setShowAnswerTime(false)
                .setShowOrganizeRank(false)
                .setPassfailTips("")
                .setEditAddressTip("")
                .setPassText("")
                .setAgainText("")
                .setEndButtonText("")
                .setThreeGameText("")
                .setPassedPopImg("");
    }
}    