package cn.gf.saas.module.activity.controller.admin.lottery;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryUserPageReqVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryUserRespVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryUserSaveReqVO;
import cn.gf.saas.module.activity.service.lottery.LotteryUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 幸运抽奖选择人员")
@RestController
@RequestMapping("/activity/lottery/user")
@Validated
public class LotteryUserController {

    @Resource
    private LotteryUserService lotteryUserService;

    @PostMapping("/create-batch")
    @Operation(summary = "批量新增选择人员")
    public CommonResult<Boolean> createLotteryUsers(@Valid @RequestBody LotteryUserSaveReqVO createReqVO) {
        return success(lotteryUserService.createLotteryUsers(createReqVO));
    }

    @DeleteMapping("/delete-batch")
    @Operation(summary = "批量删除选择人员")
    public CommonResult<Boolean> deleteLotteryUsers(@RequestBody List<String> userIds, @RequestParam("lotteryId") Integer lotteryId) {
        lotteryUserService.deleteLotteryUsers(userIds, lotteryId);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得当前活动选择人员分页")
    public CommonResult<PageResult<LotteryUserRespVO>> getLotteryUserPage(@Valid LotteryUserPageReqVO pageReqVO) {
        PageResult<LotteryUserRespVO> pageResult = lotteryUserService.getLotteryUserPage(pageReqVO);
        return success(pageResult);
    }
}
