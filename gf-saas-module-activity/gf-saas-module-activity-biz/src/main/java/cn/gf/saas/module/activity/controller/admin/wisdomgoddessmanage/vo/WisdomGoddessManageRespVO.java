package cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class WisdomGoddessManageRespVO {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 活动名称
     */
    @Schema(description = "活动名称")
    private String actName;

    /**
     * 主题类型Id
     */
    @Schema(description = "主题类型Id")
    private Integer actType;

    @Schema(description = "主题类型名称")
    private String actTypeName;

    /**
     * 活动开始时间
     */
    @Schema(description = "活动开始时间")
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @Schema(description = "活动结束时间")
    private LocalDateTime endTime;

    /**
     * 参与类型，0集团人员 1全部人员，2参与部门，3选择人员
     */
    @Schema(description = "参与类型，0集团人员 1全部人员，2参与部门，3选择人员")
    private Short joinType;

    /**
     * 所在一级部门
     */
    @Schema(description = "所在一级部门")
    private String departLevel1;

    /**
     * 主办单位
     */
    @Schema(description = "主办单位")
    private String organizer;

    /**
     * 每道题可重复答题次数
     */
    @Schema(description = "每道题可重复答题次数")
    private Integer answerCount;

    /**
     * 是否体验活动：0否 1是
     */
    @Schema(description = "是否体验活动：0否 1是")
    private Boolean isExperience;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    /**
     * 活动背景
     */
    @Schema(description = "活动背景")
    private String intro;

    /**
     * 游戏获奖规则
     */
    @Schema(description = "游戏获奖规则")
    private String gameRuleDesc;

    /**
     * 奖品使用说明
     */
    @Schema(description = "奖品使用说明")
    private String instruction;

    /**
     * 总榜单显示条数
     */
    @Schema(description = "总榜单显示条数")
    private Integer rankShowNum;

    /**
     * 部门榜单显示条数
     */
    @Schema(description = "部门榜单显示条数")
    private Integer departRankShowNum;

    /**
     * 单位id
     */
    @Schema(description = "单位id")
    private String companyId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 是否删除：0未删除 1已删除
     */
    @Schema(description = "是否删除：0未删除 1已删除")
    private Boolean deleted;

    /**
     * 字段显示隐藏配置json:{"OrganizerIsShow":true,"AnswerCount":true,"IndexMyPrizeBtn":true,"IndexRankBtn":true,"IndexPrizeBtn":true,"IndexIntroTab":true,"IndexGameRuleTab":true}
     */
    @Schema(description = "字段显示隐藏配置json:")
    private ConfigInfo fieldHiddenConfig;

    /**
     * 视频封面图片
     */
    @Schema(description = "视频封面图片")
    private String videoCoverPhoto;

    /**
     * 开始答题金刚区图片
     */
    @Schema(description = "开始答题金刚区图片")
    private String startAnswerPhoto;

    /**
     * 取值方式
     */
    @Schema(description = "取值方式")
    private Integer getValueType;

    /**
     * 每题分数
     */
    @Schema(description = "每题分数")
    private Integer oneScore;

    /**
     * 答题模式，0常规答题 1组合式答题
     */
    @Schema(description = "答题模式，0常规答题 1组合式答题")
    private Short answerType;

    /**
     * 弹窗提示图片
     */
    @Schema(description = "弹窗提示图片")
    private String popupImgUrl;

    /**
     * 弹窗提示语
     */
    @Schema(description = "弹窗提示语")
    private String popupPrompt;

    /**
     * 奖品领取ID
     */
    @Schema(description = "奖品领取ID")
    private Integer prizesCollectId;

    @Schema(description = "轮播图")
    private List<String> imageUrls;
}
