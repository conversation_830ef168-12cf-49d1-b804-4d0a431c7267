package cn.gf.saas.module.activity.controller.admin.prizes.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "奖品领取记录响应对象")
public class PrizesreceivedRespVO {

    @Schema(description = "主键ID", example = "1")
    private Integer id;

    @Schema(description = "奖品ID", example = "101")
    private Integer prizesId;

    @Schema(description = "用户ID", example = "user123")
    private String userId;

    @Schema(description = "当前用户姓名", example = "张三")
    private String realName;

    @Schema(description = "当前用户手机号", example = "13800138000")
    private String mobilePhone;

    @Schema(description = "手机号", example = "13800138000")
    private String userPhone;

    @Schema(description = "用户姓名", example = "张三")
    private String userName;

    @Schema(description = "部门", example = "技术部")
    private String department;

    @Schema(description = "收件信息", example = "收件信息")
    private String address;

    @Schema(description = "用户收货地址", example = "XX省XX市XX区XX街道XX号")
    private String userAddress;

    @Schema(description = "物流联系手机号", example = "13900139000")
    private String expressPhone;

    @Schema(description = "物流公司", example = "顺丰速运")
    private String expressCompany;

    @Schema(description = "物流单号", example = "SF1234567890")
    private String expressOrder;

    @Schema(description = "领取创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "订单号", example = "ORDER20230528001")
    private String orderNo;

    @Schema(description = "奖品项目编号", example = "55")
    private Integer collectId;

    @Schema(description = "员工编号", example = "EMP001")
    private String personNo;

    @Schema(description = "员工账号", example = "zhangsan_emp")
    private String personAcount;

    @Schema(description = "员工部门", example = "技术部")
    private String personDepartment;

    @Schema(description = "核销时间")
    private LocalDateTime verifyTime;

    @Schema(description = "是否已核销 (0:未核销,1:已核销)", example = "false")
    private Boolean isVerify;

    @Schema(description = "核销码", example = "user123_abc123xyz")
    private String verifyCode;

    @Schema(description = "核销操作人用户ID", example = "admin01")
    private String verifyUserId;

    @Schema(description = "地区控件合并地址字段", example = "广东省深圳市南山区")
    private String controlAddress;

    @Schema(description = "地区控件合并地址字段IDs", example = "440000,440300,440305")
    private String controlAddressIds;
}