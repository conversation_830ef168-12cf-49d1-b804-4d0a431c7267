package cn.gf.saas.module.activity.controller.admin.readingact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * readingactlibrary 表的响应实体类
 */
@Data
public class ReadingActBookResp {
    /**
     * 图书编号
     */
    @Schema(description = "图书编号")
    private Integer bookId;

    /**
     * shId
     */
    @Schema(description = "shId")
    private String shId;

    /**
     * 资产编号
     */
    @Schema(description = "资产编号")
    private Long assetsId;

    /**
     * 图书名称
     */
    @Schema(description = "图书名称")
    private String name;

    @Schema(description = "排序")
    private Integer sort;

    /**
     * 图书封面
     */
    @Schema(description = "图书封面")
    private String coverImg;

    /**
     * 默认类型
     */
    @Schema(description = "默认类型")
    private String defaultType;

    /**
     * 出版社
     */
    @Schema(description = "出版社")
    private String publisher;

    /**
     * 默认类型值
     */
    @Schema(description = "默认类型值")
    private Integer defaultTypeValue;

    /**
     * 阅读链接
     */
    @Schema(description = "阅读链接")
    private String readingUrl;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 最后同步时间
     */
    @Schema(description = "最后同步时间")
    private LocalDateTime lastSyncTime;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Boolean deleted;

    /**
     * 作者
     */
    @Schema(description = "作者")
    private String author;

    /**
     * 类型：0图书 1听书
     */
    @Schema(description = "类型：0图书 1听书")
    private Integer type;

    /**
     * 简介
     */
    @Schema(description = "简介")
    private String intro;

    /**
     * 总页数/总集数
     */
    @Schema(description = "总页数/总集数")
    private Integer totalNumber;

    /**
     * 图书分类
     */
    @Schema(description = "图书分类")
    private String categorys;

    /**
     * ISBN
     */
    @Schema(description = "ISBN")
    private String isbn;

    /**
     * 图书路径
     */
    @Schema(description = "图书路径")
    private String picPathAddress;

    /**
     * 评分
     */
    @Schema(description = "评分")
    private String score;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String statusText;

    /**
     * 状态来源
     */
    @Schema(description = "状态来源")
    private String statusSourceText;

    /**
     * 播音员
     */
    @Schema(description = "播音员")
    private String announcer;

    @Schema(description = "出版日期")
    private String publishDate;

}    