package cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledgequestion.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import cn.gf.saas.module.activity.enums.QuestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 题库题目分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UemKnowledgeQuestionPageReqVO extends PageParam {

    @Schema(description = "题库id", example = "2")
    private Integer knowledgeId;

    @Schema(description = "题目类型 多选/单选/判断题", example = "判断题")
    private QuestionTypeEnum questionType;

    @Schema(description = "题目")
    private String question;

    @Schema(description = "题目标题")
    private String title;

    @Schema(description = "题目编号")
    private Long questionNumber;
}