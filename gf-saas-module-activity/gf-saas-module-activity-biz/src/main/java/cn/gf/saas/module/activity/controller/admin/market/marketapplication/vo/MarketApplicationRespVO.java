package cn.gf.saas.module.activity.controller.admin.market.marketapplication.vo;

import cn.gf.saas.module.activity.controller.admin.market.markettemplate.vo.MarketTemplateRespVO;
import cn.gf.saas.module.activity.controller.admin.market.markettemplate.vo.MarketTemplateSimpleRespVO;
import cn.gf.saas.module.activity.enums.ApplicationTypeEnum;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 活动市场应用 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MarketApplicationRespVO {

    @Schema(description = "应用id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12190")
    @ExcelProperty("应用id")
    private Integer id;

    @Schema(description = "应用名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("应用名称")
    private String name;

    @Schema(description = "应用背景图链接", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("应用背景图链接")
    private String backgroundUrl;

    @Schema(description = "应用介绍图链接", example = "https://www.iocoder.cn")
    @ExcelProperty("应用介绍图链接")
    private String introduceUrl;

    @Schema(description = "简介", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("简介")
    private String introduce;

    @Schema(description = "状态：1开启  0关闭")
    @ExcelProperty("状态：1开启  0关闭")
    private Boolean enable;

    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("显示顺序")
    private Integer sort;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "模板列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<MarketTemplateSimpleRespVO> Templates;

    @Schema(description = "应用类型：1:知识竞赛  2:PK答题  3:调查问卷")
    private ApplicationTypeEnum type;

    @Schema(description = "分类ID")
    private Integer categoryId;
}