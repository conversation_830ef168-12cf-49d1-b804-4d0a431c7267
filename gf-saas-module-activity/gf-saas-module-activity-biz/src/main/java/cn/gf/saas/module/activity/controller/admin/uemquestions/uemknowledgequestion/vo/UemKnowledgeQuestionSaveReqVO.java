package cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledgequestion.vo;

import cn.gf.saas.module.activity.enums.QuestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 题库题目新增/修改 Request VO")
@Data
public class UemKnowledgeQuestionSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31860")
    private Integer recordId;

    @Schema(description = "题库id", example = "32654")
    private Integer knowledgeId;

    @Schema(description = "题目序号")
    private Integer questionIndex;

    @Schema(description = "题目类型 多选/单选/判断题", example = "2")
    private QuestionTypeEnum questionType;

    @Schema(description = "题目")
    private String question;

    @Schema(description = "结果解析")
    private String resultExplain;

    @Schema(description = "answer1")
    private String answer1;

    @Schema(description = "answer2")
    private String answer2;

    @Schema(description = "结果3")
    private String answer3;

    @Schema(description = "结果4")
    private String answer4;

    @Schema(description = "结果5")
    private String answer5;

    @Schema(description = "结果6")
    private String answer6;

    @Schema(description = "结果7")
    private String answer7;

    @Schema(description = "结果8")
    private String answer8;

    @Schema(description = "结果9")
    private String answer9;

    @Schema(description = "结果10")
    private String answer10;

    @Schema(description = "答案")
    private String result;

    @Schema(description = "题目编号")
    private Long questionNumber;

    @Schema(description = "题目标题")
    private String title;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "选项类型 0=文字，1=图片，2=音频，3=视频不能为空")
    private Short answer1type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "选项类型 0=文字，1=图片，2=音频，3=视频不能为空")
    private Short answer2type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "选项类型 0=文字，1=图片，2=音频，3=视频不能为空")
    private Short answer3type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "选项类型 0=文字，1=图片，2=音频，3=视频不能为空")
    private Short answer4type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "选项类型 0=文字，1=图片，2=音频，3=视频不能为空")
    private Short answer5type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "选项类型 0=文字，1=图片，2=音频，3=视频不能为空")
    private Short answer6type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "选项类型 0=文字，1=图片，2=音频，3=视频不能为空")
    private Short answer7type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "选项类型 0=文字，1=图片，2=音频，3=视频不能为空")
    private Short answer8type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "选项类型 0=文字，1=图片，2=音频，3=视频不能为空")
    private Short answer9type;

    @Schema(description = "选项类型 0=文字，1=图片，2=音频，3=视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "选项类型 0=文字，1=图片，2=音频，3=视频不能为空")
    private Short answer10type;

    @Schema(description = "分值")
    private Integer score;

    @Schema(description = "原答案")
    private String oldAnswer;

}