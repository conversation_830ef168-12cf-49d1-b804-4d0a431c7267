package cn.gf.saas.module.activity.dal.mysql.staffcollege;

import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.mybatis.core.mapper.BaseMapperX;
import cn.gf.saas.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.gf.saas.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.gf.saas.module.activity.controller.admin.staffcollege.vo.StaffCollegeUserPageReqVO;
import cn.gf.saas.module.activity.dal.dataobject.appuser.AppUserDO;
import cn.gf.saas.module.activity.dal.dataobject.staffcollege.StaffCollegeUserDO;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 职工学院参与用户 Mapper
 */
@Mapper
@DS("uemtraining")
public interface StaffCollegeUserMapper extends BaseMapperX<StaffCollegeUserDO> {
    default void insertBatch(List<StaffCollegeUserDO> users) {
        if (users == null || users.isEmpty()) return;
        users.forEach(this::insert);
    }

    default void deleteUsersByCourseId(Integer courseId, List<String> userIds) {
        if (userIds == null || userIds.isEmpty()) return;
        delete(new LambdaQueryWrapperX<StaffCollegeUserDO>()
                .eq(StaffCollegeUserDO::getCourseId, courseId)
                .in(StaffCollegeUserDO::getUserId, userIds));
    }

    default boolean isExistUser(Integer courseId, String userId) {
        return selectCount(new LambdaQueryWrapperX<StaffCollegeUserDO>()
                .eq(StaffCollegeUserDO::getCourseId, courseId)
                .eq(StaffCollegeUserDO::getUserId, userId)) > 0;
    }

    default PageResult<StaffCollegeUserDO> selectPage(StaffCollegeUserPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new MPJLambdaWrapperX<StaffCollegeUserDO>()
                .eqIfPresent(StaffCollegeUserDO::getCourseId, pageReqVO.getCourseId())
                .leftJoin(AppUserDO.class, AppUserDO::getUserId, StaffCollegeUserDO::getUserId)
                .eq(StrUtil.isNotBlank(pageReqVO.getUserId()), AppUserDO::getUserId, pageReqVO.getUserId())
                .eq(AppUserDO::getEnabled, 1)
                .eq(AppUserDO::getDeleted, 0)
                .orderByDesc(StaffCollegeUserDO::getId));
    }

    /**
     * 根据课程编号查询所有参与用户
     */
    default List<StaffCollegeUserDO> selectByCourseId(Integer courseId) {
        return selectList(new LambdaQueryWrapperX<StaffCollegeUserDO>()
                .eq(StaffCollegeUserDO::getCourseId, courseId));
    }

    /**
     * 根据课程编号批量删除参与用户
     */
    default void deleteByCourseId(Integer courseId) {
        delete(new LambdaQueryWrapperX<StaffCollegeUserDO>()
                .eq(StaffCollegeUserDO::getCourseId, courseId));
    }
}