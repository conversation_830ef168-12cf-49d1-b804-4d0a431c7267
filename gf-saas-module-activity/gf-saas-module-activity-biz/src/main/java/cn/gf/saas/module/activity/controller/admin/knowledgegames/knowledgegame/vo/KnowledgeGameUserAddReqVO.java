package cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 答题闯关选择人员新增/修改 Request VO")
@Data
public class KnowledgeGameUserAddReqVO {

    @Schema(description = "答题闯关ID", example = "1164")
    private Integer gameId;

    @Schema(description = "参与人员IDs")
    private List<String> userIds;

}