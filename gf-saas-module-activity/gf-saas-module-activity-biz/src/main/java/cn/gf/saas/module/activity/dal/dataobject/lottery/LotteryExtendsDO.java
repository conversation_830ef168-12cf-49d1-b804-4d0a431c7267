package cn.gf.saas.module.activity.dal.dataobject.lottery;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 抽奖活动扩展 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lottery_extends", autoResultMap = true)
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LotteryExtendsDO {

    /**
     * 抽奖活动ID
     */
    @TableId("lotteryId")
    private Integer lotteryId;

    /**
     * 预设奖项数
     */
    @TableField("PrePrizeCount")
    private Integer prePrizeCount;

    /**
     * 预设抽奖总人数旧值
     */
    @TableField("OldUserCount")
    private Integer oldUserCount;

    /**
     * 预设抽奖总人数
     */
    @TableField("PreUserCount")
    private Integer preUserCount;

    /**
     * 创建时间
     */
    @TableField("CreatedTime")
    private LocalDateTime createdTime;

    /**
     * 修改时间
     */
    @TableField("ModifyTime")
    private LocalDateTime modifyTime;

    /**
     * 修改人Id
     */
    @TableField("ModifyUserId")
    private String modifyUserId;

    /**
     * 总抽奖次数
     */
    @TableField("LotteryOfTotal")
    private Integer lotteryOfTotal;

    /**
     * 每天抽奖次数
     */
    @TableField("LotteryOfDays")
    private Integer lotteryOfDays;
}
