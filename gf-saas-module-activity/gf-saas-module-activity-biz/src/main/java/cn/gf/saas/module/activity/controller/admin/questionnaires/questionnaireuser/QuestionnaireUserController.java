package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireuser;

import cn.gf.saas.framework.apilog.core.annotation.ApiAccessLog;
import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageParam;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.framework.excel.core.util.ExcelUtils;
import cn.gf.saas.module.activity.controller.admin.knowledges.knowledgeuser.vo.KnowledgeUserRespVO;
import cn.gf.saas.module.activity.controller.admin.knowledges.knowledgeuser.vo.KnowledgeUserSaveReqVO;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireuser.vo.QuestionnaireUserPageReqVO;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireuser.vo.QuestionnaireUserRespVO;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireuser.vo.QuestionnaireUserSaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.knowledgeuser.KnowledgeUserDO;
import cn.gf.saas.module.activity.dal.dataobject.questionnaireuser.QuestionnaireUserDO;
import cn.gf.saas.module.activity.service.appuser.AppUserService;
import cn.gf.saas.module.activity.service.questionnaireuser.QuestionnaireUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.var;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static cn.gf.saas.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 调查问卷选择人员")
@RestController
@RequestMapping("/activity/questionnaire-user")
@Validated
public class QuestionnaireUserController {

    @Resource
    private QuestionnaireUserService questionnaireUserService;

    @Resource
    private AppUserService userService;


    @PostMapping("/create-batch")
    @Operation(summary = "创建选择人员")
//    @PreAuthorize("@ss.hasPermission('knowledgeuser:knowledge-user:create')")
    public CommonResult<Boolean> createKnowledgeUser(@Valid @RequestBody QuestionnaireUserSaveReqVO createReqVO) {
        return success(questionnaireUserService.createQuestionnaireUser(createReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除调查问卷选择人员")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire-user:delete')")
    public CommonResult<Boolean> deleteQuestionnaireUser(@RequestBody List<String> ids,@RequestParam("questionnaireId") Integer qId) {
        questionnaireUserService.deleteQuestionnaireUser(ids,qId);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得调查问卷选择人员分页")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire-user:query')")
    public CommonResult<PageResult<QuestionnaireUserRespVO>> getQuestionnaireUserPage(@Valid QuestionnaireUserPageReqVO pageReqVO) {
        PageResult<QuestionnaireUserDO> pageResult = questionnaireUserService.getQuestionnaireUserPage(pageReqVO);
        var list = BeanUtils.toBean(pageResult, QuestionnaireUserRespVO.class);
        if(list.getTotal()==0){
            return success(list);
        }
        var users = userService.getAppUsersByIds( pageResult.getList().stream().map(QuestionnaireUserDO::getUserId).collect(Collectors.toList()));

        for (var item:list.getList()
        ) {
            var user = users.stream().filter(c->c.getUserId().equals(item.getUserId())).findFirst();
            item.setDepartLevel(user.get().getDepartLevel1());
            item.setPhone(user.get().getMobilePhone());
            item.setName(user.get().getRealName());
        }
        return success(list);
    }


}