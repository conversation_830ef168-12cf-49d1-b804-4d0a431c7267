package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaire.vo;

import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaire.model.ContactConfigModel;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaire.model.RegistConfigModel;
import cn.gf.saas.module.activity.enums.ActivityJoinTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

@Schema(description = "管理后台 - 调查问卷新增/修改 Request VO")
@Data
public class QuestionnaireSaveReqVO {

    @Schema(description = "问卷ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19631")
    private Integer questionnaireId;

//    @Schema(description = "发布人id", example = "13696")
//    private String publishUserId;

//    @Schema(description = "发布时间，点击发布时填写")
//    private LocalDateTime publishTime;

//    @Schema(description = "单位id", example = "6081")
//    private String companyId;

    @Schema(description = "参与类型", example = "2")
    private Short joinType;

    @Schema(description = "所在一级部门")
    private String departLevel1;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "标题")
    private String title;
//
//    @Schema(description = "标题图片", example = "李四")
//    private String titleImageName;

    @Schema(description = "内容")
    private String content;

//    @Schema(description = "参与人数", example = "11354")
//    private Integer userCount;

    @Schema(description = "封面图片", example = "开发")
    private String coverImageName;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "1:隐藏说明,0或空默认显示")
    private Boolean hideContent;

    @Schema(description = "1:隐藏题目类型,0或空默认显示", example = "1")
    private Boolean isHideQuestionType;

//    @Schema(description = "是否参与抽奖，1：参与，0.不参与")
//    private Boolean isInLottery;

    @Schema(description = "扉页背景图", example = "https://www.iocoder.cn")
    private String backImageUrl;

    @Schema(description = "1:隐藏职工信息,0或空默认显示")
    private Boolean isHideUserInfo;

//    @Schema(description = "字体颜色配置")
//    private String questionConfig;

    @Schema(description = "有效期")
    private LocalDateTime validTime;

    @Schema(description = "主办方配置{\"organizer\":\"主办方\",\"contactName\":\"联系人\",\"contactPhone\":\"联系电话\",\"address\":\"活动地点\"}")
    private ContactConfigModel contactConfig;

    @Schema(description = "活动方式（0:报名形式,1:问卷形式）", example = "1")
    private Integer registType;

    @Schema(description = "报名限制：{\"limitType\":\"0不限制1总人数限制2性别限制\",\"allLimit\":\"总人数限制\",\"manLimit\":\"男人数\",\"womenLimit\":\"女人数\"}")
    private RegistConfigModel registConfig;

}