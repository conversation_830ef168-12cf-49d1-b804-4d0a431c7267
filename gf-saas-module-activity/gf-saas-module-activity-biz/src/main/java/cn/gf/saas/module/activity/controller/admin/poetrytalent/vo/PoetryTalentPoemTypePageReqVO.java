package cn.gf.saas.module.activity.controller.admin.poetrytalent.vo;


import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 诗词秀才诗词分类分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PoetryTalentPoemTypePageReqVO extends PageParam {

}
