package cn.gf.saas.module.activity.controller.admin.poetrytalent.vo;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "诗词达人等级更新/创建请求对象")
public class PoetryTalentLevelInfoRespVO {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifyTime;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "限制时间（秒）")
    private Integer limitTime;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "诗词ID")
    private Integer poetryId;

    @Schema(description = "诗词ID列表")
    private String poetryIdList;

    @Schema(description = "设置类型")
    private String settingType;

    @Schema(description = "随机字母数量")
    private Integer randomLetterCount;

    @Schema(description = "答案JSON")
    private String answerJson;

    @Schema(description = "重试次数")
    private Integer restartTimes;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "活动ID")
    private Integer activityId;

    @Schema(description = "诗词名称")
    private String poetryName;

    @Schema(description = "诗词朝代")
    private String poetryDynasty;

    @Schema(description = "诗词规则")
    private String poetryRule;

    @Schema(description = "诗词类型名称")
    private String poetryTypeName;

    @Schema(description = "子诗词列表")
    private List<SubPoetry> subPoetry;

    @Schema(description = "索引")
    private Integer idx;

    @Schema(description = "答案模型")
    private List<List<Object>> answerModels;

    @Data
    @Schema(description = "子诗词对象")
    public static class SubPoetry {

        @Schema(description = "ID")
        private Integer id;

        @Schema(description = "是否删除")
        private Boolean deleted;

        @Schema(description = "创建时间")
        private LocalDateTime createTime;

        @Schema(description = "修改时间")
        private LocalDateTime modifyTime;

        @Schema(description = "名称")
        private String name;

        @Schema(description = "作者")
        private String author;

        @Schema(description = "朝代")
        private String dynasty;

        @Schema(description = "类型ID")
        private Integer typeId;

        @Schema(description = "类型名称")
        private String typeName;

        @Schema(description = "规则")
        private String rule;

        @Schema(description = "等级")
        private Integer level;

        @Schema(description = "内容")
        private String content;
    }
}

