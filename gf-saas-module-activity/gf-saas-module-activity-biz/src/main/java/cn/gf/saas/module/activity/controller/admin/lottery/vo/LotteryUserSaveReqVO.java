package cn.gf.saas.module.activity.controller.admin.lottery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 幸运抽奖选择人员新增/删除 Request VO")
@Data
public class LotteryUserSaveReqVO {

    @Schema(description = "幸运抽奖活动ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1164")
    @NotNull(message = "幸运抽奖活动ID不能为空")
    private Integer lotteryId;

    @Schema(description = "参与人员IDs", requiredMode = Schema.RequiredMode.REQUIRED, example = "6469")
    @NotNull(message = "参与人员IDs不能为空")
    private List<String> userIds;
}
