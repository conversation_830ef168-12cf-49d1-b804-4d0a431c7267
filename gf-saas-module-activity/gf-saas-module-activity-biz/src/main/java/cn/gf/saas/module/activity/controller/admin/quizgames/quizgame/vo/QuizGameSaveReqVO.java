package cn.gf.saas.module.activity.controller.admin.quizgames.quizgame.vo;

import cn.gf.saas.module.activity.controller.admin.quizgames.quizgame.model.RankSettingModel;
import cn.gf.saas.module.activity.dal.dataobject.activity.RandomSettingModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - PK答题新增/修改 Request VO")
@Data
public class QuizGameSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17561")
    private Integer id;

//    @Schema(description = "单位id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11204")
//    @NotEmpty(message = "单位id不能为空")
//    private String companyId;

    @Schema(description = "活动标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "活动标题不能为空")
    private String title;

    @Schema(description = "参与类型", example = "1")
    private Short joinType;

    @Schema(description = "开始时间")
    private LocalDateTime beginTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "显示有效期")
    private LocalDateTime validTime;

    @Schema(description = "可见排行榜 json配置{\"backImg\":\"dsdsdd\",\"hideTitle\":true,.......}")

    private RankSettingModel rankSetting;

    @Schema(description = "banner")
    private String banner;

    @Schema(description = "活动说明")
    private String rules;

//    @Schema(description = "参与人数", example = "15574")
//    private Integer joinCount;

//    @Schema(description = "是否发布", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "是否发布不能为空")
//    private Boolean published;
//
//    @Schema(description = "发布人id")
//    private String publishedUser;
//
//    @Schema(description = "发布时间")
//    private LocalDateTime publishedTime;
//
//    @Schema(description = "创建人")
//    private String createdUser;
//
//    @Schema(description = "创建时间")
//    private LocalDateTime createdTime;

    @Schema(description = "题库Id, 多个使用逗号隔开", example = "23097")
    private String libKid;

    @Schema(description = "是否为随机题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否为随机题不能为空")
    private Boolean isRandom;

    @Schema(description = "随机题设置 JSON")
    private List<RandomSettingModel> randomSetting;

    @Schema(description = "每题限时 10~3600 秒	")
    private Short limitTime;

    @Schema(description = "PK总次数 0表示不限制")
    private Short limitPkTotal;

    @Schema(description = "每日PK次数限制  0表示不限制")
    private Short limitPkDay;

//    @Schema(description = "匹配等待时间")
//    private Short limitCheckTime;

//    @Schema(description = "是否抽奖")
//    private Boolean isLottery;

}