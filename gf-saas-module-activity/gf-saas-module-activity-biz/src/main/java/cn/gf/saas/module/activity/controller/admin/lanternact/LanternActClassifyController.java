package cn.gf.saas.module.activity.controller.admin.lanternact;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.activity.controller.admin.lanternact.vo.LanternActClassifyCreateReqVO;
import cn.gf.saas.module.activity.controller.admin.lanternact.vo.LanternActClassifyDeleteReqVO;
import cn.gf.saas.module.activity.controller.admin.lanternact.vo.LanternActClassifyPageReqVO;
import cn.gf.saas.module.activity.controller.admin.lanternact.vo.LanternActClassifyRespVO;
import cn.gf.saas.module.activity.controller.admin.lanternact.vo.LanternActClassifyUpdateReqVO;
import cn.gf.saas.module.activity.service.lanternact.LanternActClassifyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

/**
 * 猜灯谜分类管理 Controller
 */
@Tag(name = "管理后台 - 猜灯谜分类")
@RestController
@RequestMapping("/activity/lantern-act-classify")
@Validated
public class LanternActClassifyController {

    @Resource
    private LanternActClassifyService lanternActClassifyService;

    @PostMapping("/create")
    @Operation(summary = "创建猜灯谜分类")
    public CommonResult<Integer> createLanternActClassify(@Valid @RequestBody LanternActClassifyCreateReqVO createReqVO) {
        return success(lanternActClassifyService.create(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新猜灯谜分类")
    public CommonResult<Boolean> updateLanternActClassify(@Valid @RequestBody LanternActClassifyUpdateReqVO updateReqVO) {
        lanternActClassifyService.update(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/batch-delete")
    @Operation(summary = "批量删除猜灯谜分类")
    public CommonResult<Boolean> batchDeleteLanternActClassify(@Valid @RequestBody LanternActClassifyDeleteReqVO deleteReqVO) {
        lanternActClassifyService.batchDelete(deleteReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得猜灯谜分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<LanternActClassifyRespVO> getLanternActClassify(@RequestParam("id") Integer id) {
        LanternActClassifyRespVO classify = lanternActClassifyService.get(id);
        return success(classify);
    }

    @GetMapping("/list")
    @Operation(summary = "获得猜灯谜分类列表")
    @Parameter(name = "actId", description = "活动ID", required = true, example = "1024")
    public CommonResult<List<LanternActClassifyRespVO>> getLanternActClassifyList(@RequestParam("actId") Integer actId) {
        List<LanternActClassifyRespVO> list = lanternActClassifyService.getList(actId);
        return success(list);
    }

    @GetMapping("/page")
    @Operation(summary = "获得猜灯谜分类分页")
    public CommonResult<PageResult<LanternActClassifyRespVO>> getLanternActClassifyPage(@Valid LanternActClassifyPageReqVO pageReqVO) {
        PageResult<LanternActClassifyRespVO> pageResult = lanternActClassifyService.getPage(pageReqVO);
        return success(pageResult);
    }
}
