package cn.gf.saas.module.activity.dal.mysql.lottery;

import cn.gf.saas.framework.mybatis.core.mapper.BaseMapperX;
import cn.gf.saas.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.gf.saas.module.activity.dal.dataobject.lottery.CompanyActivityUserDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 活动用户关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("uemtraining")
public interface CompanyActivityUserMapper extends BaseMapperX<CompanyActivityUserDO> {

    /**
     * 检查用户是否已存在于活动中
     *
     * @param actId 活动ID
     * @param joinValue 用户ID
     * @return 是否存在
     */
    default Boolean isExistUser(Integer actId, String joinValue) {
        return selectCount(new LambdaQueryWrapperX<CompanyActivityUserDO>()
                .eq(CompanyActivityUserDO::getActId, actId)
                .eq(CompanyActivityUserDO::getJoinValue, joinValue)) > 0;
    }

    /**
     * 根据活动ID和用户ID列表删除记录
     *
     * @param actId 活动ID
     * @param joinValues 用户ID列表
     */
    default void deleteByActIdAndJoinValues(Integer actId, List<String> joinValues) {
        if (joinValues == null || joinValues.isEmpty()) {
            return;
        }
        delete(new LambdaQueryWrapperX<CompanyActivityUserDO>()
                .eq(CompanyActivityUserDO::getActId, actId)
                .in(CompanyActivityUserDO::getJoinValue, joinValues));
    }

    /**
     * 批量插入
     *
     * @param list 数据列表
     * @return 插入成功的数量
     */
    default Boolean insertBatch(List<CompanyActivityUserDO> list) {
        if (list == null || list.isEmpty()) {
            return true;
        }
        return this.insertBatch(list);
    }
}
