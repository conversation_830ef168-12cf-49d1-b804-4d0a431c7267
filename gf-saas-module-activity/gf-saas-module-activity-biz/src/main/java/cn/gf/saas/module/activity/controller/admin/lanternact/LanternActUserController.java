package cn.gf.saas.module.activity.controller.admin.lanternact;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.activity.controller.admin.lanternact.vo.LanternActUserDeleteReq;
import cn.gf.saas.module.activity.controller.admin.lanternact.vo.LanternActUserPageReq;
import cn.gf.saas.module.activity.controller.admin.lanternact.vo.LanternActUserReq;
import cn.gf.saas.module.activity.controller.admin.lanternact.vo.LanternActUserResp;
import cn.gf.saas.module.activity.dal.dataobject.appuser.AppUserDO;
import cn.gf.saas.module.activity.dal.dataobject.lanternact.LanternActUserDO;
import cn.gf.saas.module.activity.service.appuser.AppUserService;
import cn.gf.saas.module.activity.service.lanternact.LanternActUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

/**
 * 猜灯谜参与用户管理 Controller
 */
@Tag(name = "管理后台 - 猜灯谜参与用户")
@RestController
@RequestMapping("/activity/lantern-act-user")
@Validated
public class LanternActUserController {

    @Resource
    private LanternActUserService lanternActUserService;
    
    @Resource
    private AppUserService userService;

    @PostMapping("/add")
    @Operation(summary = "指定人员-添加")
    public CommonResult<Boolean> add(@Valid @RequestBody LanternActUserReq createReqVO) {
        return success(lanternActUserService.add(createReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除选择人员")
    public CommonResult<Boolean> deleteLanternActUser(@RequestBody List<String> userIds, @RequestParam("actId") Integer actId) {
        lanternActUserService.deleteLanternActUser(userIds, actId);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得选择人员分页")
    public CommonResult<PageResult<LanternActUserResp>> getLanternActUserPage(@Valid LanternActUserPageReq pageReqVO) {
        PageResult<LanternActUserDO> pageResult = lanternActUserService.getLanternActUserPage(pageReqVO);
        PageResult<LanternActUserResp> list = BeanUtils.toBean(pageResult, LanternActUserResp.class);
        
        if (list.getTotal() == 0) {
            return success(list);
        }
        
        // 获取用户详细信息
        List<AppUserDO> users = userService.getAppUsersByIds(
                pageResult.getList().stream().map(LanternActUserDO::getUserId).collect(Collectors.toList()));

        // 填充用户信息
        for (LanternActUserResp item : list.getList()) {
            AppUserDO user = users.stream()
                    .filter(c -> c.getUserId().equals(item.getUserId()))
                    .findFirst()
                    .orElse(null);
            if (user != null) {
                item.setDepartLevel(user.getDepartLevel1());
                item.setPhone(user.getMobilePhone());
                item.setName(user.getRealName());
            }
        }
        
        return success(list);
    }
}
