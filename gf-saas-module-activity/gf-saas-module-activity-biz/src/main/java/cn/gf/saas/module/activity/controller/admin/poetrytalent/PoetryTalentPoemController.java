package cn.gf.saas.module.activity.controller.admin.poetrytalent;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.activity.controller.admin.poetrytalent.vo.*;
import cn.gf.saas.module.activity.service.poetrytalent.PoetryTalentPoemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 诗词秀才-诗词")
@RestController
@RequestMapping("/activity/poetry-talent-poem")
@Validated
public class PoetryTalentPoemController {

    @Resource
    private PoetryTalentPoemService poetryTalentPoemService;

    @GetMapping("/page")
    @Operation(summary = "获取诗词分页")
    public CommonResult<PageResult<PoetryTalentPoemInfoRespVO>> page(@Valid PoetryTalentPoemPageReqVO pageReqVO) {
        return success(poetryTalentPoemService.getPage(pageReqVO));
    }

    @GetMapping("/type-page")
    @Operation(summary = "获取诗词分类分页")
    public CommonResult<PageResult<PoetryTalentPoemTypeRespVO>> typePage(@Valid PoetryTalentPoemTypePageReqVO pageReqVO) {
        return success(poetryTalentPoemService.typePage(pageReqVO));
    }
}
