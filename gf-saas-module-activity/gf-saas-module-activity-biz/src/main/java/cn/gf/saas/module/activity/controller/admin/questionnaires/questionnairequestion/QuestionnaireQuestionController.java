package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnairequestion;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnairequestion.vo.*;
import cn.gf.saas.module.activity.dal.dataobject.questionnairequestion.QuestionnaireQuestionDO;
import cn.gf.saas.module.activity.service.questionnairequestion.QuestionnaireQuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 调查问卷题目")
@RestController
@RequestMapping("/activity/questionnaire-question")
@Validated
public class QuestionnaireQuestionController {

    @Resource
    private QuestionnaireQuestionService questionnaireQuestionService;

    @PostMapping("/create")
    @Operation(summary = "创建调查问卷题目")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire-question:create')")
    public CommonResult<Integer> createQuestionnaireQuestion(@Valid @RequestBody QuestionnaireQuestionSaveReqVO createReqVO) {
        return success(questionnaireQuestionService.createQuestionnaireQuestion(createReqVO));
    }

    @PostMapping("/import")
    @Operation(summary = "从题库导入常规题")
//    @PreAuthorize("@ss.hasPermission('knowledge:DO:create')")
    public CommonResult<Boolean> importDO(@Valid @RequestBody QuestionnaireQuestionImportReqVO createReqVO) {
        return success(questionnaireQuestionService.importQuestion(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新调查问卷题目")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire-question:update')")
    public CommonResult<Boolean> updateQuestionnaireQuestion(@Valid @RequestBody QuestionnaireQuestionSaveReqVO updateReqVO) {
        questionnaireQuestionService.updateQuestionnaireQuestion(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-index")
    @Operation(summary = "上移/下移题目序号")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire-question:update')")
    public CommonResult<Boolean> updateQuestionnaireQuestionIndex(@Valid @RequestBody QuestionnaireQuestionIndexReqVO updateReqVO) {
        questionnaireQuestionService.updateQuestionnaireQuestionIndex(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除调查问卷题目")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire-question:delete')")
    public CommonResult<Boolean> deleteQuestionnaireQuestion(@RequestBody List<Integer> ids,@RequestParam("questionnaireId") Integer id) {
        questionnaireQuestionService.deleteQuestionnaireQuestion(ids,id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得调查问卷题目")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire-question:query')")
    public CommonResult<QuestionnaireQuestionRespVO> getQuestionnaireQuestion(@RequestParam("id") Integer id) {
        QuestionnaireQuestionDO questionnaireQuestion = questionnaireQuestionService.getQuestionnaireQuestion(id);
        return success(BeanUtils.toBean(questionnaireQuestion, QuestionnaireQuestionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得调查问卷题目分页")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire-question:query')")
    public CommonResult<PageResult<QuestionnaireQuestionRespVO>> getQuestionnaireQuestionPage(@Valid QuestionnaireQuestionPageReqVO pageReqVO) {
        PageResult<QuestionnaireQuestionDO> pageResult = questionnaireQuestionService.getQuestionnaireQuestionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, QuestionnaireQuestionRespVO.class));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出调查问卷题目 Excel")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire-question:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportQuestionnaireQuestionExcel(@Valid QuestionnaireQuestionPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<QuestionnaireQuestionDO> list = questionnaireQuestionService.getQuestionnaireQuestionPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "调查问卷题目.xls", "数据", QuestionnaireQuestionRespVO.class,
//                        BeanUtils.toBean(list, QuestionnaireQuestionRespVO.class));
//    }

}