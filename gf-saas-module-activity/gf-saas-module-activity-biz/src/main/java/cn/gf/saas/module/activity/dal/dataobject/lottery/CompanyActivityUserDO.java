package cn.gf.saas.module.activity.dal.dataobject.lottery;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

/**
 * 活动用户关联 DO
 *
 * <AUTHOR>
 */
@TableName(value = "company_activity_user", autoResultMap = true)
@KeySequence("company_activity_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyActivityUserDO {

    /**
     * 主键ID
     */
    @TableId("Id")
    private Integer id;

    /**
     * 活动ID
     */
    @TableField("act_Id")
    private Integer actId;

    /**
     * 参与类型
     */
    @TableField("join_type")
    private Integer joinType;

    /**
     * 人员id/单位id
     */
    @TableField("join_value")
    private String joinValue;
}
