package cn.gf.saas.module.activity.controller.admin.lanternact.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 猜灯谜分类 分页查询 请求VO
 */
@Data
@Schema(description = "猜灯谜分类分页查询请求VO")
public class LanternActClassifyPageReqVO extends PageParam {

    /**
     * 关联猜灯谜活动ID
     */
    @Schema(description = "关联猜灯谜活动ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联猜灯谜活动ID不能为空")
    private Integer actId;
} 