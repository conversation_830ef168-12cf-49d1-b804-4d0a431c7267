package cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class KnowledgeGameUserRespVO {

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "姓名", example = "吴清焕")
    private String name;

    @Schema(description = "手机号码", example = "13276711014")
    private String phone;

    @Schema(description = "组织", example = "研发一部")
    private String departLevel;
}
