package cn.gf.saas.module.activity.controller.admin.readingact;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.activity.controller.admin.readingact.vo.ReadingActUserPageReq;
import cn.gf.saas.module.activity.controller.admin.readingact.vo.ReadingActUserReq;
import cn.gf.saas.module.activity.controller.admin.readingact.vo.ReadingActUserResp;
import cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo.WisdomGoddessUserPageReq;
import cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo.WisdomGoddessUserReq;
import cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo.WisdomGoddessUserResp;
import cn.gf.saas.module.activity.dal.dataobject.appuser.AppUserDO;
import cn.gf.saas.module.activity.dal.dataobject.readingact.ReadingActUserDO;
import cn.gf.saas.module.activity.dal.dataobject.wisdomgoddess.WisdomGoddessUserDO;
import cn.gf.saas.module.activity.service.appuser.AppUserService;
import cn.gf.saas.module.activity.service.readingact.ReadingActUserService;
import cn.gf.saas.module.activity.service.wisdomgoddessmanage.WisdomGoddessManageUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 读书活动-用户")
@RestController
@RequestMapping("/activity/readingact-user")
@Validated
public class ReadingActUserController {

    @Resource
    private ReadingActUserService readingActUserService;

    @Resource
    private AppUserService userService;

    @PostMapping("/add")
    @Operation(summary = "指定人员-添加")
    public CommonResult<Boolean> add(@Valid @RequestBody ReadingActUserReq createReqVO) {
        return success(readingActUserService.add(createReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除选择人员")
    @Parameter(name = "actId", description = "编号", required = true)
    public CommonResult<Boolean> deleteUser(@RequestBody List<String> userIds, @RequestParam("actId") Integer actId) {
        readingActUserService.deleteUser(userIds, actId);
        return success(true);
    }


    @GetMapping("/page")
    @Operation(summary = "获得选择人员分页")
    public CommonResult<PageResult<ReadingActUserResp>> getUserPage(@Valid ReadingActUserPageReq pageReqVO) {
        PageResult<ReadingActUserDO> pageResult = readingActUserService.getUserPage(pageReqVO);
        PageResult<ReadingActUserResp> list = BeanUtils.toBean(pageResult, ReadingActUserResp.class);
        if(list.getTotal() == 0){
            return success(list);
        }
        List<AppUserDO> users = userService.getAppUsersByIds( pageResult.getList().stream().map(ReadingActUserDO::getUserId).collect(Collectors.toList()));

        for (ReadingActUserResp item:list.getList()) {
            AppUserDO user = users.stream().filter(c->c.getUserId().equals(item.getUserId())).findFirst().orElse(null);
            if(user != null) {
                item.setDepartLevel(user.getDepartLevel1());
                item.setPhone(user.getMobilePhone());
                item.setName(user.getRealName());
            }
        }
        return success(list);
    }
}
