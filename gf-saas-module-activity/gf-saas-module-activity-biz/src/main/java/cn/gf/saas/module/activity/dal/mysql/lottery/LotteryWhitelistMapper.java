package cn.gf.saas.module.activity.dal.mysql.lottery;

import cn.gf.saas.framework.mybatis.core.mapper.BaseMapperX;
import cn.gf.saas.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryWhitelistDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 抽奖白名单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("uemtraining")
public interface LotteryWhitelistMapper extends BaseMapperX<LotteryWhitelistDO> {

    /**
     * 根据活动ID查询白名单列表
     *
     * @param lotteryId 活动ID
     * @return 白名单列表
     */
    default List<LotteryWhitelistDO> selectByLotteryId(Integer lotteryId) {
        return selectList(new LambdaQueryWrapperX<LotteryWhitelistDO>()
                .eq(LotteryWhitelistDO::getLotteryId, lotteryId));
    }

    /**
     * 根据活动ID和奖项ID查询白名单数量
     *
     * @param lotteryId 活动ID
     * @param prizeId 奖项ID
     * @return 白名单数量
     */
    @Select("SELECT COUNT(*) FROM lottery_whitelist WHERE lotteryId = #{lotteryId} AND prizeId = #{prizeId}")
    Integer countByLotteryIdAndPrizeId(@Param("lotteryId") Integer lotteryId, @Param("prizeId") Integer prizeId);

    /**
     * 根据活动ID删除白名单
     *
     * @param lotteryId 活动ID
     */
    default void deleteByLotteryId(Integer lotteryId) {
        delete(new LambdaQueryWrapperX<LotteryWhitelistDO>()
                .eq(LotteryWhitelistDO::getLotteryId, lotteryId));
    }
}
