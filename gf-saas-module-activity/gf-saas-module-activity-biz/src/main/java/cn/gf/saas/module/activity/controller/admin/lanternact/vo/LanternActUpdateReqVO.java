package cn.gf.saas.module.activity.controller.admin.lanternact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 猜灯谜活动更新请求VO
 */
@Data
@Schema(description = "猜灯谜活动更新请求VO")
public class LanternActUpdateReqVO {

    /**
     * 主键
     */
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键不能为空")
    private Integer id;

    /**
     * 活动名称
     */
    @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "活动名称不能为空")
    @Size(max = 120, message = "活动名称长度不能超过120个字符")
    private String actName;

    /**
     * 活动开始时间
     */
    @Schema(description = "活动开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "活动开始时间不能为空")
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @Schema(description = "活动结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "活动结束时间不能为空")
    private LocalDateTime endTime;

    /**
     * 参与类型，0集团人员 1单位人员，3选择人员，6 不限人员
     */
    @Schema(description = "参与类型，0集团人员 1单位人员，3选择人员，6 不限人员")
    private Integer joinType;

    /**
     * 每题重复答题次数
     */
    @Schema(description = "每题重复答题次数")
    private Integer answerCount;

    /**
     * 活动背景
     */
    @Schema(description = "活动背景")
    private String intro;

    /**
     * 游戏获奖规则
     */
    @Schema(description = "游戏获奖规则")
    private String gameRuleDesc;

    /**
     * 分享海报
     */
    @Schema(description = "分享海报")
    private String sharePosters;

    /**
     * 总榜单显示数量
     */
    @Schema(description = "总榜单显示数量")
    private Integer rankShowNum;

    /**
     * 部门榜单显示数量
     */
    @Schema(description = "部门榜单显示数量")
    private Integer departRankShowNum;

    /**
     * 字段显示隐藏配置json
     */
    @Schema(description = "字段显示隐藏配置json")
    private LanternConfigInfo fieldHiddenConfig;

    /**
     * 视频封面图片
     */
    @Schema(description = "视频封面图片")
    private String videoCoverPhoto;

    /**
     * 成绩取值方式：1 取最后一次分值 2 取多次中最高分值
     */
    @Schema(description = "成绩取值方式：1 取最后一次分值 2 取多次中最高分值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "成绩取值方式不能为空")
    private Integer getValueType;

    /**
     * 开始答题头部图
     */
    @Schema(description = "开始答题头部图")
    private String headImage;

    /**
     * 轮播图
     */
    @Schema(description = "轮播图")
    private List<String> imageUrls;
}
