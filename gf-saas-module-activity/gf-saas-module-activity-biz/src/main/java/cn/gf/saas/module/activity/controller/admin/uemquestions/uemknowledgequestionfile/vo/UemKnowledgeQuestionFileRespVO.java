package cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledgequestionfile.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 题库题目文件 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UemKnowledgeQuestionFileRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32377")
    @ExcelProperty("id")
    private Integer recordId;

    @Schema(description = "题库id", example = "9069")
    @ExcelProperty("题库id")
    private Integer knowledgeId;

    @Schema(description = "题目序号")
    @ExcelProperty("题目序号")
    private Integer questionIndex;

    @Schema(description = "附件名称", example = "王五")
    @ExcelProperty("附件名称")
    private String fileName;

    @Schema(description = "文件名称", example = "1559")
    @ExcelProperty("文件名称")
    private String fileId;

    @Schema(description = "文件类型", example = "1")
    @ExcelProperty("文件类型")
    private String fileType;

    @Schema(description = "类型 1=图片，2=音频，3=视频", example = "2")
    @ExcelProperty("类型 1=图片，2=音频，3=视频")
    private Short type;

    @Schema(description = "题目编号")
    @ExcelProperty("题目编号")
    private Long qNumber;

}