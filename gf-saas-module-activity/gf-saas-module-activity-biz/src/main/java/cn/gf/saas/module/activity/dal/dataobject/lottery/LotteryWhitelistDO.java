package cn.gf.saas.module.activity.dal.dataobject.lottery;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 抽奖白名单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lottery_whitelist", autoResultMap = true)
@KeySequence("lottery_whitelist_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LotteryWhitelistDO {

    /**
     * 主键ID
     */
    @TableId("Id")
    private Integer id;

    /**
     * 手机号
     */
    @TableField("mobilePhone")
    private String mobilePhone;

    /**
     * 抽奖活动ID
     */
    @TableField("lotteryId")
    private Integer lotteryId;

    /**
     * 中奖奖项
     */
    @TableField("prizeId")
    private Integer prizeId;

    /**
     * 创建时间
     */
    @TableField("createdTime")
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    @TableField("createdUserId")
    private String createdUserId;
}
