package cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledge.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 题库 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UemKnowledgeSettingRespVO {

    @Schema(description = "题库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20207")
    @ExcelProperty("题库ID")
    private Integer knowledgeId;

    @Schema(description = "题库名称")
    @ExcelProperty("题库名称")
    private String KnowledgeName;

    @Schema(description = "题库设置")
    @ExcelProperty("题库设置")
    private List<UemKnowledgeSettingDetailRespVO> Settings;

}