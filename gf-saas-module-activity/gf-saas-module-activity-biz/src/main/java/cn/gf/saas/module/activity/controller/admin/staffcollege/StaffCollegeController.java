package cn.gf.saas.module.activity.controller.admin.staffcollege;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.module.activity.controller.admin.staffcollege.vo.StaffCollegeCreateReqVO;
import cn.gf.saas.module.activity.controller.admin.staffcollege.vo.StaffCollegeUpdateReqVO;
import cn.gf.saas.module.activity.controller.admin.staffcollege.vo.StaffCollegeRespVO;
import cn.gf.saas.module.activity.service.staffcollege.StaffCollegeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

/**
 * 职工学院课程管理
 */
@RestController
@RequestMapping("/activity/staffcollege")
@Tag(name = "管理后台 - 职工学院课程")
@Validated
public class StaffCollegeController {
    @Resource
    private StaffCollegeService staffCollegeService;

    @PostMapping("/create")
    @Operation(summary = "创建课程")
    public CommonResult<Integer> create(@Valid @RequestBody StaffCollegeCreateReqVO createReqVO) {
        return success(staffCollegeService.create(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程")
    public CommonResult<Boolean> update(@Valid @RequestBody StaffCollegeUpdateReqVO updateReqVO) {
        return success(staffCollegeService.update(updateReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获取课程")
    public CommonResult<StaffCollegeRespVO> get(@RequestParam("courseId") Integer courseId) {
        return success(staffCollegeService.get(courseId));
    }
} 