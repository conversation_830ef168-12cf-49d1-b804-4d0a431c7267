package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaire.vo;

import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaire.model.ContactConfigModel;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaire.model.RegistConfigModel;
import cn.gf.saas.module.activity.enums.ActivityJoinTypeEnum;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 调查问卷 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuestionnaireRespVO {

    @Schema(description = "问卷ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19631")
    @ExcelProperty("问卷ID")
    private Integer questionnaireId;

    @Schema(description = "发布人id", example = "13696")
    @ExcelProperty("发布人id")
    private String publishUserId;

    @Schema(description = "发布时间，点击发布时填写")
    @ExcelProperty("发布时间，点击发布时填写")
    private LocalDateTime publishTime;

    @Schema(description = "单位id", example = "6081")
    @ExcelProperty("单位id")
    private String companyId;

    @Schema(description = "参与类型", example = "2")
    @ExcelProperty("参与类型")
    private Short joinType;

    @Schema(description = "所在一级部门")
    @ExcelProperty("所在一级部门")
    private String departLevel1;

    @Schema(description = "开始时间（只到日期，不需时间）")
    @ExcelProperty("开始时间（只到日期，不需时间）")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    @Schema(description = "标题")
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "标题图片", example = "李四")
    @ExcelProperty("标题图片")
    private String titleImageName;

    @Schema(description = "内容")
    @ExcelProperty("内容")
    private String content;

    @Schema(description = "参与人数", example = "11354")
    @ExcelProperty("参与人数")
    private Integer userCount;

    @Schema(description = "封面图片", example = "开发")
    @ExcelProperty("封面图片")
    private String coverImageName;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "1:隐藏说明,0或空默认显示")
    @ExcelProperty("1:隐藏说明,0或空默认显示")
    private Boolean hideContent;

    @Schema(description = "1:隐藏题目类型,0或空默认显示", example = "1")
    @ExcelProperty("1:隐藏题目类型,0或空默认显示")
    private Boolean isHideQuestionType;

    @Schema(description = "扉页背景图", example = "https://www.iocoder.cn")
    @ExcelProperty("扉页背景图")
    private String backImageUrl;

    @Schema(description = "1:隐藏用户信息,0或空默认显示")
    @ExcelProperty("1:隐藏用户信息,0或空默认显示")
    private Boolean isHideUserInfo;

    @Schema(description = "有效期")
    @ExcelProperty("有效期")
    private LocalDateTime validTime;

    @Schema(description = "主办方配置{\"organizer\":\"主办方\",\"contactName\":\"联系人\",\"contactPhone\":\"联系电话\",\"address\":\"活动地点\"}")
    @ExcelProperty("主办方配置{\"organizer\":\"主办方\",\"contactName\":\"联系人\",\"contactPhone\":\"联系电话\",\"address\":\"活动地点\"}")
    private ContactConfigModel contactConfig;

    @Schema(description = "活动方式（0:报名形式,1:问卷形式）", example = "2")
    @ExcelProperty("活动方式（0:报名形式,1:问卷形式）")
    private Integer registType;

    @Schema(description = "报名限制：{\"limitType\":\"0不限制1总人数限制2性别限制\",\"allLimit\":\"总人数限制\",\"manLimit\":\"男人数\",\"womenLimit\":\"女人数\"}")
    @ExcelProperty("报名限制：{\"limitType\":\"0不限制1总人数限制2性别限制\",\"allLimit\":\"总人数限制\",\"manLimit\":\"男人数\",\"womenLimit\":\"女人数\"}")
    private RegistConfigModel registConfig;

}