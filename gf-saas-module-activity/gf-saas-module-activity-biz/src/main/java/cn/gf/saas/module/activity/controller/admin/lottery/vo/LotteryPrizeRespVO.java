package cn.gf.saas.module.activity.controller.admin.lottery.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 奖项 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LotteryPrizeRespVO {

    @Schema(description = "奖项ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("奖项ID")
    private Integer id;

    @Schema(description = "活动ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("活动ID")
    private Integer lotteryId;

    @Schema(description = "奖项名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("奖项名称")
    private String name;

    @Schema(description = "奖项类别")
    @ExcelProperty("奖项类别")
    private Integer category;

    @Schema(description = "奖项图片")
    @ExcelProperty("奖项图片")
    private String prizeImage;

    @Schema(description = "奖项数量")
    @ExcelProperty("奖项数量")
    private Integer number;

    @Schema(description = "奖项金额")
    @ExcelProperty("奖项金额")
    private BigDecimal amount;

    @Schema(description = "中奖金额（奖项金额）")
    @ExcelProperty("中奖金额")
    private BigDecimal winAmount;

    @Schema(description = "中奖数量（奖项类型为实物时值为1，不是实物时为空）")
    @ExcelProperty("中奖数量")
    private Integer winCount;

    @Schema(description = "总奖项数量（奖项类型为实物时该值为奖项数量的值，不是实物时为空）")
    @ExcelProperty("总奖项数量")
    private Integer totalPrizeCount;

    @Schema(description = "总奖项金额（奖项类型为虚拟券时值为：奖项数量 * 中奖金额，是实物时为空）")
    @ExcelProperty("总奖项金额")
    private BigDecimal totalPrizeAmount;

    @Schema(description = "面积占比")
    @ExcelProperty("面积占比")
    private BigDecimal areaRatio;

    @Schema(description = "中奖提示信息")
    @ExcelProperty("中奖提示信息")
    private String toast;

    @Schema(description = "奖品详情")
    @ExcelProperty("奖品详情")
    private String content;
}
