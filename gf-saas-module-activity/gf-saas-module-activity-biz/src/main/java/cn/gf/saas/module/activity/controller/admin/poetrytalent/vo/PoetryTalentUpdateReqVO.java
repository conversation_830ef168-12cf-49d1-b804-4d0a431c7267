package cn.gf.saas.module.activity.controller.admin.poetrytalent.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@Data
@Schema(description = "诗词秀才活动更新请求对象")
public class PoetryTalentUpdateReqVO {

    @Schema(description = "活动id")
    private Long id;

    @Schema(description = "活动名称")
    @Size(max = 50, message = "活动名称长度不能超过50")
    @NotBlank(message = "活动名称不能为空")
    private String title;

    @Schema(description = "活动列表图标 URL")
    private String iconUrl;

    @Schema(description = "卡通人物 URL")
    private String bannerUrl;

    @Schema(description = "关卡背景图 URL")
    private String bgUrl;

    @Schema(description = "排行榜背景图 URL")
    private String rankUrl;

    @Schema(description = "诗词选题背景图 URL")
    private String poetryBackgroundUrl;

    @Schema(description = "活动规则背景图 URL")
    private String backgroundUrl;

    @Schema(description = "可重启次数")
    private int restartTimes;

    @Schema(description = "成绩取值方式-活动：1 过关数最多用时最少，2 取最后一次")
    @NotNull(message = "成绩取值方式-活动不能为空")
    private int lastScoreActivity;

    @Schema(description = "成绩取值方式-关卡：1 用时最少，2 取最后一次")
    @NotNull(message = "成绩取值方式-关卡不能为空")
    private int lastScoreLevel;

    @Schema(description = "活动规则")
    @NotBlank(message = "活动规则不能为空")
    private String ruleContext;

    @Schema(description = "是否可见配置项 0：显示，1：隐藏")
    private int hideMan;

//    @Schema(description = "是否抽奖")
//    private boolean lottery;

//    @Schema(description = "抽奖 ID")
//    private String lotteryId;

    @Schema(description = "活动开始时间")
    @NotNull(message = "活动开始时间不能为空")
    private LocalDateTime startTime;

    @Schema(description = "活动结束时间")
    @NotNull(message = "活动结束时间不能为空")
    private LocalDateTime endTime;

    @Schema(description = "有效期")
    @NotNull(message = "有效期不能为空")
    private LocalDateTime expireTime;

    @Schema(description = "参与人员类型: 1集团人员、2单位人员、4指定人员、5不限人员")
    private Integer joinType;

    @Schema(description = "榜单显示数量： 0表示不限制")
    private Integer rankLimit;

    @Schema(description = "总榜")
    private Boolean allRank;

    @Schema(description = "部门榜")
    private Boolean departRank;

    @Schema(description = "单位榜")
    private Boolean companyRank;
}