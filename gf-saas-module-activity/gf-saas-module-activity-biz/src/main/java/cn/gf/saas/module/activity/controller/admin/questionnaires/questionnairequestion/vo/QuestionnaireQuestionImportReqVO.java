package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnairequestion.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 题库导入 Request VO")
@Data
public class QuestionnaireQuestionImportReqVO {

    @Schema(description = "题目id集合", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "题目id集合不能为空")
    private List<Integer> ids;

    @Schema(description = "活动id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "活动id不能为空")
    private Integer id;
}