package cn.gf.saas.module.activity.dal.mysql.lottery;

import cn.gf.saas.framework.mybatis.core.mapper.BaseMapperX;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryRecordsDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

/**
 * 抽奖记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("uemtraining")
public interface LotteryRecordsMapper extends BaseMapperX<LotteryRecordsDO> {

}
