package cn.gf.saas.module.activity.controller.admin.poetrytalent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "诗词秀才关卡删除请求对象")
public class PoetryTalentLevelDeleteReqVO {

    @Schema(description = "活动id")
    @NotNull(message = "活动id不能为空")
    private Integer activityId;

    @Schema(description = "关卡Ids")
    @NotNull(message = "关卡Ids不能为空")
    private List<Integer> ids;
}
