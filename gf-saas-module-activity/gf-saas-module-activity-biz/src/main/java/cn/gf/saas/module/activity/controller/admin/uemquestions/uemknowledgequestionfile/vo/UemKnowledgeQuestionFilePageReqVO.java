package cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledgequestionfile.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 题库题目文件分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UemKnowledgeQuestionFilePageReqVO extends PageParam {

    @Schema(description = "题库id", example = "9069")
    private Integer knowledgeId;

    @Schema(description = "题目序号")
    private Integer questionIndex;

    @Schema(description = "附件名称", example = "王五")
    private String fileName;

    @Schema(description = "文件名称", example = "1559")
    private String fileId;

    @Schema(description = "文件类型", example = "1")
    private String fileType;

    @Schema(description = "类型 1=图片，2=音频，3=视频", example = "2")
    private Short type;

    @Schema(description = "题目编号")
    private Long qNumber;

}