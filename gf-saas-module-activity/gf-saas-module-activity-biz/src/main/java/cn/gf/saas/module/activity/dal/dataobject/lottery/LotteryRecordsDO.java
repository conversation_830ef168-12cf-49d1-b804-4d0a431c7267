package cn.gf.saas.module.activity.dal.dataobject.lottery;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 抽奖记录 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lotteryrecords", autoResultMap = true)
@KeySequence("lotteryrecords_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LotteryRecordsDO {

    /**
     * 记录ID
     */
    @TableId("recordId")
    private Integer recordId;

    /**
     * 抽奖活动ID
     */
    @TableField("lotteryId")
    private Integer lotteryId;

    /**
     * 用户ID
     */
    @TableField("userId")
    private String userId;

    /**
     * 抽奖号码
     */
    @TableField("LuckyNo")
    private Integer luckyNo;

    /**
     * 是否中奖
     */
    @TableField("IsWinning")
    private Boolean isWinning;

    /**
     * 抽奖时间
     */
    @TableField("CreatedTime")
    private LocalDateTime createdTime;

    /**
     * 中奖奖项
     */
    @TableField("PrizesId")
    private Integer prizesId;

    /**
     * 奖项金额
     */
    @TableField("PrizesAmount")
    private BigDecimal prizesAmount;

    /**
     * 提示信息
     */
    @TableField("PrizesToast")
    private String prizesToast;

    /**
     * 奖项名称
     */
    @TableField("PrizesName")
    private String prizesName;

    /**
     * 记录类型：未中奖，奖池不足，已中奖
     */
    @TableField("Reason")
    private String reason;

    /**
     * 福利申请Id
     */
    @TableField("WelfareApplyId")
    private String welfareApplyId;
}
