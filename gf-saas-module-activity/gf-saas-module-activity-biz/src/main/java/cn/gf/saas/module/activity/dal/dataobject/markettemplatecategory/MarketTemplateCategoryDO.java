package cn.gf.saas.module.activity.dal.dataobject.markettemplatecategory;

import cn.gf.saas.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 活动市场分类 DO
 *
 * <AUTHOR>
 */
@TableName("activity_market_template_category")
@KeySequence("activity_market_template_category_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketTemplateCategoryDO extends BaseDO {

    /**
     * 分类id
     */
    @TableId
    private Integer id;
    /**
     * 分类名称
     */
    private String name;

}