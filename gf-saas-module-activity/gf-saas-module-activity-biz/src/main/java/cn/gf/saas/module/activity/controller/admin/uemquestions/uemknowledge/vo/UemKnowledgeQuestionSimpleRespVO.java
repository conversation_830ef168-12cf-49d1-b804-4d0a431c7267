package cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledge.vo;

import cn.gf.saas.module.activity.enums.QuestionTypeEnum;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 题库 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UemKnowledgeQuestionSimpleRespVO {

    @Schema(description = "题目ID,导入时用到", requiredMode = Schema.RequiredMode.REQUIRED, example = "20207")
    @ExcelProperty("题目ID,导入时用到")
    private Integer recordId;

    @Schema(description = "题目类型")
    @ExcelProperty("题目类型")
    private QuestionTypeEnum questionType;

    @Schema(description = "题目编号:  题目类型（1判断 2单选 3多选 9其它）(1位) + KnowledgeId（11位) + 顺序位（3位）", requiredMode = Schema.RequiredMode.REQUIRED, example = "20207")
    @ExcelProperty("题目编号:  题目类型（1判断 2单选 3多选 9其它）(1位) + KnowledgeId（11位) + 顺序位（3位）")
    private Long questionNumber;

    @Schema(description = "题目")
    @ExcelProperty("题目")
    private String question;
//
//    @Schema(description = "标题")
//    @ExcelProperty("标题")
//    private String Title;

    @Schema(description = "答案")
    @ExcelProperty("答案")
    private String result;

    @Schema(description = "answer1")
    private String answer1;

    @Schema(description = "answer2")
    private String answer2;

    @Schema(description = "结果3")
    private String answer3;

    @Schema(description = "结果4")
    private String answer4;

    @Schema(description = "结果5")
    private String answer5;

    @Schema(description = "结果6")
    private String answer6;

    @Schema(description = "结果7")
    private String answer7;

    @Schema(description = "结果8")
    private String answer8;

    @Schema(description = "结果9")
    private String answer9;

    @Schema(description = "结果10")
    private String answer10;

}