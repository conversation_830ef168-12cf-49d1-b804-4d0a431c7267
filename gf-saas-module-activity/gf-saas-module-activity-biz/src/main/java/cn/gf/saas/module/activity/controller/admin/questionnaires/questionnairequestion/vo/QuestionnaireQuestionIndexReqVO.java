package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnairequestion.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 调查问卷题目上移下移 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class QuestionnaireQuestionIndexReqVO{

    @Schema(description = "自增id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1717")
    private Integer recordId;

    @Schema(description = "问卷id", requiredMode = Schema.RequiredMode.REQUIRED,example = "6009")
    private Integer questionnaireId;

    @Schema(description = "true上移false下移", requiredMode = Schema.RequiredMode.REQUIRED,example = "true")
    private Boolean up;
}