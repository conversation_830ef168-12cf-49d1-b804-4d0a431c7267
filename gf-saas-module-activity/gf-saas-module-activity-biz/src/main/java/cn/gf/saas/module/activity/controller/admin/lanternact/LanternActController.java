package cn.gf.saas.module.activity.controller.admin.lanternact;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.activity.controller.admin.lanternact.vo.*;
import cn.gf.saas.module.activity.service.lanternact.LanternActService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

/**
 * 猜灯谜活动管理 Controller
 */
@Tag(name = "管理后台 - 猜灯谜活动")
@RestController
@RequestMapping("/activity/lantern-act")
@Validated
public class LanternActController {

    @Resource
    private LanternActService lanternActService;

    @PostMapping("/create")
    @Operation(summary = "创建猜灯谜活动")
    public CommonResult<Integer> createLanternAct(@Valid @RequestBody LanternActCreateReqVO createReqVO) {
        return success(lanternActService.create(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新猜灯谜活动")
    public CommonResult updateLanternAct(@Valid @RequestBody LanternActUpdateReqVO updateReqVO) {
        lanternActService.update(updateReqVO);
        return success();
    }

    @GetMapping("/get")
    @Operation(summary = "获得猜灯谜活动")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<LanternActRespVO> getLanternAct(@RequestParam("id") Integer id) {
        LanternActRespVO lanternAct = lanternActService.get(id);
        return success(lanternAct);
    }

    @GetMapping("/page")
    @Operation(summary = "获得猜灯谜活动分页")
    public CommonResult<PageResult<LanternActRespVO>> getLanternActPage(@Valid LanternActPageReqVO pageReqVO) {
        PageResult<LanternActRespVO> pageResult = lanternActService.getPage(pageReqVO);
        return success(pageResult);
    }
}
