package cn.gf.saas.module.activity.dal.dataobject.lottery;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 抽奖活动 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lottery", autoResultMap = true)
@KeySequence("lottery_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LotteryDO {

    /**
     * 主键ID
     */
    @TableId("Id")
    private Integer id;

    /**
     * 项目名称
     */
    @TableField("Name")
    private String name;

    /**
     * 开始时间
     */
    @TableField("BeginTime")
    private LocalDateTime beginTime;

    /**
     * 截止时间
     */
    @TableField("EndTime")
    private LocalDateTime endTime;

    /**
     * 领取开始时间
     */
    @TableField("ReceiveBeginTime")
    private LocalDateTime receiveBeginTime;

    /**
     * 领取结束时间
     */
    @TableField("ReceiveEndTime")
    private LocalDateTime receiveEndTime;

    /**
     * 发放时间
     */
    @TableField("GrantTime")
    private LocalDateTime grantTime;

    /**
     * 公司ID
     */
    @TableField("CompanyId")
    private String companyId;

    /**
     * 创建时间
     */
    @TableField("CreatedTime")
    private LocalDateTime createdTime;

    /**
     * 发布用户ID
     */
    @TableField("PublishUserId")
    private String publishUserId;

    /**
     * 未中奖提示
     */
    @TableField("losingtoast")
    private String losingToast;

    /**
     * 发布时间
     */
    @TableField("PublishTime")
    private LocalDateTime publishTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 参与方式：0：选定用户 1：知识竞赛 2：答题闯关 3:健步活动小程序, 4闲置商城，5普惠商城，6读书活动，7云年会, 8猜灯谜,9手工活动,10职工学院
     */
    @TableField("joinType")
    private Integer joinType;

    /**
     * 是否显示中奖金额
     */
    @TableField("isShowAmount")
    private Boolean isShowAmount;

    /**
     * 无权用户欢迎词
     */
    @TableField("rejectMsg")
    private String rejectMsg;

    /**
     * 大转盘按钮文案
     */
    @TableField("lotteryDiscMsg")
    private String lotteryDiscMsg;

    /**
     * 抽中后能否继续抽奖0不能,1:能
     */
    @TableField("isLotteryAgain")
    private Boolean isLotteryAgain;

    /**
     * 活动说明
     */
    @TableField("Info")
    private String info;

    /**
     * 抽奖模式配置，默认 0、抽奖转盘；1、抽奖礼盒
     */
    @TableField("lotteryType")
    private Integer lotteryType;

    /**
     * 是否自定义抽奖人数
     */
    @TableField("Islivelottery")
    private Boolean isLiveLottery;

    /**
     * json配置
     */
    @TableField("settingConfig")
    private String settingConfig;
}
