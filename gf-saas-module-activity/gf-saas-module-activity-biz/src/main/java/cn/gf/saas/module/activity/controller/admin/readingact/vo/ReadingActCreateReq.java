package cn.gf.saas.module.activity.controller.admin.readingact.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Date;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.gf.saas.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * readingact 表的请求实体类
 */
@Data
public class ReadingActCreateReq {

    @Schema(description = "活动名称")
    @NotBlank(message = "活动名称不能为空")
    @Size(max = 50, message = "活动名称长度不能超过50个字符")
    private String title;

    @Schema(description = "活动开始时间")
    @NotNull(message = "活动开始时间不能为空")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime beginTime;

    @Schema(description = "活动结束时间")
    @NotNull(message = "活动结束时间不能为空")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime endTime;

    @Schema(description = "有效期时间")
    @NotNull(message = "有效期时间不能为空")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime expireTime;

    @Schema(description = "是否自动签到")
    private Boolean isAutoCheckIn;

    @Schema(description = "是否开启积分 默认开启，不能关闭")
    private Boolean isOpenPoints;

    @Schema(description = "是否开启连续20天签到")
    private Boolean isOpenTwentySign;

    @Schema(description = "积分榜-用户统计排名")
    private Boolean statisticpointsUserEnabled;

    @Schema(description = "积分榜-单位统计排名")
    private Boolean statisticpointsCompanyEnabled;

    @Schema(description = "读书榜-用户排名")
    private Boolean statisticreadUserEnabled;

    @Schema(description = "读书榜-单位排名")
    private Boolean statisticreadCompanyEnabled;

    @Schema(description = "活动规则内容")
    private String content;

    @Schema(description = "图书分类")
    private Boolean categoryIsShow;

    @Schema(description = "阅读记录")
    private Boolean readHistoryEnabled;

    @Schema(description = "多个配置集合json字段(IsShowBookSquare是否显示书香广场入口,IsShowShare是否显示分享按钮,IsBookSquareDesensitize书香广场姓名是否脱敏)")
    @Valid
    private ConfigInfo fieldConfig;

}    