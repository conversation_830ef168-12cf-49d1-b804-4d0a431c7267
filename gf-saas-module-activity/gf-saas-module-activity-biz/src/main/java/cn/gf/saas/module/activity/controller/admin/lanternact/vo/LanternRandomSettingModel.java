package cn.gf.saas.module.activity.controller.admin.lanternact.vo;

import cn.gf.saas.module.activity.enums.QuestionTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 文件客户端的配置
 * 不同实现的客户端，需要不同的配置，通过子类来定义
 *
 * <AUTHOR>
 */
@Data
public class LanternRandomSettingModel {
    @JsonProperty("KnowledgeId")
    private String KnowledgeId;
    @JsonProperty("QuestionType")
    private QuestionTypeEnum QuestionType;
    @JsonProperty("QuestionTotal")
    //@NotNull(message = "题目数量不能为空")
    private String QuestionTotal;
    @JsonProperty("QuestionScore")
    @NotNull(message = "题目分值不能为空")
    private Integer QuestionScore;
    @JsonProperty("Sort")
    @NotNull(message = "题目排序不能为空")
    private String Sort;


}