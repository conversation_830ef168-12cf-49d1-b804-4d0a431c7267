package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireexam.vo;

import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireexam.converter.ExamExportConvert;
import cn.gf.saas.module.activity.dal.dataobject.questionnaire.QuestionnaireExamResultModel;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 调查问卷答题 Response VO")
@Data
@ExcelIgnoreUnannotated
@ContentRowHeight(-1)
public class QuestionnaireExamRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4130")
    private Integer recordId;

    @Schema(description = "题目id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6758")
    private Integer questionnaireId;

    @Schema(description = "姓名", example = "赵本山")
    @ExcelProperty("姓名")
    private String name;

    @Schema(description = "手机号")
    @ExcelProperty("手机号")
    private String mobilePhone;

    @Schema(description = "单位")
    @ExcelProperty("单位")
    private String companyName;

    @Schema(description = "部门")
    @ExcelProperty("部门")
    private String departLevel1;

    @Schema(description = "选项答案")
    @ExcelProperty(value = "选项答案",converter = ExamExportConvert.class)
    @ContentStyle(shrinkToFit = BooleanEnum.DEFAULT,wrapped = BooleanEnum.DEFAULT)
    private List<QuestionnaireExamResultModel> result;

    @Schema(description = "答题时间")
    @ExcelProperty("时间")
    private LocalDateTime createdTime;
}