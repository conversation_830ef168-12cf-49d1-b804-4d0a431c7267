package cn.gf.saas.module.activity.controller.admin.lanternact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 猜灯谜参与用户响应VO
 */
@Data
@Schema(description = "猜灯谜参与用户响应VO")
public class LanternActUserResp {

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private String userId;

    /**
     * 姓名
     */
    @Schema(description = "姓名", example = "张三")
    private String name;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码", example = "13800138000")
    private String phone;

    /**
     * 组织
     */
    @Schema(description = "组织", example = "研发一部")
    private String departLevel;
}
