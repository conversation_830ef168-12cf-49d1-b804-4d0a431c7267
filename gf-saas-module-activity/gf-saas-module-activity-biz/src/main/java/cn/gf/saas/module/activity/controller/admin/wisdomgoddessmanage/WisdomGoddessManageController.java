package cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameCreateReqVO;
import cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo.*;
import cn.gf.saas.module.activity.dal.dataobject.wisdomgoddess.WisdomGoddessThemeDO;
import cn.gf.saas.module.activity.service.wisdomgoddessmanage.WisdomGoddessManageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 智趣答题")
@RestController
@RequestMapping("/activity/wisdom-goddess")
@Validated
public class WisdomGoddessManageController {

    @Resource
    private WisdomGoddessManageService wisdomGoddessManageService;

    @PostMapping("/create")
    @Operation(summary = "创建")
    public CommonResult<Integer> create(@Valid @RequestBody WisdomGoddessManageCreateReqVO createReqVO) {
        return success(wisdomGoddessManageService.create(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新")
    public CommonResult update(@Valid @RequestBody WisdomGoddessManageUpdateReqVO createReqVO) {
        wisdomGoddessManageService.update(createReqVO);
        return success();
    }

    @GetMapping("/get")
    @Operation(summary = "获取详情")
    public CommonResult<WisdomGoddessManageRespVO> get(Integer id) {
        return success(wisdomGoddessManageService.getDetail(id));
    }

    @GetMapping("/act-type-list")
    @Operation(summary = "获取主题类型列表")
    public CommonResult<List<WisdomGoddessThemeDO>> actTypeList() {
        return success(wisdomGoddessManageService.actTypeList());
    }
}
