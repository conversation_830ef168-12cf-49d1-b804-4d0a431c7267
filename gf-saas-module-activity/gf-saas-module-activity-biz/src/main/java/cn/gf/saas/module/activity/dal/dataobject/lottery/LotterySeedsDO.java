package cn.gf.saas.module.activity.dal.dataobject.lottery;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

/**
 * 抽奖种子 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lotteryseeds", autoResultMap = true)
@KeySequence("lotteryseeds_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LotterySeedsDO {

    /**
     * 记录ID
     */
    @TableId("recordId")
    private Integer recordId;

    /**
     * 项目编号
     */
    @TableField("lotteryId")
    private Integer lotteryId;

    /**
     * 奖项编号
     */
    @TableField("prizesId")
    private Integer prizesId;

    /**
     * 种子号码
     */
    @TableField("seedNo")
    private Integer seedNo;
}
