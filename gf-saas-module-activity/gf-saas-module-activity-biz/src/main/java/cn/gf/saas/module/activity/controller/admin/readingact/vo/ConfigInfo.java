package cn.gf.saas.module.activity.controller.admin.readingact.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.gf.saas.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Data
@Schema(description = "配置信息对象")
public class ConfigInfo {

    @Schema(description = "过期时间，格式为 'yyyy-MM-dd HH:mm:ss'")
    @JsonProperty("ExpireTime")
    private String ExpireTime;

    @Schema(description = "是否显示分享功能")
    @JsonProperty("IsShowShare")
    private boolean IsShowShare;

    @Schema(description = "是否显示书籍广场")
    @JsonProperty("IsShowBookSquare")
    private boolean IsShowBookSquare;

    @Schema(description = "书籍广场是否脱敏")
    @JsonProperty("IsBookSquareDesensitize")
    private boolean IsBookSquareDesensitize;

    @Schema(description = "活动规则")
    @JsonProperty("IsShowRule")
    private boolean IsShowRule;

    @Schema(description = "章节目录文案")
    @JsonProperty("IsShowNumber")
    private boolean IsShowNumber;

    @Schema(description = "是否显示地址信息")
    @JsonProperty("IsShowAddress")
    private boolean IsShowAddress;

    @Schema(description = "搜索功能")
    @JsonProperty("IsShowSearch")
    private boolean IsShowSearch;

    @Schema(description = "读书榜积分榜上榜人数限制")
    @JsonProperty("PointRank")
    private String PointRank;
}    