package cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "答题闯关样式配置 请求对象")
public class StyleConfigReq {
    @Schema(description = "答案图片链接")
    private String answerImage;

    @Schema(description = "排名图片链接")
    private String rankImage;

    @Schema(description = "规则图片链接")
    private String ruleImage;

    @Schema(description = "颜色信息")
    private String color;

    /**
     * 初始化对象
     */
    public static StyleConfigReq initStyleConfig(String color) {
        if(StrUtil.isEmpty(color)){
            color = "";
        }
        return new StyleConfigReq().setAnswerImage("").setRankImage("").setRuleImage("").setColor(color);
    }
}
