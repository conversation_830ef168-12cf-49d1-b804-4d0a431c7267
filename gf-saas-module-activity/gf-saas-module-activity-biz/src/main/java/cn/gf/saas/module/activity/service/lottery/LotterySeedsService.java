package cn.gf.saas.module.activity.service.lottery;

/**
 * 抽奖种子 Service 接口
 *
 * <AUTHOR>
 */
public interface LotterySeedsService {

    /**
     * 重置所有种子
     *
     * @param lotteryId 活动ID
     * @return 是否成功
     */
    Boolean resetSeeds(Integer lotteryId);

    /**
     * 重新构建种子
     *
     * @param lotteryId 活动ID
     * @param prizesId 奖项ID
     * @param count 数量
     * @return 是否成功
     */
    Boolean restructureSeeds(Integer lotteryId, Integer prizesId, Integer count);
}
