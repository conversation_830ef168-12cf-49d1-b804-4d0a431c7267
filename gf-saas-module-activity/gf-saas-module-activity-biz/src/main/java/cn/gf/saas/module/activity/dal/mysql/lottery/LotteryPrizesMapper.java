package cn.gf.saas.module.activity.dal.mysql.lottery;

import cn.gf.saas.framework.mybatis.core.mapper.BaseMapperX;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryPrizesDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 奖品 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("uemtraining")
public interface LotteryPrizesMapper extends BaseMapperX<LotteryPrizesDO> {

    /**
     * 查询指定抽奖活动的奖项数量总和
     *
     * @param lotteryId 抽奖活动ID
     * @return 奖项数量总和
     */
    @Select("SELECT COALESCE(SUM(Number), 0) FROM lotteryprizes WHERE lotteryId = #{lotteryId}")
    Integer getTotalPrizeCount(@Param("lotteryId") Integer lotteryId);

    /**
     * 查询指定抽奖活动的面积占比总和
     *
     * @param lotteryId 抽奖活动ID
     * @return 面积占比总和
     */
    @Select("SELECT COALESCE(SUM(Arearatio), 0) FROM lotteryprizes WHERE lotteryId = #{lotteryId}")
    BigDecimal getTotalAreaRatio(@Param("lotteryId") Integer lotteryId);

}
