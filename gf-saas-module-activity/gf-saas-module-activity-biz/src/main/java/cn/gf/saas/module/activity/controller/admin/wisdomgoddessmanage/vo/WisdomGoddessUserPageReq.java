package cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
public class WisdomGoddessUserPageReq extends PageParam {
    @Schema(description = "活动ID", example = "9554")
    private Integer actId;

    @Schema(description = "参与人员ID", example = "3264")
    private String userId;

    @Schema(description = "参与时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] joinTime;
}
