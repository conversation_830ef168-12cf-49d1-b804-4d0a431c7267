package cn.gf.saas.module.activity.controller.admin.staffcollege;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.activity.controller.admin.staffcollege.vo.video.*;
import cn.gf.saas.module.activity.service.staffcollege.StaffCollegeVideoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

/**
 * 职工学院视频管理
 */
@RestController
@RequestMapping("/activity/staffcollege/video")
@Tag(name = "管理后台 - 职工学院视频")
@Validated
public class StaffCollegeVideoController {
    @Resource
    private StaffCollegeVideoService staffCollegeVideoService;

    @PostMapping("/create")
    @Operation(summary = "创建视频")
    public CommonResult<Integer> create(@Valid @RequestBody StaffCollegeVideoCreateReqVO createReqVO) {
        return success(staffCollegeVideoService.create(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新视频")
    public CommonResult<Boolean> update(@Valid @RequestBody StaffCollegeVideoUpdateReqVO updateReqVO) {
        return success(staffCollegeVideoService.update(updateReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "批量删除视频")
    public CommonResult<Boolean> delete(@Valid @RequestBody StaffCollegeVideoDeleteReqVO deleteReqVO) {
        return success(staffCollegeVideoService.delete(deleteReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获取视频详情")
    public CommonResult<StaffCollegeVideoRespVO> get(@RequestParam("videoId") Integer videoId) {
        return success(staffCollegeVideoService.get(videoId));
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询视频")
    public CommonResult<PageResult<StaffCollegeVideoRespVO>> getPage(@Valid StaffCollegeVideoPageReqVO pageReqVO) {
        return success(staffCollegeVideoService.getPage(pageReqVO));
    }
} 