package cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledgequestionfile;

import cn.gf.saas.framework.apilog.core.annotation.ApiAccessLog;
import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageParam;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.framework.excel.core.util.ExcelUtils;
import cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledgequestionfile.vo.UemKnowledgeQuestionFilePageReqVO;
import cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledgequestionfile.vo.UemKnowledgeQuestionFileRespVO;
import cn.gf.saas.module.activity.dal.dataobject.uemknowledgequestionfile.UemKnowledgeQuestionFileDO;
import cn.gf.saas.module.activity.service.uemknowledgequestionfile.UemKnowledgeQuestionFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.gf.saas.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 题库题目文件")
@RestController
@RequestMapping("/activity/uem-knowledge-question-file")
@Validated
public class UemKnowledgeQuestionFileController {

    @Resource
    private UemKnowledgeQuestionFileService uemKnowledgeQuestionFileService;

//    @PostMapping("/create")
//    @Operation(summary = "创建题库题目文件")
//    @PreAuthorize("@ss.hasPermission('activity:uem-knowledge-question-file:create')")
//    public CommonResult<Integer> createUemKnowledgeQuestionFile(@Valid @RequestBody UemKnowledgeQuestionFileSaveReqVO createReqVO) {
//        return success(uemKnowledgeQuestionFileService.createUemKnowledgeQuestionFile(createReqVO));
//    }

//    @PutMapping("/update")
//    @Operation(summary = "更新题库题目文件")
//    @PreAuthorize("@ss.hasPermission('activity:uem-knowledge-question-file:update')")
//    public CommonResult<Boolean> updateUemKnowledgeQuestionFile(@Valid @RequestBody UemKnowledgeQuestionFileSaveReqVO updateReqVO) {
//        uemKnowledgeQuestionFileService.updateUemKnowledgeQuestionFile(updateReqVO);
//        return success(true);
//    }

//    @DeleteMapping("/delete")
//    @Operation(summary = "删除题库题目文件")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('activity:uem-knowledge-question-file:delete')")
//    public CommonResult<Boolean> deleteUemKnowledgeQuestionFile(@RequestParam("id") Integer id) {
//        uemKnowledgeQuestionFileService.deleteUemKnowledgeQuestionFile(id);
//        return success(true);
//    }

    @GetMapping("/get")
    @Operation(summary = "获得题库题目文件")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('activity:uem-knowledge-question-file:query')")
    public CommonResult<UemKnowledgeQuestionFileRespVO> getUemKnowledgeQuestionFile(@RequestParam("id") Integer id) {
        UemKnowledgeQuestionFileDO uemKnowledgeQuestionFile = uemKnowledgeQuestionFileService.getUemKnowledgeQuestionFile(id);
        return success(BeanUtils.toBean(uemKnowledgeQuestionFile, UemKnowledgeQuestionFileRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得题库题目文件分页")
//    @PreAuthorize("@ss.hasPermission('activity:uem-knowledge-question-file:query')")
    public CommonResult<PageResult<UemKnowledgeQuestionFileRespVO>> getUemKnowledgeQuestionFilePage(@Valid UemKnowledgeQuestionFilePageReqVO pageReqVO) {
        PageResult<UemKnowledgeQuestionFileDO> pageResult = uemKnowledgeQuestionFileService.getUemKnowledgeQuestionFilePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UemKnowledgeQuestionFileRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出题库题目文件 Excel")
//    @PreAuthorize("@ss.hasPermission('activity:uem-knowledge-question-file:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportUemKnowledgeQuestionFileExcel(@Valid UemKnowledgeQuestionFilePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<UemKnowledgeQuestionFileDO> list = uemKnowledgeQuestionFileService.getUemKnowledgeQuestionFilePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "题库题目文件.xls", "数据", UemKnowledgeQuestionFileRespVO.class,
                        BeanUtils.toBean(list, UemKnowledgeQuestionFileRespVO.class));
    }

}