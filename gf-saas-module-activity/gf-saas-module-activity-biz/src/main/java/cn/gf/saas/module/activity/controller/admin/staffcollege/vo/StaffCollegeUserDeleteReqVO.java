package cn.gf.saas.module.activity.controller.admin.staffcollege.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Schema(description = "管理后台 - 职工学院参与用户批量删除 Request VO")
@Data
public class StaffCollegeUserDeleteReqVO {
    @Schema(description = "课程编号", example = "1001")
    private Integer courseId;

    @Schema(description = "参与用户ID列表")
    private List<String> userIds;
} 