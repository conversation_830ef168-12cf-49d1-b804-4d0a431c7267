package cn.gf.saas.module.activity.convert.lottery;

import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryRespVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotterySaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryDO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryExtendsDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 幸运抽奖 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface LotteryConvert {

    LotteryConvert INSTANCE = Mappers.getMapper(LotteryConvert.class);

    LotteryDO convert(LotterySaveReqVO bean);

    LotteryRespVO convert(LotteryDO bean);

    LotteryExtendsDO convertExtends(LotterySaveReqVO bean);

}
