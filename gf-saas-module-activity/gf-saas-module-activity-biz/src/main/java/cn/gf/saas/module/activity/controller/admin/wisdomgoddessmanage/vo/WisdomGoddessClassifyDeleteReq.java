package cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class WisdomGoddessClassifyDeleteReq {
    @Schema(description = "关卡ids")
    @NotNull(message = "关卡ids不能为空")
    private List<Long> ids;

    @Schema(description = "活动id")
    @NotNull(message = "活动id不能为空")
    private Integer actId;
}
