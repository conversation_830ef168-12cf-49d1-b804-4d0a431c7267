package cn.gf.saas.module.activity.controller.admin.lanternact.vo;

import cn.gf.saas.module.activity.controller.admin.lanternact.vo.LanternRandomSettingModel;
import cn.gf.saas.module.activity.dal.dataobject.activity.RandomSettingModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 猜灯谜分类响应VO
 */
@Data
@Schema(description = "猜灯谜分类响应VO")
public class LanternActClassifyRespVO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 关联猜灯谜活动ID
     */
    @Schema(description = "关联猜灯谜活动ID")
    private Integer actId;

    /**
     * 关联答题闯关ID
     */
    @Schema(description = "关联答题闯关ID")
    private Integer gameId;

    /**
     * 知识竞赛Id
     */
    @Schema(description = "知识竞赛Id")
    private Integer knowledgeId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    private String classifyName;

    /**
     * banner图，底图
     */
    @Schema(description = "banner图，底图")
    private String bannerUrl;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    /**
     * 灯谜总数
     */
    @Schema(description = "灯谜总数")
    private Integer questionCount;

    /**
     * 答题限时（分钟）
     */
    @Schema(description = "答题限时（分钟）")
    private Integer limitMinutes;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 是否删除：0未删除 1已删除
     */
    @Schema(description = "是否删除：0未删除 1已删除")
    private Boolean deleted;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 是否是随机题
     */
    @Schema(description = "是否是随机题")
    private Boolean isRandom;

    /**
     * 题库Ids
     */
    @Schema(description = "题库Ids")
    private List<Integer> libknowledgeIds;

    /**
     * 随机题设置
     */
    @Schema(description = "随机题设置")
    private List<RandomSettingModel> randomSetting;

    /**
     * 重猜次数
     */
    @Schema(description = "重猜次数")
    private Integer redoCount;

    /**
     * 显示答案方式 true 不显示正确答案 false 显示正确答案
     */
    @Schema(description = "显示答案方式 true 不显示正确答案 false 显示正确答案")
    private Boolean notdisplayanswer;

    /**
     * 快速进入下一题
     */
    @Schema(description = "快速进入下一题")
    private Boolean quicknext;

    /**
     * 倒计时秒数
     */
    @Schema(description = "倒计时秒数")
    private Integer countdownSeconds;
}
