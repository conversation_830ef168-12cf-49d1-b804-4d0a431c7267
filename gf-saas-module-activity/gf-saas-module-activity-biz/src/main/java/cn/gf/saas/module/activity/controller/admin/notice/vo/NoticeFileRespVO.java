package cn.gf.saas.module.activity.controller.admin.notice.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 新闻文件 Response VO")
@Data
@ExcelIgnoreUnannotated
public class NoticeFileRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13788")
    @ExcelProperty("id")
    private Integer recordId;

    @Schema(description = "新闻id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2460")
    @ExcelProperty("新闻id")
    private Integer noticeId;

    @Schema(description = "文件名称", example = "14642")
    @ExcelProperty("文件名称")
    private String fileId;

    @Schema(description = "文件类型", example = "1")
    @ExcelProperty("文件类型")
    private String fileType;

    @Schema(description = "附件名称", example = "赵六")
    @ExcelProperty("附件名称")
    private String fileName;

}