package cn.gf.saas.module.activity.dal.mysql.proposal;

import cn.gf.saas.framework.mybatis.core.mapper.BaseMapperX;
import cn.gf.saas.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.gf.saas.module.activity.dal.dataobject.proposal.ProposalUserDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 提案发起人表的 Mapper 接口
 */
@Mapper
@DS("uemtraining")
public interface ProposalUserMapper extends BaseMapperX<ProposalUserDO> {

    /**
     * 根据提案ID查询所有参与用户
     */
    default List<ProposalUserDO> selectByProposalId(Integer proposalId) {
        return selectList(new LambdaQueryWrapperX<ProposalUserDO>()
                .eq(ProposalUserDO::getProposalId, proposalId)
                .orderByDesc(ProposalUserDO::getIsSponsor)
                .orderByAsc(ProposalUserDO::getCreatedTime));
    }

    /**
     * 批量插入提案用户（使用MyBatis-Plus方式）
     */
    default void insertBatch(List<ProposalUserDO> users) {
        if (users == null || users.isEmpty()) {
            return;
        }
        // 使用MyBatis-Plus的逐个插入方式
        users.forEach(this::insert);
    }
}
