package cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

import static cn.gf.saas.framework.common.util.date.DateUtils.*;

@Data
public class KnowledgeGameReq {

    /**
     * 主键，自增
     */
    @Schema(description = "主键，自增")
    private Long id;

    /**
     * 公司 ID
     */
    @Schema(description = "公司 ID")
    private String companyId;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 图片
     */
    @Schema(description = "图片")
    private String banner;

    /**
     * 活动规则
     */
    @Schema(description = "活动规则")
    private String rules;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime endTime;

    /**
     * 有效期
     */
    @Schema(description = "有效期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime validDate;

    /**
     * 参与类型，0 集团人员 1 全部人员，2 参与部门，3 选择人员，4 自主报名,5 全网报名，6 不限人员，7 读书活动，8 云年会，9 答题闯关，10 猜灯谜，11 智趣女神，12 国税积分，13 健步走小程序，14 职工学院
     */
    @Schema(description = "参与类型，0 集团人员 1 全部人员，2 参与部门，3 选择人员，4 自主报名,5 全网报名，6 不限人员，7 读书活动，8 云年会，9 答题闯关，10 猜灯谜，11 智趣女神，12 国税积分，13 健步走小程序，14 职工学院")
    private Integer joinType;

    /**
     * 发布用户 ID
     */
    @Schema(description = "发布用户 ID")
    private String publishUserId;

    /**
     * 发布时间，点击发布时填写
     */
    @Schema(description = "发布时间，点击发布时填写")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime publishTime;

    /**
     * 所在一级部门
     */
    @Schema(description = "所在一级部门")
    private String departLevel1;

    /**
     * 是否完成通知
     */
    @Schema(description = "是否完成通知")
    private Integer isCompletednotice;

    /**
     * 完成通知标题
     */
    @Schema(description = "完成通知标题")
    private String completednoticetitle;

    /**
     * 完成通知内容
     */
    @Schema(description = "完成通知内容")
    private String completednotice;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Boolean deleted;

    /**
     * 排名显示个数
     */
    @Schema(description = "排名显示个数")
    private Integer rankLimit;

    /**
     * 答题后不显示正确答案
     */
    @Schema(description = "答题后不显示正确答案")
    private Boolean notdisplayanswer;

    /**
     * 快速进入下一题
     */
    @Schema(description = "快速进入下一题")
    private Boolean quicknext;

    /**
     * 是否抽象
     */
    @Schema(description = "是否抽象")
    private Boolean isInLottery;

    /**
     * 是否以闯关数进行排名
     */
    @Schema(description = "是否以闯关数进行排名")
    private Boolean isPassNumberRank;

    /**
     * 庆祝图(男)
     */
    @Schema(description = "庆祝图(男)")
    private String manCelebrateImg;

    /**
     * 庆祝图(女)
     */
    @Schema(description = "庆祝图(女)")
    private String woManCelebrateImg;

    /**
     * 是否提供兑换券
     */
    @Schema(description = "是否提供兑换券")
    private Boolean isWelfareApply;

    /**
     * 标题字体大小
     */
    @Schema(description = "标题字体大小")
    private String titleFontSize;

    /**
     * 视频背景图
     */
    @Schema(description = "视频背景图")
    private String videoCoverPhoto;

    /**
     * 标题图片
     */
    @Schema(description = "标题图片")
    private String titleImageName;

    /**
     * 取值方式
     */
    @Schema(description = "取值方式")
    private Integer getValueType;

    /**
     * 通关提示
     */
    @Schema(description = "通关提示")
    private String passedTips;

    /**
     * 闯关成功图片
     */
    @Schema(description = "闯关成功图片")
    private String passedImg;

    /**
     * 闯关失败图片
     */
    @Schema(description = "闯关失败图片")
    private String failedImg;

    /**
     * 用户是否可以暂停闯关
     */
    @Schema(description = "用户是否可以暂停闯关")
    private Boolean isUserPause;

    /**
     * 是否启用活动预热
     */
    @Schema(description = "是否启用活动预热")
    private Boolean isEnabledActRegist;

    /**
     * 活动预热的 id
     */
    @Schema(description = "活动预热的 id")
    private Integer actRegistId;

    /**
     * 重做次数
     */
    @Schema(description = "重做次数")
    private Integer redoTime;

    /**
     * 保存重做最高分
     */
    @Schema(description = "保存重做最高分")
    private Boolean saveTopScore;

    /**
     * 主题类型 Id：0 默认 1 五一
     */
    @Schema(description = "主题类型 Id：0 默认 1 五一")
    private Integer themeTypeId;

    /**
     * 倒计时秒数
     */
    @Schema(description = "倒计时秒数")
    private Integer countdownSeconds;

    /**
     * 是否显示排名
     */
    @Schema(description = "是否显示排名")
    private Boolean isShowRank;

    /**
     * 活动头部图片
     */
    @Schema(description = "活动头部图片")
    private String headImg;

    /**
     * 1:启用,0 不启用
     */
    @Schema(description = "1:启用,0 不启用")
    private Boolean isShowPoint;

    /**
     * 是否显示标题
     */
    @Schema(description = "是否显示标题")
    private Boolean isShowTitle;

    /**
     * (去抽奖)弹窗按钮文案
     */
    @Schema(description = "(去抽奖)弹窗按钮文案")
    private String lotteryTitle;

    /**
     * 我的奖品按钮文案
     */
    @Schema(description = "我的奖品按钮文案")
    private String prizeTitle;

    /**
     * 是否启用单位榜（活动预热）
     */
    @Schema(description = "是否启用单位榜（活动预热）")
    private Boolean isShowUnitRank;

    /**
     * 是否排行榜启用“部门”字段
     */
    @Schema(description = "是否排行榜启用“部门”字段")
    private Boolean isShowDepartment;

    /**
     * 每日闯关次数，0 表示不限
     */
    @Schema(description = "每日闯关次数，0 表示不限")
    private Integer dayredotime;

    /**
     * 上传题面视频封面背景图
     */
    @Schema(description = "上传题面视频封面背景图")
    private String gameCoverPhoto;

    /**
     * 新增儿童闯关排序游戏，默认不启用
     */
    @Schema(description = "新增儿童闯关排序游戏，默认不启用")
    private Boolean isChildrenGame;

    /**
     * 是否需要填写地址 1:启用,0 不启用
     */
    @Schema(description = "是否需要填写地址 1:启用,0 不启用")
    private Boolean editAddress;

    /**
     * 闯关成功/失败弹窗配置
     */
    @Schema(description = "闯关成功/失败弹窗配置")
    private String popupConfig;

    /**
     * 扉页配置
     */
    @Schema(description = "扉页配置")
    private String titlePageImageName;

    /**
     * 拓新小程序活动图
     */
    @Schema(description = "拓新小程序活动图")
    private String appletImg;

    /**
     * 样式配置,json 格式
     */
    @Schema(description = "样式配置,json 格式")
    private String styleConfig;
}