package cn.gf.saas.module.activity.controller.admin.readingact.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.gf.saas.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * readingact 表的响应实体类
 */
@Data
public class ReadingActResp {
    @Schema(description = "活动id")
    @NotNull(message = "活动id不能为空")
    private Integer id;

    @Schema(description = "活动名称")
    @NotBlank(message = "活动名称不能为空")
    private String title;

    @Schema(description = "活动开始时间")
    @NotNull(message = "活动开始时间不能为空")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime beginTime;

    @Schema(description = "活动结束时间")
    @NotNull(message = "活动结束时间不能为空")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime endTime;

    @Schema(description = "过期时间，格式为 'yyyy-MM-dd HH:mm:ss'")
    @NotNull(message = "过期时间不能为空")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime expireTime;

    @Schema(description = "参与类型，0集团人员 1全部人员，2参与部门，3选择人员，4自主报名")
    private Integer joinType;

    @Schema(description = "是否自动签到")
    private Boolean isAutoCheckIn;

    @Schema(description = "是否开启积分 默认开启，不能关闭")
    private Boolean isOpenPoints;

    @Schema(description = "是否开启连续20天签到")
    private Boolean isOpenTwentySign;

    @Schema(description = "积分榜-用户统计排名")
    private Boolean statisticpointsUserEnabled;

    @Schema(description = "积分榜-单位统计排名")
    private Boolean statisticpointsCompanyEnabled;

    @Schema(description = "读书榜-用户排名")
    private Boolean statisticreadUserEnabled;

    @Schema(description = "读书榜-单位排名")
    private Boolean statisticreadCompanyEnabled;

    @Schema(description = "活动规则")
    private String content;

    @Schema(description = "图书分类")
    private Boolean categoryIsShow;

    @Schema(description = "阅读记录")
    private Boolean readHistoryEnabled;

    @Schema(description = "多个配置集合json字段(IsShowBookSquare是否显示书香广场入口,IsShowShare是否显示分享按钮,IsBookSquareDesensitize书香广场姓名是否脱敏)")
    @Valid
    private ConfigInfo fieldConfig;

    @Schema(description = "列表配图")
    private String titleImg;

    @Schema(description = "首页轮播图")
    private List<String> homeImg;
}    