package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnairequestion.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 调查问卷题目 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuestionnaireQuestionRespVO {

    @Schema(description = "自增id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1717")
    @ExcelProperty("自增id")
    private Integer recordId;

    @Schema(description = "问卷id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6009")
    @ExcelProperty("问卷id")
    private Integer questionnaireId;

    @Schema(description = "题目序号")
    @ExcelProperty("题目序号")
    private Integer questionIndex;

    @Schema(description = "题目类型 多选/单选/其它", example = "2")
    @ExcelProperty("题目类型 多选/单选/其它")
    private String questionType;

    @Schema(description = "题目")
    @ExcelProperty("题目")
    private String question;

    @Schema(description = "A")
    @ExcelProperty("A")
    private String answer1;

    @Schema(description = "B")
    @ExcelProperty("B")
    private String answer2;

    @Schema(description = "C")
    @ExcelProperty("C")
    private String answer3;

    @Schema(description = "D")
    @ExcelProperty("D")
    private String answer4;

    @Schema(description = "E")
    @ExcelProperty("E")
    private String answer5;

    @Schema(description = "F")
    @ExcelProperty("F")
    private String answer6;

    @Schema(description = "G")
    @ExcelProperty("G")
    private String answer7;

    @Schema(description = "H")
    @ExcelProperty("H")
    private String answer8;

    @Schema(description = "I")
    @ExcelProperty("I")
    private String answer9;

    @Schema(description = "J")
    @ExcelProperty("J")
    private String answer10;

    @Schema(description = "k")
    @ExcelProperty("k")
    private String answer11;

    @Schema(description = "l")
    @ExcelProperty("l")
    private String answer12;

    @Schema(description = "m")
    @ExcelProperty("m")
    private String answer13;

    @Schema(description = "n")
    @ExcelProperty("n")
    private String answer14;

    @Schema(description = "o")
    @ExcelProperty("o")
    private String answer15;

    @Schema(description = "p")
    @ExcelProperty("p")
    private String answer16;

    @Schema(description = "q")
    @ExcelProperty("q")
    private String answer17;

    @Schema(description = "r")
    @ExcelProperty("r")
    private String answer18;

    @Schema(description = "s")
    @ExcelProperty("s")
    private String answer19;

    @Schema(description = "t")
    @ExcelProperty("t")
    private String answer20;

    @Schema(description = "u")
    @ExcelProperty("u")
    private String answer21;

    @Schema(description = "v")
    @ExcelProperty("v")
    private String answer22;

    @Schema(description = "w")
    @ExcelProperty("w")
    private String answer23;

    @Schema(description = "x")
    @ExcelProperty("x")
    private String answer24;

    @Schema(description = "y")
    @ExcelProperty("y")
    private String answer25;

    @Schema(description = "z")
    @ExcelProperty("z")
    private String answer26;

    @Schema(description = "27")
    @ExcelProperty("27")
    private String answer27;

    @Schema(description = "28")
    @ExcelProperty("28")
    private String answer28;

    @Schema(description = "29")
    @ExcelProperty("29")
    private String answer29;

    @Schema(description = "30")
    @ExcelProperty("30")
    private String answer30;

    @Schema(description = "31")
    @ExcelProperty("31")
    private String answer31;

    @Schema(description = "32")
    @ExcelProperty("32")
    private String answer32;

    @Schema(description = "33")
    @ExcelProperty("33")
    private String answer33;

    @Schema(description = "34")
    @ExcelProperty("34")
    private String answer34;

    @Schema(description = "35")
    @ExcelProperty("35")
    private String answer35;

    @Schema(description = "36")
    @ExcelProperty("36")
    private String answer36;

    @Schema(description = "37")
    @ExcelProperty("37")
    private String answer37;

    @Schema(description = "38")
    @ExcelProperty("38")
    private String answer38;

    @Schema(description = "39")
    @ExcelProperty("39")
    private String answer39;

    @Schema(description = "40")
    @ExcelProperty("40")
    private String answer40;

    @Schema(description = "41")
    @ExcelProperty("41")
    private String answer41;

    @Schema(description = "42")
    @ExcelProperty("42")
    private String answer42;

    @Schema(description = "43")
    @ExcelProperty("43")
    private String answer43;

    @Schema(description = "44")
    @ExcelProperty("44")
    private String answer44;

    @Schema(description = "45")
    @ExcelProperty("45")
    private String answer45;

    @Schema(description = "46")
    @ExcelProperty("46")
    private String answer46;

    @Schema(description = "47")
    @ExcelProperty("47")
    private String answer47;

    @Schema(description = "48")
    @ExcelProperty("48")
    private String answer48;

    @Schema(description = "49")
    @ExcelProperty("49")
    private String answer49;

    @Schema(description = "50")
    @ExcelProperty("50")
    private String answer50;

    @Schema(description = "最多选择")
    @ExcelProperty("最多选择")
    private Integer maxoptions;

    @Schema(description = "最少选择")
    @ExcelProperty("最少选择")
    private Integer minoptions;

    @Schema(description = "答题形式", example = "1")
    @ExcelProperty("答题形式")
    private Integer answerType;

    @Schema(description = "辅助值")
    @ExcelProperty("辅助值")
    private String answerOption;

    @Schema(description = "是否需要辅助填写（填空题）")
    @ExcelProperty("是否需要辅助填写（填空题）")
    private Boolean isfilled;

    @Schema(description = "标题图片")
    @ExcelProperty("标题图片")
    private String titleImage;

    @Schema(description = "是否启用自填项配置")
    @ExcelProperty("是否启用自填项配置")
    private Boolean isEnableSelfItem;

    @Schema(description = "自填项标题")
    @ExcelProperty("自填项标题")
    private String selfItemTitle;

    @Schema(description = "自填项备注")
    @ExcelProperty("自填项备注")
    private String selfItemRemark;
}