package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnairequestion.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 调查问卷题目新增/修改 Request VO")
@Data
public class QuestionnaireQuestionSaveReqVO {

    @Schema(description = "自增id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1717")
    private Integer recordId;

    @Schema(description = "问卷id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6009")
    @NotNull(message = "问卷id不能为空")
    private Integer questionnaireId;

    @Schema(description = "题目序号")
    private Integer questionIndex;

    @Schema(description = "题目类型 多选/单选/其它/分割线/大纲", example = "2")
    private String questionType;

    @Schema(description = "题目")
    private String question;

    @Schema(description = "A")
    private String answer1;

    @Schema(description = "B")
    private String answer2;

    @Schema(description = "C")
    private String answer3;

    @Schema(description = "D")
    private String answer4;

    @Schema(description = "E")
    private String answer5;

    @Schema(description = "F")
    private String answer6;

    @Schema(description = "G")
    private String answer7;

    @Schema(description = "H")
    private String answer8;

    @Schema(description = "I")
    private String answer9;

    @Schema(description = "J")
    private String answer10;

    @Schema(description = "k")
    private String answer11;

    @Schema(description = "l")
    private String answer12;

    @Schema(description = "m")
    private String answer13;

    @Schema(description = "n")
    private String answer14;

    @Schema(description = "o")
    private String answer15;

    @Schema(description = "p")
    private String answer16;

    @Schema(description = "q")
    private String answer17;

    @Schema(description = "r")
    private String answer18;

    @Schema(description = "s")
    private String answer19;

    @Schema(description = "t")
    private String answer20;

    @Schema(description = "u")
    private String answer21;

    @Schema(description = "v")
    private String answer22;

    @Schema(description = "w")
    private String answer23;

    @Schema(description = "x")
    private String answer24;

    @Schema(description = "y")
    private String answer25;

    @Schema(description = "z")
    private String answer26;

    @Schema(description = "27")
    private String answer27;

    @Schema(description = "28")
    private String answer28;

    @Schema(description = "29")
    private String answer29;

    @Schema(description = "30")
    private String answer30;

    @Schema(description = "31")
    private String answer31;

    @Schema(description = "32")
    private String answer32;

    @Schema(description = "33")
    private String answer33;

    @Schema(description = "34")
    private String answer34;

    @Schema(description = "35")
    private String answer35;

    @Schema(description = "36")
    private String answer36;

    @Schema(description = "37")
    private String answer37;

    @Schema(description = "38")
    private String answer38;

    @Schema(description = "39")
    private String answer39;

    @Schema(description = "40")
    private String answer40;

    @Schema(description = "41")
    private String answer41;

    @Schema(description = "42")
    private String answer42;

    @Schema(description = "43")
    private String answer43;

    @Schema(description = "44")
    private String answer44;

    @Schema(description = "45")
    private String answer45;

    @Schema(description = "46")
    private String answer46;

    @Schema(description = "47")
    private String answer47;

    @Schema(description = "48")
    private String answer48;

    @Schema(description = "49")
    private String answer49;

    @Schema(description = "50")
    private String answer50;

    @Schema(description = "最多选择")
    private Integer maxoptions;

    @Schema(description = "最少选择")
    private Integer minoptions;

    @Schema(description = "答题形式,①横线填空（辅助属性：横线长度多少字） ②年份选择③年月选择④年月日选择⑤数字范围选择（辅助属性：上限，下限）⑥自定义下拉选项（辅助属性，选项之间用逗号隔开）7：文本框 8：数字输入框 9：多行文本框 10:省市区县", example = "1")
    private Integer answerType;

    @Schema(description = "辅助值")
    private String answerOption;

    @Schema(description = "是否需要辅助填写（填空题）")
    private Boolean isfilled;

    @Schema(description = "标题图片")
    private String titleImage;

    @Schema(description = "是否启用自填项配置")
    @ExcelProperty("是否启用自填项配置")
    private Boolean isEnableSelfItem;

    @Schema(description = "自填项标题")
    @ExcelProperty("自填项标题")
    private String selfItemTitle;

    @Schema(description = "自填项备注")
    @ExcelProperty("自填项备注")
    private String selfItemRemark;

}