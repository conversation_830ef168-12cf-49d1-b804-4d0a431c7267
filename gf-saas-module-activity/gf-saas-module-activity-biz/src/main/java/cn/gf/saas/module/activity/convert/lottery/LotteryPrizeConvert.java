package cn.gf.saas.module.activity.convert.lottery;

import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryPrizeDetailRespVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryPrizeRespVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryPrizeSaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryPrizesDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 奖项 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface LotteryPrizeConvert {

    LotteryPrizeConvert INSTANCE = Mappers.getMapper(LotteryPrizeConvert.class);

    LotteryPrizesDO convert(LotteryPrizeSaveReqVO bean);

    LotteryPrizeRespVO convert(LotteryPrizesDO bean);

    LotteryPrizeDetailRespVO convertDetail(LotteryPrizesDO bean);

    List<LotteryPrizeRespVO> convertList(List<LotteryPrizesDO> list);

}
