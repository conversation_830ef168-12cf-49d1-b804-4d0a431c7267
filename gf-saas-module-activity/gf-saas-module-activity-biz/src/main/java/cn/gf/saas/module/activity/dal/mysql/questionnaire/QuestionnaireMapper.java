package cn.gf.saas.module.activity.dal.mysql.questionnaire;

import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.mybatis.core.mapper.BaseMapperX;
import cn.gf.saas.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaire.vo.QuestionnairePageReqVO;
import cn.gf.saas.module.activity.dal.dataobject.questionnaire.QuestionnaireDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 调查问卷 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("uemtraining")
public interface QuestionnaireMapper extends BaseMapperX<QuestionnaireDO> {

    default PageResult<QuestionnaireDO> selectPage(QuestionnairePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionnaireDO>()
                .betweenIfPresent(QuestionnaireDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(QuestionnaireDO::getEndTime, reqVO.getEndTime()));
    }

    @Delete("DELETE FROM questionnaireexam WHERE userid = #{userId} and questionnaireId  =  #{questionnaireId}")
    Integer deleteQuestionnaireExam(@Param("userId") String userId, @Param("questionnaireId")Integer questionnaireId);

    @Delete("DELETE FROM questionnaireregisteruser WHERE userid = #{userId} and questionnaireId  =  #{questionnaireId}")
    Integer deleteQuestionnaireRegisterUser(@Param("userId") String userId, @Param("questionnaireId")Integer questionnaireId);
}