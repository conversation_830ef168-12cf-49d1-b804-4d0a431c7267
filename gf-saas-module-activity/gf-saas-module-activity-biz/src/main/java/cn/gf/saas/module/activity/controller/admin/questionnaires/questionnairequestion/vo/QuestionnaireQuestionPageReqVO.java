package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnairequestion.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 调查问卷题目分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QuestionnaireQuestionPageReqVO extends PageParam {

    @Schema(description = "问卷id", example = "6009")
    private Integer questionnaireId;

}