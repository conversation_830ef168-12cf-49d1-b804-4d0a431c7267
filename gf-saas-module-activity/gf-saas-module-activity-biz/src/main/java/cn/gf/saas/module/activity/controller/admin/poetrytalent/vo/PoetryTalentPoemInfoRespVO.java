package cn.gf.saas.module.activity.controller.admin.poetrytalent.vo;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

@Data
@Schema(description = "诗词信息对象")
public class PoetryTalentPoemInfoRespVO {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "朝代")
    private String dynasty;

    @Schema(description = "类型ID")
    private Integer typeId;

    @Schema(description = "类型名称")
    private String typeName;

    @Schema(description = "规则")
    private String rule;

    @Schema(description = "等级")
    private Integer level;

    @Schema(description = "内容")
    private String content;
}