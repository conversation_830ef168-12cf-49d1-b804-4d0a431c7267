package cn.gf.saas.module.activity.controller.admin.readingact.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * readingactlibrary 表的请求实体类
 */
@Data
public class ReadingActBookReq extends PageParam {

    @Schema(description = "actId")
    @NotNull(message = "actId不能为空")
    private Integer actId;

    @Schema(description = "图书名称")
    @Size(max = 50, message = "图书名称长度不能超过50个字符")
    private String name;

    @Schema(description = "分类code")
    private String categoryCode;

}    