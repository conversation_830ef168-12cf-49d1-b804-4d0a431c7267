package cn.gf.saas.module.activity.controller.admin.lanternact.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 猜灯谜参与用户分页请求VO
 */
@Data
@Schema(description = "猜灯谜参与用户分页请求VO")
public class LanternActUserPageReq extends PageParam {

    /**
     * 活动ID
     */
    @Schema(description = "活动ID", example = "9554")
    private Integer actId;

    /**
     * 参与人员ID
     */
    @Schema(description = "参与人员ID", example = "3264")
    private String userId;

    /**
     * 参与时间
     */
    @Schema(description = "参与时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] joinTime;
}
