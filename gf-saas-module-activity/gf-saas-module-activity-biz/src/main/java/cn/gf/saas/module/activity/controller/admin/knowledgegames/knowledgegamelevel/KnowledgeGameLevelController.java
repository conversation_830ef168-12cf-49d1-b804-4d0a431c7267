package cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegamelevel;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameCreateReqVO;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameRespVO;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameUserRespVO;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegamelevel.vo.*;
import cn.gf.saas.module.activity.service.knowledgegame.level.KnowledgeGameLevelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 答题闯关-关卡")
@RestController
@RequestMapping("/activity/knowledgegame-level")
@Validated
public class KnowledgeGameLevelController {

    @Resource
    private KnowledgeGameLevelService knowledgeGameService;

    @PostMapping("/create")
    @Operation(summary = "答题闯关关卡-创建")
    public CommonResult<Integer> create(@Valid @RequestBody KnowledgeGameLevelCreateReqVO createReqVO) {
        return success(knowledgeGameService.createLevel(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "答题闯关关卡-更新")
    public CommonResult<Integer> update(@Valid @RequestBody KnowledgeGameLevelUpdateReqVO updateReqVO) {
        knowledgeGameService.updateLevel(updateReqVO);
        return success();
    }

    @GetMapping("/get")
    @Operation(summary = "答题闯关关卡-详情")
    public CommonResult<KnowledgeGameLevelRespVO> getDetail(Long id) {
        return success(knowledgeGameService.getDetail(id));
    }

    @GetMapping("/page")
    @Operation(summary = "答题闯关关卡-分页")
    public CommonResult<PageResult<KnowledgeGameLevelRespVO>> getPage(@Valid KnowledgeGameLevelPageReqVO pageReqVO) {
        return success(knowledgeGameService.getPage(pageReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "答题闯关关卡-删除")
    public CommonResult<Integer> delete(@Valid @RequestBody KnowledgeGameLevelDeleteReqVO deleteReqVO) {
        knowledgeGameService.deleteLevel(deleteReqVO);
        return success();
    }

}
