package cn.gf.saas.module.activity.service.lottery;

import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.gf.saas.framework.tenant.core.context.TenantContextHolder;
import cn.gf.saas.framework.web.core.util.WebFrameworkUtils;
import cn.gf.saas.module.activity.controller.admin.activity.vo.ActivityPublishReqVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryRespVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotterySaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.appuser.AppUserDO;
import cn.gf.saas.module.activity.dal.dataobject.knowledgegame.CompanyCustomActivityDO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.*;
import cn.gf.saas.module.activity.dal.mysql.knowledgegame.CompanyCustomActivityMapper;
import cn.gf.saas.module.activity.dal.mysql.lottery.*;
import cn.gf.saas.module.activity.enums.ActModelEnum;
import cn.gf.saas.module.activity.service.activity.ActivityModelService;
import cn.gf.saas.module.activity.service.appuser.AppUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.gf.saas.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.gf.saas.module.activity.enums.ErrorCodeConstants.*;

/**
 * 幸运抽奖 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LotteryServiceImpl implements ActivityModelService, LotteryService {

    @Resource
    private LotteryMapper lotteryMapper;

    @Resource
    private LotteryExtendsMapper lotteryExtendsMapper;

    @Resource
    private LotteryPrizesMapper lotteryPrizesMapper;

    @Resource
    private LotteryUserMapper lotteryUserMapper;

    @Resource
    private LotterySeedsMapper lotterySeedsMapper;

    @Resource
    private LotteryWhitelistMapper lotteryWhitelistMapper;

    @Resource
    private LotteryRecordsMapper lotteryRecordsMapper;

    @Resource
    private CompanyCustomActivityMapper companyCustomActivityMapper;

    @Resource
    private CompanyActivityUserMapper companyActivityUserMapper;

    @Resource
    private LotterySeedsService lotterySeedsService;

    @Resource
    private AppUserService appUserService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createLottery(LotterySaveReqVO createReqVO) {
        // 校验业务规则
        validateLotteryRules(createReqVO, null);

        // 1. 插入lottery表
        LotteryDO lottery = BeanUtils.toBean(createReqVO, LotteryDO.class);
        lottery.setCompanyId(TenantContextHolder.getTenantId());
        lottery.setCreatedTime(LocalDateTime.now());
        lottery.setDeleted(false);
        lottery.setJoinType(null); // 默认选择人员
        lottery.setIsLotteryAgain(false);
        
        // 设置JSON配置
        String receiveMsg = createReqVO.getReceiveMsg() != null ? createReqVO.getReceiveMsg() : "恭喜你,成功领取奖品！";
        lottery.setSettingConfig(buildSettingConfig(receiveMsg));
        
        lotteryMapper.insert(lottery);

        // 2. 插入lottery_extends表
        LotteryExtendsDO lotteryExtends = LotteryExtendsDO.builder()
                .lotteryId(lottery.getId())
                .preUserCount(createReqVO.getPreUserCount())
                .lotteryOfTotal(createReqVO.getLotteryOfTotal())
                .lotteryOfDays(createReqVO.getLotteryOfDays())
                .createdTime(LocalDateTime.now())
                .build();
        lotteryExtendsMapper.insert(lotteryExtends);

        // 3. 插入CompanyCustomActivity表
        CompanyCustomActivityDO companyCustomActivity = CompanyCustomActivityDO.builder()
                .companyId(TenantContextHolder.getTenantId())
                .actTitle(createReqVO.getName())
                .actType(3)
                .actId(String.valueOf(lottery.getId()))
                .actBeginTime(createReqVO.getBeginTime())
                .actEndTime(createReqVO.getEndTime())
                .actJoinType(3)
                .published(false)
                .build();
        companyCustomActivityMapper.insert(companyCustomActivity);

        return lottery.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLottery(LotterySaveReqVO updateReqVO) {
        // 校验存在
        validateLotteryExists(updateReqVO.getId());
        
        // 校验业务规则
        validateLotteryRules(updateReqVO, updateReqVO.getId());

        // 1. 更新lottery表 - 手动设置所有字段确保空值也能更新
        LotteryDO updateLottery = new LotteryDO();
        updateLottery.setId(updateReqVO.getId());
        updateLottery.setName(updateReqVO.getName());
        updateLottery.setBeginTime(updateReqVO.getBeginTime());
        updateLottery.setEndTime(updateReqVO.getEndTime());
        updateLottery.setReceiveBeginTime(updateReqVO.getReceiveBeginTime());
        updateLottery.setReceiveEndTime(updateReqVO.getReceiveEndTime());
        updateLottery.setIsShowAmount(updateReqVO.getIsShowAmount());
        updateLottery.setLotteryType(updateReqVO.getLotteryType());
        updateLottery.setJoinType(updateReqVO.getJoinType());
        updateLottery.setIsLiveLottery(updateReqVO.getIsLiveLottery());
        updateLottery.setInfo(updateReqVO.getInfo()); // 允许为空
        updateLottery.setLosingToast(updateReqVO.getLosingToast()); // 允许为空
        updateLottery.setSettingConfig(buildSettingConfig(updateReqVO.getReceiveMsg())); // receiveMsg允许为空
        lotteryMapper.updateById(updateLottery);

        // 当传参joinType不等于0时，删除lotteryuser和company_activity_user表的参与用户
        if (updateReqVO.getJoinType() != null && updateReqVO.getJoinType() != 0) {
            // 删除lotteryuser表数据
            lotteryUserMapper.delete(new LambdaQueryWrapperX<LotteryUserDO>()
                    .eq(LotteryUserDO::getLotteryId, updateReqVO.getId()));

            // 删除company_activity_user表数据
            CompanyCustomActivityDO companyActivity = companyCustomActivityMapper.selectByActIdAndActType(
                    String.valueOf(updateReqVO.getId()), ActModelEnum.LOTTERY.getStatus());
            if (companyActivity != null) {
                companyActivityUserMapper.delete(new LambdaQueryWrapperX<CompanyActivityUserDO>()
                        .eq(CompanyActivityUserDO::getActId, companyActivity.getId()));
            }
        }

        // 2. 更新lottery_extends表
        LotteryExtendsDO updateExtends = LotteryExtendsDO.builder()
                .lotteryId(updateReqVO.getId())
                .preUserCount(updateReqVO.getPreUserCount())
                .lotteryOfTotal(updateReqVO.getLotteryOfTotal())
                .lotteryOfDays(updateReqVO.getLotteryOfDays())
                .modifyTime(LocalDateTime.now())
                .build();
        lotteryExtendsMapper.updateById(updateExtends);

        // 3. 更新CompanyCustomActivity表
        CompanyCustomActivityDO updateActivity = CompanyCustomActivityDO.builder()
                .actTitle(updateReqVO.getName())
                .actBeginTime(updateReqVO.getBeginTime())
                .actEndTime(updateReqVO.getEndTime())
                .build();
        updateActivity.setActId(String.valueOf(updateReqVO.getId()));
        companyCustomActivityMapper.updateByActId(updateActivity);
    }

    @Override
    public LotteryDO getLottery(Integer id) {
        return lotteryMapper.selectById(id);
    }

    /**
     * 校验抽奖活动是否存在
     */
    private void validateLotteryExists(Integer id) {
        if (lotteryMapper.selectById(id) == null) {
            throw exception(LOTTERY_NOT_EXISTS);
        }
    }

    @Override
    public LotteryRespVO getLotteryDetail(Integer id) {
        LotteryDO lottery = getLottery(id);
        if (lottery == null) {
            return null;
        }

        // 获取扩展信息
        LotteryExtendsDO lotteryExtends = lotteryExtendsMapper.selectById(id);

        // 转换为响应VO
        LotteryRespVO respVO = BeanUtils.toBean(lottery, LotteryRespVO.class);

        // 设置扩展字段
        if (lotteryExtends != null) {
            respVO.setPreUserCount(lotteryExtends.getPreUserCount());
            respVO.setLotteryOfDays(lotteryExtends.getLotteryOfDays());
            respVO.setLotteryOfTotal(lotteryExtends.getLotteryOfTotal());
        }

        // 解析JSON配置获取receiveMsg
        if (lottery.getSettingConfig() != null) {
            try {
                Map<String, Object> config = objectMapper.readValue(lottery.getSettingConfig(), Map.class);
                String receiveMsg = (String) config.get("receiveMsg");
                // 保持原始值，包括空字符串
                respVO.setReceiveMsg(receiveMsg);
            } catch (JsonProcessingException e) {
                log.error("解析JSON配置失败", e);
                respVO.setReceiveMsg(null);
            }
        }

        return respVO;
    }

    /**
     * 校验业务规则
     */
    private void validateLotteryRules(LotterySaveReqVO reqVO, Integer lotteryId) {
        // 校验：每人共可抽奖 >= 每人每天可抽奖
        if (reqVO.getLotteryOfTotal() != null && reqVO.getLotteryOfDays() != null) {
            if (reqVO.getLotteryOfTotal() < reqVO.getLotteryOfDays()) {
                throw exception(LOTTERY_TOTAL_LESS_THAN_DAILY);
            }
        }

        // 更新时校验：预抽奖人数 >= 奖项数量总和
        if (lotteryId != null && reqVO.getPreUserCount() != null) {
            Integer totalPrizeCount = lotteryPrizesMapper.getTotalPrizeCount(lotteryId);
            if (reqVO.getPreUserCount() < totalPrizeCount) {
                throw exception(LOTTERY_USER_COUNT_LESS_THAN_PRIZE_COUNT);
            }
        }
    }

    /**
     * 构建JSON配置
     */
    private String buildSettingConfig(String receiveMsg) {
        Map<String, Object> config = new HashMap<>();
        // 直接使用传入的receiveMsg值，包括null和空字符串
        config.put("receiveMsg", receiveMsg);
        config.put("enableWechat", false);
        config.put("hideExpireTips", false);

        try {
            return objectMapper.writeValueAsString(config);
        } catch (JsonProcessingException e) {
            log.error("构建JSON配置失败", e);
            return "{\"receiveMsg\":null,\"enableWechat\":false,\"hideExpireTips\":false}";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setPublishStatus(ActivityPublishReqVO pageReqVO) {
        Integer id = pageReqVO.getId();
        Boolean isPublish = pageReqVO.getIsPublish();

        // 校验活动是否存在
        validateLotteryExists(id);

        if (isPublish) {
            // 发布
            publishLottery(id);
        } else {
            // 取消发布
            unpublishLottery(id);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteActivity(Integer id) {
        // 校验活动是否存在
        LotteryDO lottery = lotteryMapper.selectById(id);
        if (lottery == null) {
            throw exception(LOTTERY_NOT_EXISTS);
        }

        // 判断活动是否发布，已发布不能删除
        if (lottery.getPublishTime() != null) {
            throw exception(LOTTERY_ALREADY_PUBLISHED);
        }

        // 逻辑删除活动
        lottery.setDeleted(true);
        lotteryMapper.updateById(lottery);

        // 删除种子数据
        lotterySeedsMapper.deleteByLotteryId(id);

        // 删除CompanyCustomActivityDO数据
        companyCustomActivityMapper.deleteByActId(id);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean copyActivity(Integer id) {
        // 校验活动是否存在
        LotteryDO originalLottery = lotteryMapper.selectById(id);
        if (originalLottery == null) {
            throw exception(LOTTERY_NOT_EXISTS);
        }

        // 复制活动
        LotteryDO newLottery = BeanUtils.toBean(originalLottery, LotteryDO.class);
        newLottery.setId(null);
        newLottery.setName(subActivityName("[复制活动]" + originalLottery.getName()));
        newLottery.setPublishUserId(null);
        newLottery.setPublishTime(null);
        newLottery.setCreatedTime(LocalDateTime.now());
        lotteryMapper.insert(newLottery);

        // 复制扩展信息
        LotteryExtendsDO originalExtends = lotteryExtendsMapper.selectById(id);
        if (originalExtends != null) {
            LotteryExtendsDO newExtends = BeanUtils.toBean(originalExtends, LotteryExtendsDO.class);
            newExtends.setLotteryId(newLottery.getId());
            newExtends.setCreatedTime(LocalDateTime.now());
            newExtends.setModifyTime(null);
            newExtends.setModifyUserId(null);
            lotteryExtendsMapper.insert(newExtends);
        }

        // 复制奖项
        List<LotteryPrizesDO> originalPrizes = lotteryPrizesMapper.selectList(
                new LambdaQueryWrapperX<LotteryPrizesDO>()
                        .eq(LotteryPrizesDO::getLotteryId, id));
        for (LotteryPrizesDO originalPrize : originalPrizes) {
            LotteryPrizesDO newPrize = BeanUtils.toBean(originalPrize, LotteryPrizesDO.class);
            newPrize.setId(null);
            newPrize.setLotteryId(newLottery.getId());
            lotteryPrizesMapper.insert(newPrize);

            // 复制种子
            List<LotterySeedsDO> originalSeeds = lotterySeedsMapper.selectList(
                    new LambdaQueryWrapperX<LotterySeedsDO>()
                            .eq(LotterySeedsDO::getLotteryId, id)
                            .eq(LotterySeedsDO::getPrizesId, originalPrize.getId()));
            for (LotterySeedsDO originalSeed : originalSeeds) {
                LotterySeedsDO newSeed = BeanUtils.toBean(originalSeed, LotterySeedsDO.class);
                newSeed.setRecordId(null);
                newSeed.setLotteryId(newLottery.getId());
                newSeed.setPrizesId(newPrize.getId());
                lotterySeedsMapper.insert(newSeed);
            }
        }

        // 复制用户
        List<LotteryUserDO> originalUsers = lotteryUserMapper.selectList(
                new LambdaQueryWrapperX<LotteryUserDO>()
                        .eq(LotteryUserDO::getLotteryId, id));
        for (LotteryUserDO originalUser : originalUsers) {
            LotteryUserDO newUser = BeanUtils.toBean(originalUser, LotteryUserDO.class);
            newUser.setLotteryId(newLottery.getId());
            newUser.setUsedCount(0); // 重置使用次数
            lotteryUserMapper.insert(newUser);
        }

        // 复制白名单
        List<LotteryWhitelistDO> originalWhitelist = lotteryWhitelistMapper.selectByLotteryId(id);
        for (LotteryWhitelistDO originalWhite : originalWhitelist) {
            LotteryWhitelistDO newWhite = BeanUtils.toBean(originalWhite, LotteryWhitelistDO.class);
            newWhite.setId(null);
            newWhite.setLotteryId(newLottery.getId());
            newWhite.setCreatedTime(LocalDateTime.now());
            lotteryWhitelistMapper.insert(newWhite);
        }

        // 复制CompanyCustomActivity
        CompanyCustomActivityDO originalActivity = companyCustomActivityMapper.selectByActIdAndActType(
                String.valueOf(id), ActModelEnum.LOTTERY.getStatus());
        if (originalActivity != null) {
            CompanyCustomActivityDO newActivity = BeanUtils.toBean(originalActivity, CompanyCustomActivityDO.class);
            newActivity.setId(null);
            newActivity.setActId(String.valueOf(newLottery.getId()));
            newActivity.setActTitle(newLottery.getName());
            newActivity.setPublished(false);
            newActivity.setCreatedTime(LocalDateTime.now());
            companyCustomActivityMapper.insert(newActivity);
        }

        return true;
    }

    @Override
    public String previewActivity(Integer id) {
        // 校验活动是否存在
        validateLotteryExists(id);

        // 生成预览用户(清除数据时会清除该用户信息)
        AppUserDO userDO = new AppUserDO();
        long timestampMillis = System.currentTimeMillis();
        String account = "8" + "000" + timestampMillis;
        userDO.setAccount(account);
        userDO.setCompanyId(TenantContextHolder.getTenantId());
        userDO.setRealName("saas预览用户[幸运抽奖]");
        userDO.setMobilePhone(account);
        userDO.setUserId(UUID.randomUUID().toString());
        appUserService.createAppUser(BeanUtils.toBean(userDO, cn.gf.saas.module.activity.controller.admin.appuser.vo.AppUserSaveReqVO.class));

        // 如果是选择人员模式，添加到参与用户
        LotteryDO lottery = lotteryMapper.selectById(id);
        if (lottery.getJoinType() != null && lottery.getJoinType() == 0) {
            LotteryUserDO lotteryUser = LotteryUserDO.builder()
                    .lotteryId(id)
                    .userId(userDO.getUserId())
                    .totalCount(1)
                    .usedCount(0)
                    .build();
            lotteryUserMapper.insert(lotteryUser);
        }

        // 获得免登录链接
        String url = appUserService.LoginRedirect(userDO.getUserId(), "/v2.0/lottery/lottery?title=幸运抽奖");
        return url;
    }

    @Override
    public String previewActivityNoLogin(Integer id) {
        // 校验活动是否存在
        validateLotteryExists(id);
        return "/v2.0/lottery/lottery?title=幸运抽奖";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean ClearPreviewActivity(Integer id) {
        // 校验活动是否存在
        validateLotteryExists(id);

        // 删除预览用户
        List<AppUserDO> users = appUserService.list(new LambdaQueryWrapper<AppUserDO>()
                .eq(AppUserDO::getRealName, "saas预览用户[幸运抽奖]"));

        if (!users.isEmpty()) {
            List<String> userIds = users.stream().map(AppUserDO::getUserId).collect(Collectors.toList());

            // 删除lotteryuser数据
            lotteryUserMapper.delete(new LambdaQueryWrapperX<LotteryUserDO>()
                    .eq(LotteryUserDO::getLotteryId, id)
                    .in(LotteryUserDO::getUserId, userIds));

            // 删除lotteryrecords数据
            lotteryRecordsMapper.delete(new LambdaQueryWrapperX<LotteryRecordsDO>()
                    .eq(LotteryRecordsDO::getLotteryId, id)
                    .in(LotteryRecordsDO::getUserId, userIds));

            // 删除AppUserDO数据
            for (AppUserDO user : users) {
                appUserService.deleteAppUser(user.getRecordId());
            }
        }

        return true;
    }

    /**
     * 发布活动
     */
    private void publishLottery(Integer id) {
        // 检查是否设置奖品
        Long prizeCount = lotteryPrizesMapper.selectCount(new LambdaQueryWrapperX<LotteryPrizesDO>()
                .eq(LotteryPrizesDO::getLotteryId, id));
        if (prizeCount == 0) {
            throw exception(LOTTERY_NO_PRIZES);
        }

        LotteryDO lottery = lotteryMapper.selectById(id);

        // 如果是选择人员模式，检查是否添加参与人员
        if (lottery.getJoinType() != null && lottery.getJoinType() == 0) {
            Long userCount = lotteryUserMapper.selectCount(new LambdaQueryWrapperX<LotteryUserDO>()
                    .eq(LotteryUserDO::getLotteryId, id));
            if (userCount == 0) {
                throw exception(LOTTERY_NO_USERS);
            }
        }

        // 检查奖项面积占比
        BigDecimal totalAreaRatio = lotteryPrizesMapper.getTotalAreaRatio(id);
        if (totalAreaRatio.compareTo(BigDecimal.ZERO) != 0 && totalAreaRatio.compareTo(BigDecimal.ONE) != 0) {
            throw exception(LOTTERY_AREA_RATIO_ERROR);
        }

        // 如果面积占比之和等于1，检查奖项数是否大于等于参与人数
        if (totalAreaRatio.compareTo(BigDecimal.ONE) == 0) {
            Long userCount = lotteryUserMapper.selectCount(new LambdaQueryWrapperX<LotteryUserDO>()
                    .eq(LotteryUserDO::getLotteryId, id));
            Integer totalPrizeCount = lotteryPrizesMapper.getTotalPrizeCount(id);
            if (totalPrizeCount < userCount) {
                throw exception(LOTTERY_PRIZE_COUNT_LESS_THAN_USER_COUNT);
            }
        }

        // 更新发布信息
        LotteryDO updateLottery = new LotteryDO();
        updateLottery.setId(id);
        updateLottery.setPublishUserId(WebFrameworkUtils.getLoginUserId().toString());
        updateLottery.setPublishTime(LocalDateTime.now());
        lotteryMapper.updateById(updateLottery);

        // 重置所有种子
        lotterySeedsService.resetSeeds(id);
    }

    /**
     * 取消发布活动
     */
    private void unpublishLottery(Integer id) {
        // 检查是否已开始抽奖
        Long recordCount = lotteryRecordsMapper.selectCount(new LambdaQueryWrapperX<LotteryRecordsDO>()
                .eq(LotteryRecordsDO::getLotteryId, id));
        if (recordCount > 0) {
            throw exception(LOTTERY_ALREADY_STARTED);
        }

        // 更新发布信息为null
        LotteryDO updateLottery = new LotteryDO();
        updateLottery.setId(id);
        updateLottery.setPublishUserId(null);
        updateLottery.setPublishTime(null);
        lotteryMapper.updateById(updateLottery);
    }

    /**
     * 截取活动名称，确保不超过数据库字段长度
     */
    private String subActivityName(String name) {
        if (name == null) {
            return null;
        }
        // 假设数据库字段长度为100
        return name.length() > 100 ? name.substring(0, 100) : name;
    }
}
