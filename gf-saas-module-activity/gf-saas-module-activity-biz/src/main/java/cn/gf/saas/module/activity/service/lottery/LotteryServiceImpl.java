package cn.gf.saas.module.activity.service.lottery;

import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.framework.tenant.core.context.TenantContextHolder;
import cn.gf.saas.module.activity.controller.admin.activity.vo.ActivityPublishReqVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryRespVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotterySaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.knowledgegame.CompanyCustomActivityDO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryDO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryExtendsDO;
import cn.gf.saas.module.activity.dal.mysql.knowledgegame.CompanyCustomActivityMapper;
import cn.gf.saas.module.activity.dal.mysql.lottery.LotteryExtendsMapper;
import cn.gf.saas.module.activity.dal.mysql.lottery.LotteryMapper;
import cn.gf.saas.module.activity.dal.mysql.lottery.LotteryPrizesMapper;
import cn.gf.saas.module.activity.enums.ActModelEnum;
import cn.gf.saas.module.activity.service.activity.ActivityModelService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static cn.gf.saas.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.gf.saas.module.activity.enums.ErrorCodeConstants.*;

/**
 * 幸运抽奖 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LotteryServiceImpl implements ActivityModelService, LotteryService {

    @Resource
    private LotteryMapper lotteryMapper;

    @Resource
    private LotteryExtendsMapper lotteryExtendsMapper;

    @Resource
    private LotteryPrizesMapper lotteryPrizesMapper;

    @Resource
    private CompanyCustomActivityMapper companyCustomActivityMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createLottery(LotterySaveReqVO createReqVO) {
        // 校验业务规则
        validateLotteryRules(createReqVO, null);

        // 1. 插入lottery表
        LotteryDO lottery = BeanUtils.toBean(createReqVO, LotteryDO.class);
        lottery.setCompanyId(TenantContextHolder.getTenantId());
        lottery.setCreatedTime(LocalDateTime.now());
        lottery.setDeleted(false);
        lottery.setJoinType(null); // 默认选择人员
        lottery.setIsLotteryAgain(false);
        
        // 设置JSON配置
        String receiveMsg = createReqVO.getReceiveMsg() != null ? createReqVO.getReceiveMsg() : "恭喜你,成功领取奖品！";
        lottery.setSettingConfig(buildSettingConfig(receiveMsg));
        
        lotteryMapper.insert(lottery);

        // 2. 插入lottery_extends表
        LotteryExtendsDO lotteryExtends = LotteryExtendsDO.builder()
                .lotteryId(lottery.getId())
                .preUserCount(createReqVO.getPreUserCount())
                .lotteryOfTotal(createReqVO.getLotteryOfTotal())
                .lotteryOfDays(createReqVO.getLotteryOfDays())
                .createdTime(LocalDateTime.now())
                .build();
        lotteryExtendsMapper.insert(lotteryExtends);

        // 3. 插入CompanyCustomActivity表
        CompanyCustomActivityDO companyCustomActivity = CompanyCustomActivityDO.builder()
                .companyId(TenantContextHolder.getTenantId())
                .actTitle(createReqVO.getName())
                .actType(3)
                .actId(String.valueOf(lottery.getId()))
                .actBeginTime(createReqVO.getBeginTime())
                .actEndTime(createReqVO.getEndTime())
                .actJoinType(3)
                .published(false)
                .build();
        companyCustomActivityMapper.insert(companyCustomActivity);

        return lottery.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLottery(LotterySaveReqVO updateReqVO) {
        // 校验存在
        validateLotteryExists(updateReqVO.getId());
        
        // 校验业务规则
        validateLotteryRules(updateReqVO, updateReqVO.getId());

        // 1. 更新lottery表 - 手动设置所有字段确保空值也能更新
        LotteryDO updateLottery = new LotteryDO();
        updateLottery.setId(updateReqVO.getId());
        updateLottery.setName(updateReqVO.getName());
        updateLottery.setBeginTime(updateReqVO.getBeginTime());
        updateLottery.setEndTime(updateReqVO.getEndTime());
        updateLottery.setReceiveBeginTime(updateReqVO.getReceiveBeginTime());
        updateLottery.setReceiveEndTime(updateReqVO.getReceiveEndTime());
        updateLottery.setIsShowAmount(updateReqVO.getIsShowAmount());
        updateLottery.setLotteryType(updateReqVO.getLotteryType());
        updateLottery.setJoinType(updateReqVO.getJoinType());
        updateLottery.setIsLiveLottery(updateReqVO.getIsLiveLottery());
        updateLottery.setInfo(updateReqVO.getInfo()); // 允许为空
        updateLottery.setLosingToast(updateReqVO.getLosingToast()); // 允许为空
        updateLottery.setSettingConfig(buildSettingConfig(updateReqVO.getReceiveMsg())); // receiveMsg允许为空
        lotteryMapper.updateById(updateLottery);

        // 2. 更新lottery_extends表
        LotteryExtendsDO updateExtends = LotteryExtendsDO.builder()
                .lotteryId(updateReqVO.getId())
                .preUserCount(updateReqVO.getPreUserCount())
                .lotteryOfTotal(updateReqVO.getLotteryOfTotal())
                .lotteryOfDays(updateReqVO.getLotteryOfDays())
                .modifyTime(LocalDateTime.now())
                .build();
        lotteryExtendsMapper.updateById(updateExtends);

        // 3. 更新CompanyCustomActivity表
        CompanyCustomActivityDO updateActivity = CompanyCustomActivityDO.builder()
                .actTitle(updateReqVO.getName())
                .actBeginTime(updateReqVO.getBeginTime())
                .actEndTime(updateReqVO.getEndTime())
                .build();
        updateActivity.setActId(String.valueOf(updateReqVO.getId()));
        companyCustomActivityMapper.updateByActId(updateActivity);
    }

    @Override
    public LotteryDO getLottery(Integer id) {
        return lotteryMapper.selectById(id);
    }

    /**
     * 校验抽奖活动是否存在
     */
    private void validateLotteryExists(Integer id) {
        if (lotteryMapper.selectById(id) == null) {
            throw exception(LOTTERY_NOT_EXISTS);
        }
    }

    @Override
    public LotteryRespVO getLotteryDetail(Integer id) {
        LotteryDO lottery = getLottery(id);
        if (lottery == null) {
            return null;
        }

        // 获取扩展信息
        LotteryExtendsDO lotteryExtends = lotteryExtendsMapper.selectById(id);

        // 转换为响应VO
        LotteryRespVO respVO = BeanUtils.toBean(lottery, LotteryRespVO.class);

        // 设置扩展字段
        if (lotteryExtends != null) {
            respVO.setPreUserCount(lotteryExtends.getPreUserCount());
            respVO.setLotteryOfDays(lotteryExtends.getLotteryOfDays());
            respVO.setLotteryOfTotal(lotteryExtends.getLotteryOfTotal());
        }

        // 解析JSON配置获取receiveMsg
        if (lottery.getSettingConfig() != null) {
            try {
                Map<String, Object> config = objectMapper.readValue(lottery.getSettingConfig(), Map.class);
                String receiveMsg = (String) config.get("receiveMsg");
                // 保持原始值，包括空字符串
                respVO.setReceiveMsg(receiveMsg);
            } catch (JsonProcessingException e) {
                log.error("解析JSON配置失败", e);
                respVO.setReceiveMsg(null);
            }
        }

        return respVO;
    }

    /**
     * 校验业务规则
     */
    private void validateLotteryRules(LotterySaveReqVO reqVO, Integer lotteryId) {
        // 校验：每人共可抽奖 >= 每人每天可抽奖
        if (reqVO.getLotteryOfTotal() != null && reqVO.getLotteryOfDays() != null) {
            if (reqVO.getLotteryOfTotal() < reqVO.getLotteryOfDays()) {
                throw exception(LOTTERY_TOTAL_LESS_THAN_DAILY);
            }
        }

        // 更新时校验：预抽奖人数 >= 奖项数量总和
        if (lotteryId != null && reqVO.getPreUserCount() != null) {
            Integer totalPrizeCount = lotteryPrizesMapper.getTotalPrizeCount(lotteryId);
            if (reqVO.getPreUserCount() < totalPrizeCount) {
                throw exception(LOTTERY_USER_COUNT_LESS_THAN_PRIZE_COUNT);
            }
        }
    }

    /**
     * 构建JSON配置
     */
    private String buildSettingConfig(String receiveMsg) {
        Map<String, Object> config = new HashMap<>();
        // 直接使用传入的receiveMsg值，包括null和空字符串
        config.put("receiveMsg", receiveMsg);
        config.put("enableWechat", false);
        config.put("hideExpireTips", false);

        try {
            return objectMapper.writeValueAsString(config);
        } catch (JsonProcessingException e) {
            log.error("构建JSON配置失败", e);
            return "{\"receiveMsg\":null,\"enableWechat\":false,\"hideExpireTips\":false}";
        }
    }

    @Override
    public Boolean setPublishStatus(ActivityPublishReqVO pageReqVO) {
        return null;
    }

    @Override
    public Boolean deleteActivity(Integer id) {
        return null;
    }

    @Override
    public Boolean copyActivity(Integer id) {
        return null;
    }

    @Override
    public String previewActivity(Integer id) {
        return "";
    }

    @Override
    public String previewActivityNoLogin(Integer id) {
        return "";
    }

    @Override
    public Boolean ClearPreviewActivity(Integer id) {
        return null;
    }
}
