package cn.gf.saas.module.activity.service.lottery;

import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.framework.tenant.core.context.TenantContextHolder;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotterySaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.knowledgegame.CompanyCustomActivityDO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryDO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryExtendsDO;
import cn.gf.saas.module.activity.dal.mysql.knowledgegame.CompanyCustomActivityMapper;
import cn.gf.saas.module.activity.dal.mysql.lottery.LotteryExtendsMapper;
import cn.gf.saas.module.activity.dal.mysql.lottery.LotteryMapper;
import cn.gf.saas.module.activity.dal.mysql.lottery.LotteryPrizesMapper;
import cn.gf.saas.module.activity.enums.ActModelEnum;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static cn.gf.saas.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.gf.saas.module.activity.enums.ErrorCodeConstants.ACTIVITY_NOT_EXISTS;

/**
 * 幸运抽奖 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LotteryServiceImpl implements LotteryService {

    @Resource
    private LotteryMapper lotteryMapper;

    @Resource
    private LotteryExtendsMapper lotteryExtendsMapper;

    @Resource
    private LotteryPrizesMapper lotteryPrizesMapper;

    @Resource
    private CompanyCustomActivityMapper companyCustomActivityMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createLottery(LotterySaveReqVO createReqVO) {
        // 校验业务规则
        validateLotteryRules(createReqVO, null);

        // 1. 插入lottery表
        LotteryDO lottery = BeanUtils.toBean(createReqVO, LotteryDO.class);
        lottery.setCompanyId(TenantContextHolder.getTenantId());
        lottery.setCreatedTime(LocalDateTime.now());
        lottery.setDeleted(false);
        lottery.setJoinType(3); // 默认选择人员
        lottery.setIsLotteryAgain(false);
        
        // 设置JSON配置
        lottery.setSettingConfig(buildSettingConfig(createReqVO.getReceiveMsg()));
        
        lotteryMapper.insert(lottery);

        // 2. 插入lottery_extends表
        LotteryExtendsDO lotteryExtends = LotteryExtendsDO.builder()
                .lotteryId(lottery.getId())
                .preUserCount(createReqVO.getPreUserCount())
                .lotteryOfTotal(createReqVO.getLotteryOfTotal())
                .lotteryOfDays(createReqVO.getLotteryOfDays())
                .createdTime(LocalDateTime.now())
                .build();
        lotteryExtendsMapper.insert(lotteryExtends);

        // 3. 插入CompanyCustomActivity表
        CompanyCustomActivityDO companyCustomActivity = CompanyCustomActivityDO.builder()
                .companyId(TenantContextHolder.getTenantId())
                .actTitle(createReqVO.getName())
                .actType(ActModelEnum.LOTTERY.getStatus())
                .actId(String.valueOf(lottery.getId()))
                .actBeginTime(createReqVO.getBeginTime())
                .actEndTime(createReqVO.getEndTime())
                .actJoinType(3) // 默认选择人员
                .published(false)
                .build();
        companyCustomActivityMapper.insert(companyCustomActivity);

        return lottery.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLottery(LotterySaveReqVO updateReqVO) {
        // 校验存在
        validateLotteryExists(updateReqVO.getId());
        
        // 校验业务规则
        validateLotteryRules(updateReqVO, updateReqVO.getId());

        // 1. 更新lottery表
        LotteryDO updateLottery = BeanUtils.toBean(updateReqVO, LotteryDO.class);
        updateLottery.setSettingConfig(buildSettingConfig(updateReqVO.getReceiveMsg()));
        lotteryMapper.updateById(updateLottery);

        // 2. 更新lottery_extends表
        LotteryExtendsDO updateExtends = LotteryExtendsDO.builder()
                .lotteryId(updateReqVO.getId())
                .preUserCount(updateReqVO.getPreUserCount())
                .lotteryOfTotal(updateReqVO.getLotteryOfTotal())
                .lotteryOfDays(updateReqVO.getLotteryOfDays())
                .modifyTime(LocalDateTime.now())
                .build();
        lotteryExtendsMapper.updateById(updateExtends);

        // 3. 更新CompanyCustomActivity表
        CompanyCustomActivityDO updateActivity = CompanyCustomActivityDO.builder()
                .actTitle(updateReqVO.getName())
                .actBeginTime(updateReqVO.getBeginTime())
                .actEndTime(updateReqVO.getEndTime())
                .build();
        updateActivity.setActId(String.valueOf(updateReqVO.getId()));
        companyCustomActivityMapper.updateByActId(updateActivity);
    }

    @Override
    public LotteryDO getLottery(Integer id) {
        return lotteryMapper.selectById(id);
    }

    /**
     * 校验抽奖活动是否存在
     */
    private void validateLotteryExists(Integer id) {
        if (lotteryMapper.selectById(id) == null) {
            throw exception(ACTIVITY_NOT_EXISTS);
        }
    }

    /**
     * 校验业务规则
     */
    private void validateLotteryRules(LotterySaveReqVO reqVO, Integer lotteryId) {
        // 校验：每人共可抽奖 >= 每人每天可抽奖
        if (reqVO.getLotteryOfTotal() != null && reqVO.getLotteryOfDays() != null) {
            if (reqVO.getLotteryOfTotal() < reqVO.getLotteryOfDays()) {
                throw new IllegalArgumentException("每人共可抽奖次数不能小于每人每天可抽奖次数");
            }
        }

        // 更新时校验：预抽奖人数 >= 奖项数量总和
        if (lotteryId != null && reqVO.getPreUserCount() != null) {
            Integer totalPrizeCount = lotteryPrizesMapper.getTotalPrizeCount(lotteryId);
            if (reqVO.getPreUserCount() < totalPrizeCount) {
                throw new IllegalArgumentException("预抽奖人数不能小于奖项数量总和");
            }
        }
    }

    /**
     * 构建JSON配置
     */
    private String buildSettingConfig(String receiveMsg) {
        Map<String, Object> config = new HashMap<>();
        config.put("receiveMsg", receiveMsg != null ? receiveMsg : "恭喜你,成功领取奖品！");
        config.put("enableWechat", false);
        config.put("hideExpireTips", false);
        
        try {
            return objectMapper.writeValueAsString(config);
        } catch (JsonProcessingException e) {
            log.error("构建JSON配置失败", e);
            return "{\"receiveMsg\":\"恭喜你,成功领取奖品！\",\"enableWechat\":false,\"hideExpireTips\":false}";
        }
    }
}
