package cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "配置信息对象")
public class ConfigInfo {

    @Schema(description = "列表页-主办单位")
    @JsonProperty("OrganizerIsShow")
    private boolean OrganizerIsShow;

    @Schema(description = "列表页-答题规则")
    @JsonProperty("AnswerCount")
    private boolean AnswerCount;

    @Schema(description = "首页我的奖品按钮是否显示")
    @JsonProperty("IndexMyPrizeBtn")
    private boolean IndexMyPrizeBtn;

    @Schema(description = "首页-排行榜")
    @JsonProperty("IndexRankBtn")
    private boolean IndexRankBtn;

    @Schema(description = "首页奖品按钮是否显示")
    @JsonProperty("IndexPrizeBtn")
    private boolean IndexPrizeBtn;

    @Schema(description = "首页-活动背景")
    @JsonProperty("IndexIntroTab")
    private boolean IndexIntroTab;

    @Schema(description = "首页游戏规则标签是否显示")
    @JsonProperty("IndexGameRuleTab")
    private boolean IndexGameRuleTab;

    @Schema(description = "答题-分类题目列表 true ：隐藏  false ：显示")
    @JsonProperty("IndexTitleList")
    private boolean IndexTitleList;

    @Schema(description = "重做随机")
    @JsonProperty("RedoRandom")
    private boolean RedoRandom;

    @Schema(description = "首页-活动答题情况 true ：隐藏  false ：显示")
    @JsonProperty("IndexActAnswers")
    private boolean IndexActAnswers;

    @Schema(description = "答题-分类答题情况 true ：隐藏  false ：显示")
    @JsonProperty("ClassifyAnswers")
    private boolean ClassifyAnswers;

    @Schema(description = "答题-历史最高分")
    @JsonProperty("HighScore")
    private boolean HighScore;

    @Schema(description = "是否隐藏开奖 true ：隐藏  false ：显示")
    @JsonProperty("NoPrize")
    private boolean NoPrize;
}    