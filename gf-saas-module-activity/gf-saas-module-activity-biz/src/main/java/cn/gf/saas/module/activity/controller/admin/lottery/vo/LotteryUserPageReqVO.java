package cn.gf.saas.module.activity.controller.admin.lottery.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 幸运抽奖选择人员分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LotteryUserPageReqVO extends PageParam {

    @Schema(description = "幸运抽奖活动ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1164")
    @NotNull(message = "幸运抽奖活动ID不能为空")
    private Integer lotteryId;
}
