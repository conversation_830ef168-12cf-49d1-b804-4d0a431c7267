package cn.gf.saas.module.activity.controller.admin.staffcollege.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class StaffCollegeUserRespVO {
    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "组织")
    private String departLevel;
} 