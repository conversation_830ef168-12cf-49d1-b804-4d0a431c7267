package cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameRespVO;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameCreateReqVO;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameUpdateReqVO;
import cn.gf.saas.module.activity.service.knowledgegame.game.KnowledgeGameService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 答题闯关")
@RestController
@RequestMapping("/activity/knowledge-game")
@Validated
public class KnowledgeGameController {
    @Resource
    private KnowledgeGameService knowledgeGameService;

    @PostMapping("/create")
    @Operation(summary = "创建答题闯关")
    public CommonResult<Integer> create(@Valid @RequestBody KnowledgeGameCreateReqVO createReqVO) {
        return success(knowledgeGameService.saveKnowledge(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新答题闯关")
    public CommonResult<Integer> update(@Valid @RequestBody KnowledgeGameUpdateReqVO createReqVO) {
        knowledgeGameService.updateKnowledge(createReqVO);
        return success();
    }

    @GetMapping("/get")
    @Operation(summary = "获取答题闯关")
    public CommonResult<KnowledgeGameRespVO> getDetail(Long id) {
        return success(knowledgeGameService.getDetail(id));
    }

}
