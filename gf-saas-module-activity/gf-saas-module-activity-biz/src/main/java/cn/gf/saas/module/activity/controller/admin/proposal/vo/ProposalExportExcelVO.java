package cn.gf.saas.module.activity.controller.admin.proposal.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 职工提案 Excel 导出 VO
 */
@Data
@ExcelIgnoreUnannotated
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND)
@HeadFontStyle(fontHeightInPoints = 12, bold = BooleanEnum.TRUE)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT, verticalAlignment = VerticalAlignmentEnum.CENTER, wrapped = BooleanEnum.TRUE)
public class ProposalExportExcelVO {

    @ExcelProperty("提案议题")
    @ColumnWidth(25)
    private String title;

    @ExcelProperty("提案简介")
    @ColumnWidth(30)
    private String introduction;

    @ExcelProperty("详细内容")
    @ColumnWidth(40)
    private String description;

    @ExcelProperty("当前状态")
    @ColumnWidth(12)
    private String statusName;

    @ExcelProperty("发起时间")
    @ColumnWidth(20)
    private LocalDateTime createdTime;

    @ExcelProperty("发起人")
    @ColumnWidth(15)
    private String creatorName;

    @ExcelProperty("发起人部门")
    @ColumnWidth(20)
    private String creatorDeptName;

    @ExcelProperty("协同发起人")
    @ColumnWidth(30)
    private String collaborators;
}
