package cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameUserAddReqVO;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameUserPageReqVO;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameUserRespVO;
import cn.gf.saas.module.activity.controller.admin.quizgames.quizgameoptuser.vo.QuizGameOptUserPageReqVO;
import cn.gf.saas.module.activity.controller.admin.quizgames.quizgameoptuser.vo.QuizGameOptUserRespVO;
import cn.gf.saas.module.activity.dal.dataobject.appuser.AppUserDO;
import cn.gf.saas.module.activity.dal.dataobject.knowledgegame.KnowledgeGameUserDO;
import cn.gf.saas.module.activity.dal.dataobject.quizgameoptuser.QuizGameOptUserDO;
import cn.gf.saas.module.activity.service.appuser.AppUserService;
import cn.gf.saas.module.activity.service.knowledgegame.gameuser.KnowledgeGameUserService;
import cn.hutool.core.collection.CollUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.var;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;
import java.util.stream.Collectors;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 答题闯关-参与用户")
@RestController
@RequestMapping("/activity/knowledge-game-user")
@Validated
public class KnowledgeGameUserController {
    @Resource
    private KnowledgeGameUserService knowledgeGameUserService;

    @Resource
    private AppUserService userService;

    @PostMapping("/add")
    @Operation(summary = "答题闯关-指定人员-添加")
    public CommonResult<Integer> add(@Valid @RequestBody KnowledgeGameUserAddReqVO createReqVO) {
        knowledgeGameUserService.addKnowledgeGameUser(createReqVO);
        return success();
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除答题闯关选择人员")
    @Parameter(name = "gameId", description = "编号", required = true)
    public CommonResult<Boolean> deleteQuizGameOptUser(@RequestBody List<String> userIds,@RequestParam("gameId") Integer gameId) {
        knowledgeGameUserService.deleteKnowledgeGameUser(userIds, gameId);
        return success(true);
    }


    @GetMapping("/page")
    @Operation(summary = "获得答题闯关选择人员分页")
    public CommonResult<PageResult<KnowledgeGameUserRespVO>> getQuizGameOptUserPage(@Valid KnowledgeGameUserPageReqVO pageReqVO) {
        PageResult<KnowledgeGameUserDO> pageResult = knowledgeGameUserService.getKnowledgeGameUserPage(pageReqVO);
        PageResult<KnowledgeGameUserRespVO> list = BeanUtils.toBean(pageResult, KnowledgeGameUserRespVO.class);
        if(list.getTotal() == 0){
            return success(list);
        }
        List<AppUserDO> users = userService.getAppUsersByIds( pageResult.getList().stream().map(KnowledgeGameUserDO::getUserId).collect(Collectors.toList()));

        for (KnowledgeGameUserRespVO item:list.getList()) {
            AppUserDO user = users.stream().filter(c->c.getUserId().equals(item.getUserId())).findFirst().orElse(null);
            if(user != null) {
                item.setDepartLevel(user.getDepartLevel1());
                item.setPhone(user.getMobilePhone());
                item.setName(user.getRealName());
            }
        }
        return success(list);
    }
}
