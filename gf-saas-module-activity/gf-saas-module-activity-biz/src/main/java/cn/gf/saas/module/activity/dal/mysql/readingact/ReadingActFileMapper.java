package cn.gf.saas.module.activity.dal.mysql.readingact;

import cn.gf.saas.framework.mybatis.core.mapper.BaseMapperX;
import cn.gf.saas.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.gf.saas.module.activity.dal.dataobject.readingact.ReadingActFileDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * readingactfile 表的 Mapper 接口
 */
@Mapper
@DS("uemtraining")
public interface ReadingActFileMapper extends BaseMapperX<ReadingActFileDO> {

    /**
     * 根据actId删除
     */
    default int deleteByActId(Integer actId) {
        return delete(new LambdaQueryWrapperX<ReadingActFileDO>()
                .eq(ReadingActFileDO::getActId, actId));
    }

    /**
     * 根据actId查询
     */
    default List<ReadingActFileDO> selectListByActId(Integer actId) {
        return selectList(new LambdaQueryWrapperX<ReadingActFileDO>()
                .eq(ReadingActFileDO::getActId, actId));
    }
}