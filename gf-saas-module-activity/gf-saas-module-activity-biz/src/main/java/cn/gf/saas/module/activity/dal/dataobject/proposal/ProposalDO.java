package cn.gf.saas.module.activity.dal.dataobject.proposal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 职工提案表对应的 Java 实体类
 */
@Data
@TableName(value = "proposal", autoResultMap = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProposalDO {

    /**
     * 主键ID
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 公司ID
     */
    @TableField("CompanyId")
    private String companyId;

    /**
     * 提案议题
     */
    @TableField("Title")
    private String title;

    /**
     * 提案简介
     */
    @TableField("Introduction")
    private String introduction;

    /**
     * 详细内容
     */
    @TableField("Description")
    private String description;

    /**
     * 是否已提交
     */
    @TableField("IsSubmitted")
    private Boolean isSubmitted;

    /**
     * 提交时间
     */
    @TableField("SubmittedTime")
    private LocalDateTime submittedTime;

    /**
     * 审批时间
     */
    @TableField("ApprovalTime")
    private LocalDateTime approvalTime;

    /**
     * 当前状态
     * 已发起 = 0, 审批中 = 1, 已驳回 = 2, 已同意 = 3, 执行中 = 4, 执行结束 = 5
     */
    @TableField("CurrentStatus")
    private Integer currentStatus;

    /**
     * 发起人ID
     */
    @TableField("CreatedUserId")
    private String createdUserId;

    /**
     * 发起时间
     */
    @TableField("CreatedTime")
    private LocalDateTime createdTime;

    /**
     * 是否已删除
     */
    @TableField("Deleted")
    private Boolean deleted;

    /**
     * 审核意见
     */
    @TableField("Remark")
    private String remark;

    /**
     * 提案状态枚举
     */
    public enum Status {
        INITIATED(0, "待提交"),
        APPROVING(1, "审批中"),
        REJECTED(2, "已驳回"),
        APPROVED(3, "已同意"),
        EXECUTING(4, "执行中"),
        COMPLETED(5, "执行结束");

        private final Integer code;
        private final String name;

        Status(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static Status getByCode(Integer code) {
            for (Status status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}
