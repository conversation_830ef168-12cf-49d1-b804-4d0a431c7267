package cn.gf.saas.module.activity.controller.admin.readingact;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.idempotent.core.annotation.Idempotent;
import cn.gf.saas.framework.idempotent.core.keyresolver.impl.ExpressionIdempotentKeyResolver;
import cn.gf.saas.module.activity.controller.admin.readingact.vo.*;
import cn.gf.saas.module.activity.dal.dataobject.readingact.ReadingActLibraryDO;
import cn.gf.saas.module.activity.service.readingact.ReadingActBookService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 读书活动-图书管理")
@RestController
@RequestMapping("/activity/readingact-book")
@Validated
public class ReadingActBookController {
    @Autowired
    private ReadingActBookService readingActBookService;

    @GetMapping("/get")
    @Operation(summary = "根据图书编号获取图书信息")
    public CommonResult<ReadingActBookResp> getByShId(@RequestParam("shId") String shId, @RequestParam("type") Integer type) {
        return success(readingActBookService.getByShId(shId, type));
    }

    @GetMapping("/page")
    @Operation(summary = "获取图书信息列表分页")
    public CommonResult<PageResult<ReadingActBookResp>> page(@Valid ReadingActBookReq pageReq) {
        return success(readingActBookService.page(pageReq));
    }

    @PostMapping("/save")
    @Operation(summary = "保存图书信息")
    @Idempotent(timeout = 8, keyResolver = ExpressionIdempotentKeyResolver.class, message = "书籍添加中，请稍后再试！", keyArg = "#createReq.actId")
    public CommonResult<Boolean> save(@Valid @RequestBody ReadingActBookCreateReq createReq) {
        return success(readingActBookService.save(createReq));
    }

    @PostMapping("/update-sort")
    @Operation(summary = "更新图书排序")
    public CommonResult<Boolean> updateBookSort(@Valid @RequestBody ReadingActBookUpdateReq update) {
        return success(readingActBookService.updateBookSort(update));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "根据图书编号删除图书信息")
    public CommonResult<Boolean> delete(@Valid @RequestBody ReadingActBookDeleteReq req) {
        return success(readingActBookService.delete(req));
    }

    @GetMapping("/all-category")
    @Operation(summary = "获取所有图书分类列表")
    public CommonResult<List<ReadingActBookLibraryCategoryResp>> libraryAllCategory() {
        return success(readingActBookService.libraryAllCategory());
    }

    @GetMapping("/book/page")
    @Operation(summary = "根据关键字获取图书分页")
    public CommonResult<PageResult<ReadingActBookResp>> libraryPage(@Valid ReadingActBookLibraryReq pageReq) {
        return success(readingActBookService.libraryPage(pageReq));
    }

    @GetMapping("/book/page-by-category")
    @Operation(summary = "根据分类获取图书分页")
    public CommonResult<PageResult<ReadingActBookResp>> libraryPageByCategoryCode(@Valid ReadingActBookLibraryReq pageReq) {
        return success(readingActBookService.libraryPageByCategoryCode(pageReq));
    }

    //-----------听书---------------
    @GetMapping("/listen-book/page")
    @Operation(summary = "根据关键字获取图书分页")
    public CommonResult<PageResult<ReadingActBookResp>> libraryListenBookPage(@Valid ReadingActBookLibraryReq pageReq) {
        return success(readingActBookService.libraryListeningBookPage(pageReq));
    }

}    