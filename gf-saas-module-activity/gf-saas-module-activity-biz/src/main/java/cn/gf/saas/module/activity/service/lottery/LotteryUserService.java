package cn.gf.saas.module.activity.service.lottery;

import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryUserPageReqVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryUserRespVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryUserSaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryUserDO;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 幸运抽奖选择人员 Service 接口
 *
 * <AUTHOR>
 */
public interface LotteryUserService {

    /**
     * 批量新增选择人员
     *
     * @param createReqVO 创建信息
     * @return 是否成功
     */
    Boolean createLotteryUsers(@Valid LotteryUserSaveReqVO createReqVO);

    /**
     * 批量删除选择人员
     *
     * @param userIds，lotteryId 删除信息
     */
    void deleteLotteryUsers(List<String> userIds, Integer lotteryId);

    /**
     * 获得选择人员分页
     *
     * @param pageReqVO 分页查询
     * @return 选择人员分页
     */
    PageResult<LotteryUserRespVO> getLotteryUserPage(LotteryUserPageReqVO pageReqVO);

}
