package cn.gf.saas.module.activity.controller.admin.lanternact.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 猜灯谜活动分页查询请求VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "猜灯谜活动分页查询请求VO")
public class LanternActPageReqVO extends PageParam {

    /**
     * 活动名称
     */
    @Schema(description = "活动名称")
    private String actName;

    /**
     * 活动开始时间
     */
    @Schema(description = "活动开始时间")
    private LocalDateTime[] startTime;

    /**
     * 活动结束时间
     */
    @Schema(description = "活动结束时间")
    private LocalDateTime[] endTime;

    /**
     * 参与类型，0集团人员 1单位人员，3选择人员，6 不限人员
     */
    @Schema(description = "参与类型，0集团人员 1单位人员，3选择人员，6 不限人员")
    private Integer joinType;

    /**
     * 是否体验活动：0否 1是
     */
    @Schema(description = "是否体验活动：0否 1是")
    private Boolean isExperience;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private LocalDateTime[] publishTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime[] createTime;
}
