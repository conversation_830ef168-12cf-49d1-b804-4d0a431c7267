package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireexam;

import cn.gf.saas.framework.apilog.core.annotation.ApiAccessLog;
import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageParam;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.framework.excel.core.util.ExcelUtils;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireexam.vo.*;
import cn.gf.saas.module.activity.dal.dataobject.questionnaireexam.QuestionnaireExamDO;
import cn.gf.saas.module.activity.service.questionnaireexam.QuestionnaireExamService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.gf.saas.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 调查问卷答题")
@RestController
@RequestMapping("/activity/questionnaire-exam")
@Validated
public class QuestionnaireExamController {

    @Resource
    private QuestionnaireExamService questionnaireExamService;



    @DeleteMapping("/delete")
    @Operation(summary = "删除调查问卷答题")
    @Parameter(name = "questionnaireId", description = "编号", required = true, example = "1024")
//    @Parameter(name = "questionnaireId", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire-exam:delete')")
    public CommonResult<Boolean> deleteQuestionnaireExam(@RequestParam("questionnaireId") Integer questionnaireId,@RequestBody List<Integer> ids) {
        questionnaireExamService.deleteQuestionnaireExam(ids,questionnaireId);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得调查问卷答题分页")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire-exam:query')")
    public CommonResult<PageResult<QuestionnaireExamRespVO>> getQuestionnaireExamPage(@Valid QuestionnaireExamPageReqVO pageReqVO) {
        PageResult<QuestionnaireExamDO> pageResult = questionnaireExamService.getQuestionnaireExamPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, QuestionnaireExamRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出调查问卷答题 Excel")
//    @PreAuthorize("@ss.hasPermission('activity:questionnaire-exam:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportQuestionnaireExamExcel(@Valid QuestionnaireExamPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<QuestionnaireExamDO> list = questionnaireExamService.getQuestionnaireExamPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "调查问卷答题.xls", "数据", QuestionnaireExamRespVO.class,
                        BeanUtils.toBean(list, QuestionnaireExamRespVO.class));
    }

}