package cn.gf.saas.module.activity.service.prizes;

import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.activity.controller.admin.prizes.vo.PrizesFileVO;
import cn.gf.saas.module.activity.controller.admin.prizes.vo.PrizesPageReqVO;
import cn.gf.saas.module.activity.controller.admin.prizes.vo.PrizesRespVO;
import cn.gf.saas.module.activity.controller.admin.prizes.vo.PrizesSaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.prizes.PrizesDO;
import cn.gf.saas.module.activity.dal.dataobject.prizes.PrizescollectDO;
import cn.gf.saas.module.activity.dal.dataobject.prizes.PrizesfileDO;
import cn.gf.saas.module.activity.dal.mysql.prizes.PrizesMapper;
import cn.gf.saas.module.activity.dal.mysql.prizes.PrizescollectMapper;
import cn.gf.saas.module.activity.dal.mysql.prizes.PrizesfileMapper;
import cn.gf.saas.module.activity.dal.mysql.prizes.PrizespermissionsMapper;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.gf.saas.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.gf.saas.framework.tenant.core.context.TenantContextHolder.getTenantId;
import static cn.gf.saas.module.activity.enums.ErrorCodeConstants.*;

/**
 * 奖品 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrizesServiceImpl implements PrizesService {

    @Resource
    private PrizesMapper prizesMapper;
    @Resource
    private PrizescollectMapper prizescollectMapper;
    @Resource
    private PrizesfileMapper prizesfileMapper;

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public Integer createPrizes(PrizesSaveReqVO createReqVO) {
        // 检查活动
        PrizescollectDO prizescollectDO = prizescollectMapper.selectOneById(createReqVO.getCollectId());
        Optional.ofNullable(prizescollectDO)
                .orElseThrow(() -> exception(PRIZES_NOT_EXIST));

        // 检查奖品数量是否超过50个
        List<PrizesDO> prizesDOS = prizesMapper.selectListByCollectId(createReqVO.getCollectId());
        if (prizesDOS.size() > 50) {
            throw exception(PRIZES_NUM_ERROR);
        }

        // 插入奖品
        PrizesDO prizes = BeanUtils.toBean(createReqVO, PrizesDO.class)
                .setDeleted(false)
                .setTipContent(createReqVO.getTipContent() == null ? "" : createReqVO.getTipContent())
                .setTiptitle(createReqVO.getTiptitle() == null ? "" : createReqVO.getTiptitle())
                .setDescription(createReqVO.getDescription() == null ? "" : createReqVO.getDescription())
                .setBegintime(prizescollectDO.getBegintime())
                .setEndtime(prizescollectDO.getEndtime())
                .setBought(0)
                .setCompanyId(getTenantId())
                .setCreatedTime(LocalDateTime.now());
        prizesMapper.insert(prizes);
        
        // 处理封面图片
        if(createReqVO.getCoverImage() != null) {
            saveCoverImage(prizes.getId(), createReqVO.getCoverImage());
        }
        
        return prizes.getId();
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void updatePrizes(PrizesSaveReqVO updateReqVO) {
        // 检查活动
        PrizescollectDO prizescollectDO = prizescollectMapper.selectOneById(updateReqVO.getCollectId());
        Optional.ofNullable(prizescollectDO)
                .orElseThrow(() -> exception(PRIZES_NOT_EXIST));
        // 校验存在
        validatePrizesExists(updateReqVO.getId());
        // 更新奖品
        PrizesDO updateObj = BeanUtils.toBean(updateReqVO, PrizesDO.class)
                .setTipContent(updateReqVO.getTipContent() == null ? "" : updateReqVO.getTipContent())
                .setTiptitle(updateReqVO.getTiptitle() == null ? "" : updateReqVO.getTiptitle())
                .setDescription(updateReqVO.getDescription() == null ? "" : updateReqVO.getDescription())
                .setBegintime(prizescollectDO.getBegintime())
                .setEndtime(prizescollectDO.getEndtime());
        prizesMapper.updateById(updateObj);
        
        // 处理封面图片
        // 先删除原有的封面图片
        prizesfileMapper.deleteByPrizesId(updateReqVO.getId());
        // 保存新的封面图片
        if(updateReqVO.getCoverImage() != null) {
            saveCoverImage(updateReqVO.getId(), updateReqVO.getCoverImage());
        }
    }

    /**
     * 保存封面图片
     *
     * @param prizesId 奖品ID
     * @param coverImage 封面图片信息
     */
    private void saveCoverImage(Integer prizesId, PrizesFileVO coverImage) {
        if (coverImage != null && coverImage.getFileId() != null) {
            PrizesfileDO prizesfileDO = new PrizesfileDO();
            prizesfileDO.setPrizesId(prizesId);
            prizesfileDO.setFileId(coverImage.getFileId());
            prizesfileDO.setFileName(coverImage.getFileName());
            prizesfileDO.setFileType(coverImage.getFileType());
            prizesfileMapper.insert(prizesfileDO);
        }
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void deletePrizes(Collection<Integer> ids) {
        // 批量删除奖品
        prizesMapper.deleteByIds(ids);
        // 批量删除奖品相关的文件
        ids.forEach(id -> prizesfileMapper.deleteByPrizesId(id));
    }

    private void validatePrizesExists(Integer id) {
        if (prizesMapper.selectById(id) == null) {
            throw exception(PRIZES_NOT_EXISTS);
        }
    }

    @Override
    public PrizesRespVO getPrizes(Integer id) {
        PrizesDO prizesDO = prizesMapper.selectById(id);

        // 填充封面图片信息
        if (prizesDO != null) {
            PrizesRespVO respVO = BeanUtils.toBean(prizesDO, PrizesRespVO.class);
            PrizesfileDO file = prizesfileMapper.selectOneByPrizesId(id);
            if (file != null) {
                PrizesFileVO fileVO = new PrizesFileVO();
                fileVO.setFileId(file.getFileId());
                fileVO.setFileName(file.getFileName());
                fileVO.setFileType(file.getFileType());
                respVO.setCoverImage(fileVO);
            }
            return respVO;
        }else {
            return null;
        }
    }

    @Override
    public PageResult<PrizesRespVO> getPrizesPage(PrizesPageReqVO pageReqVO) {
        PageResult<PrizesDO> pageResult = prizesMapper.selectPage(pageReqVO);

        return BeanUtils.toBean(pageResult, PrizesRespVO.class);
    }
} 