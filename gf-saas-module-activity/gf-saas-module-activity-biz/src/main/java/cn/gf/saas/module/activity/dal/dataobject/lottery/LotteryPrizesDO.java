package cn.gf.saas.module.activity.dal.dataobject.lottery;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;

/**
 * 奖品 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lotteryprizes", autoResultMap = true)
@KeySequence("lotteryprizes_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LotteryPrizesDO {

    /**
     * 主键ID
     */
    @TableId("Id")
    private Integer id;

    /**
     * 抽奖活动ID
     */
    @TableField("lotteryId")
    private Integer lotteryId;

    /**
     * 奖项名称
     */
    @TableField("Name")
    private String name;

    /**
     * 奖项数量
     */
    @TableField("Number")
    private Integer number;

    /**
     * 奖项金额
     */
    @TableField("Amount")
    private BigDecimal amount;

    /**
     * 余额（初始为总额）
     */
    @TableField("Balance")
    private BigDecimal balance;

    /**
     * 中奖提示信息
     */
    @TableField("Toast")
    private String toast;

    /**
     * 奖项占比
     */
    @TableField("Arearatio")
    private BigDecimal areaRatio;

    /**
     * 补充奖项数量
     */
    @TableField("IncreNumber")
    private Integer increNumber;

    /**
     * 奖项类别，0虚拟券、1、实物
     */
    @TableField("Category")
    private Integer category;

    /**
     * 奖品领取活动id
     */
    @TableField("collectId")
    private Integer collectId;

    /**
     * 奖品详情
     */
    @TableField("Content")
    private String content;

    /**
     * 奖品图片,显示在抽奖转盘
     */
    @TableField("prizeImage")
    private String prizeImage;
}
