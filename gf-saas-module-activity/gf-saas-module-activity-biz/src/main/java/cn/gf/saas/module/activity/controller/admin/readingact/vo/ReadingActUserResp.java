package cn.gf.saas.module.activity.controller.admin.readingact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * readingactuser 表的响应实体类
 */
@Data
public class ReadingActUserResp {
    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "姓名", example = "吴清焕")
    private String name;

    @Schema(description = "手机号码", example = "13276711014")
    private String phone;

    @Schema(description = "组织", example = "研发一部")
    private String departLevel;
}    