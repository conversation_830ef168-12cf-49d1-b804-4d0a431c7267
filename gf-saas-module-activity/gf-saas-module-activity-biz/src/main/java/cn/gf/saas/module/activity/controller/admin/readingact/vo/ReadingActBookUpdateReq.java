package cn.gf.saas.module.activity.controller.admin.readingact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ReadingActBookUpdateReq {

    @Schema(description = "actId")
    @NotNull(message = "actId不能为空")
    private Integer actId;

    @Schema(description = "图书bookId")
    @NotNull(message = "图书bookId不能为空")
    private Long bookId;

    @Schema(description = "排序")
    @NotNull(message = "排序不能为空")
    private Integer sort;
}
