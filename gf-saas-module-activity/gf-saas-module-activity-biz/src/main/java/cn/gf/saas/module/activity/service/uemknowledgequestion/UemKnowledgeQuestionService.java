package cn.gf.saas.module.activity.service.uemknowledgequestion;

import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledgequestion.vo.UemKnowledgeQuestionPageReqVO;
import cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledgequestion.vo.UemKnowledgeQuestionSaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.uemknowledgequestion.UemKnowledgeQuestionDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
 * 题库题目 Service 接口
 *
 * <AUTHOR>
 */
public interface UemKnowledgeQuestionService extends IService<UemKnowledgeQuestionDO> {

    /**
     * 创建题库题目
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createUemKnowledgeQuestion(@Valid UemKnowledgeQuestionSaveReqVO createReqVO);

    /**
     * 更新题库题目
     *
     * @param updateReqVO 更新信息
     */
    void updateUemKnowledgeQuestion(@Valid UemKnowledgeQuestionSaveReqVO updateReqVO);

    /**
     * 删除题库题目
     *
     * @param id 编号
     */
    void deleteUemKnowledgeQuestion(Integer id);

    /**
     * 获得题库题目
     *
     * @param id 编号
     * @return 题库题目
     */
    UemKnowledgeQuestionDO getUemKnowledgeQuestion(Integer id);

    /**
     * 获得题库题目分页
     *
     * @param pageReqVO 分页查询
     * @return 题库题目分页
     */
    PageResult<UemKnowledgeQuestionDO> getUemKnowledgeQuestionPage(UemKnowledgeQuestionPageReqVO pageReqVO);

}