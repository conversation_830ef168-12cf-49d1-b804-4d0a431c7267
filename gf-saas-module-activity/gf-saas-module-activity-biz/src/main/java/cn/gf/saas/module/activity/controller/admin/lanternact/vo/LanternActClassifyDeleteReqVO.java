package cn.gf.saas.module.activity.controller.admin.lanternact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 猜灯谜分类删除请求VO
 */
@Data
@Schema(description = "猜灯谜分类删除请求VO")
public class LanternActClassifyDeleteReqVO {

    /**
     * 分类ID集合
     */
    @Schema(description = "分类ID集合", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "分类ID集合不能为空")
    private List<Integer> ids;
} 