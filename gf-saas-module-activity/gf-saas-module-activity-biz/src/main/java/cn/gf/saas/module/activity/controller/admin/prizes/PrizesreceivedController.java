package cn.gf.saas.module.activity.controller.admin.prizes;

import cn.gf.saas.framework.apilog.core.annotation.ApiAccessLog;
import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.excel.core.template.ExcelTemplateUtils;
import cn.gf.saas.framework.excel.core.util.ExcelUtils;
import cn.gf.saas.module.activity.controller.admin.prizes.vo.*;
import cn.gf.saas.module.activity.dal.dataobject.prizes.PrizesDO;
import cn.gf.saas.module.activity.service.prizes.PrizesService;
import cn.gf.saas.module.activity.service.prizes.PrizesreceivedService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;

import static cn.gf.saas.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.gf.saas.framework.common.pojo.CommonResult.success;

/**
 * 奖品领取记录 前端控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/activity/prizes-received")
@Tag(name = "管理后台 - 奖品-领取奖品")
@Validated
public class PrizesreceivedController {
    @Resource
    private PrizesreceivedService prizesreceivedService;

    @Resource
    private PrizesService prizesService;

    @GetMapping("/page")
    @Operation(summary = "领取奖品记录-分页")
    public CommonResult<PageResult<PrizesreceivedRespVO>> getPrizesreceivedPage(@Valid PrizesreceivedPageReqVO pageReqVO) {
        return success(prizesreceivedService.getPrizesreceivedPage(pageReqVO));
    }

    @GetMapping("/export")
    @Operation(summary = "导出领取奖品记录")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPrizesreceived(@Valid PrizesreceivedPageReqVO pageVO,
                                     HttpServletResponse response) throws IOException {
        // 获取奖品信息
        PrizesRespVO prizes = prizesService.getPrizes(pageVO.getPrizesId());
        String prizesName = prizes != null ? prizes.getTitle() : "未知奖品";

        // 根据isReceived参数决定导出格式
        if (pageVO.getIsReceived()) {
            // 获取已领取数据列表
            List<PrizesreceivedExcelVO> list = prizesreceivedService.getPrizesreceivedList(pageVO);
            // 导出Excel
            ExcelUtils.write(response, prizesName + "已领取名单.xls", "数据", 
                    PrizesreceivedExcelVO.class, list, prizesName + "已领取名单");
        } else {
            // 获取未领取数据列表
            List<PrizesreceivedUnreceivedExcelVO> list = prizesreceivedService.getUnreceivedPrizesreceivedList(pageVO);
            // 导出Excel
            ExcelUtils.write(response, prizesName + "未领取名单.xls", "数据", 
                    PrizesreceivedUnreceivedExcelVO.class, list, prizesName + "未领取名单");
        }
    }

    @GetMapping("/user-received")
    @Operation(summary = "领取奖品记录-重新领奖")
    public CommonResult<Boolean> userReceived(@Valid PrizesreceivedReceivedReqVO reqVO) {
        return success(prizesreceivedService.userReceived(reqVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "订单-导出")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPrizesreceivedExcel(@Valid PrizesreceivedPageReqVO pageReqVO,
                                          HttpServletResponse response) throws IOException {
        // 查询数据
        List<PrizesreceivedOrderExcelVO> list = prizesreceivedService.getPrizesreceivedOrderList(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "领取名单.xls", "数据", PrizesreceivedOrderExcelVO.class, list);
    }

    @GetMapping("/get-import-template")
    @Operation(summary = "领取奖品记录-导入模版下载")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtils.write(response, "奖品物流导入模板", "奖品物流", PrizesreceivedOrderTemplateExcelVO.class, null);
    }

    @PostMapping("/import")
    @Operation(summary = "领取奖品记录-导入")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true),
            @Parameter(name = "prizesId", description = "奖品ID", required = true)
    })
    public CommonResult<PrizesreceivedImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
                                                                @RequestParam(value = "prizesId", required = false, defaultValue = "false")
                                                                Integer prizesId) throws IOException {
        // 读取 Excel 数据
        List<PrizesreceivedOrderExcelVO> list = ExcelUtils.read(file, PrizesreceivedOrderExcelVO.class);
        // 导入数据
        return success(prizesreceivedService.importPrizesreceivedList(list, prizesId));
    }
}
