package cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegamelevel.vo.KnowledgeGameLevelPageReqVO;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegamelevel.vo.KnowledgeGameLevelRespVO;
import cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo.*;
import cn.gf.saas.module.activity.service.wisdomgoddessmanage.WisdomGoddessClassifyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.ibatis.annotations.Delete;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 智趣答题-分类")
@RestController
@RequestMapping("/activity/wisdom-goddess-classify")
@Validated
public class WisdomGoddessClassifyController {

    @Resource
    private WisdomGoddessClassifyService wisdomGoddessManageService;

    @PostMapping("/create")
    @Operation(summary = "创建")
    public CommonResult<Integer> create(@Valid @RequestBody WisdomGoddessClassifyCreateReq createReqVO) {
        return success(wisdomGoddessManageService.create(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新")
    public CommonResult update(@Valid @RequestBody WisdomGoddessClassifyUpdateReq updateReqVO) {
        wisdomGoddessManageService.update(updateReqVO);
        return success();
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除")
    public CommonResult delete(@Valid @RequestBody WisdomGoddessClassifyDeleteReq deleteReqVO) {
        wisdomGoddessManageService.delete(deleteReqVO);
        return success();
    }

    @GetMapping("/get")
    @Operation(summary = "获取详情")
    public CommonResult<WisdomGoddessClassifyResp> get(Integer id) {
        return success(wisdomGoddessManageService.getDetail(id));
    }

    @GetMapping("/page")
    @Operation(summary = "分页")
    public CommonResult<PageResult<WisdomGoddessClassifyResp>> getPage(@Valid WisdomGoddessClassifyPageReq pageReqVO) {
        return success(wisdomGoddessManageService.getPage(pageReqVO));
    }
}
