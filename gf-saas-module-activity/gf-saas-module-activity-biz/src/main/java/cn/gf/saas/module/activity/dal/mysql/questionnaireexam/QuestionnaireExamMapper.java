package cn.gf.saas.module.activity.dal.mysql.questionnaireexam;

import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.mybatis.core.mapper.BaseMapperX;
import cn.gf.saas.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaireexam.vo.QuestionnaireExamPageReqVO;
import cn.gf.saas.module.activity.dal.dataobject.appuser.AppUserDO;
import cn.gf.saas.module.activity.dal.dataobject.questionnaireexam.QuestionnaireExamDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 调查问卷答题 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("uemtraining")
public interface QuestionnaireExamMapper extends BaseMapperX<QuestionnaireExamDO> {


    default PageResult<QuestionnaireExamDO> selectPage(QuestionnaireExamPageReqVO reqVO) {
        return selectJoinPage(reqVO, QuestionnaireExamDO.class,
                new MPJLambdaWrapper<QuestionnaireExamDO>()
                        .selectAll(QuestionnaireExamDO.class)
                        .selectAs(AppUserDO::getRealName, QuestionnaireExamDO::getName)
                        .selectAs(AppUserDO::getMobilePhone, QuestionnaireExamDO::getMobilePhone)
                        .selectAs(AppUserDO::getDepartLevel1, QuestionnaireExamDO::getDepartLevel1)
                        .selectAs(AppUserDO::getCompanyId, QuestionnaireExamDO::getCompanyId)
                        .eq(QuestionnaireExamDO::getQuestionnaireId,reqVO.getQuestionnaireId())
                        .likeIfExists(AppUserDO::getRealName,reqVO.getName())
                        .likeIfExists(AppUserDO::getMobilePhone,reqVO.getMobilePhone())
                        .leftJoin(AppUserDO.class, AppUserDO::getUserId, QuestionnaireExamDO::getUserId));

//        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionnaireExamDO>()
//                .eqIfPresent(QuestionnaireExamDO::getQuestionnaireId, reqVO.getQuestionnaireId())
//                .eqIfPresent(QuestionnaireExamDO::getUserId, reqVO.getUserId())
//                .eqIfPresent(QuestionnaireExamDO::getResult, reqVO.getResult())
//                .betweenIfPresent(QuestionnaireExamDO::getCreatedTime, reqVO.getCreatedTime())
//                .orderByDesc(QuestionnaireExamDO::getId));
    }
}