package cn.gf.saas.module.activity.controller.admin.lanternact.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "猜灯谜活动配置信息对象")
public class LanternConfigInfo {

    @Schema(description = "列表页-主办单位 true 显示 false 隐藏")
    @JsonProperty("OrganizerIsShow")
    private boolean organizerIsShow;

    @Schema(description = "列表页-答题规则，true 显示 false 隐藏")
    @JsonProperty("AnswerCount")
    private boolean answerCount;

    @Schema(description = "首页-我得奖品，true 显示 false 隐藏")
    @JsonProperty("IndexMyPrizeBtn")
    private boolean indexMyPrizeBtn;

    @Schema(description = "首页-排行榜，true 显示 false 隐藏")
    @JsonProperty("IndexRankBtn")
    private boolean indexRankBtn;

    @Schema(description = "首页-领奖专区，true 显示 false 隐藏")
    @JsonProperty("IndexPrizeBtn")
    private boolean indexPrizeBtn;

    @Schema(description = "首页-活动背景，true 显示 false 隐藏")
    @JsonProperty("IndexIntroTab")
    private boolean indexIntroTab;

    @Schema(description = "首页-答题获奖规则，true 显示 false 隐藏")
    @JsonProperty("IndexGameRuleTab")
    private boolean indexGameRuleTab;

    @Schema(description = "答题-题面序号，true 显示 false 隐藏")
    @JsonProperty("IndexTitleNum")
    private boolean indexTitleNum;

    @Schema(description = "是否显示部门榜，true 隐藏 false 显示")
    @JsonProperty("HideDeptRank")
    private boolean hideDeptRank;

    @Schema(description = "答题-剩余次数，true 隐藏 false 显示")
    @JsonProperty("HideResidueTimes")
    private boolean hideResidueTimes;
}
