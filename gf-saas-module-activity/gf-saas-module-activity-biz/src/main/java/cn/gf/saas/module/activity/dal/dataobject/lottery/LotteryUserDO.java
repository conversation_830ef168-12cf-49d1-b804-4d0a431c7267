package cn.gf.saas.module.activity.dal.dataobject.lottery;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

/**
 * 参与用户 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lotteryuser", autoResultMap = true)
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LotteryUserDO {

    /**
     * 抽奖活动ID
     */
    @TableId("lotteryId")
    private Integer lotteryId;

    /**
     * 用户ID
     */
    @TableField("userId")
    private String userId;

    /**
     * 抽奖次数
     */
    @TableField("TotalCount")
    private Integer totalCount;

    /**
     * 使用次数
     */
    @TableField("UsedCount")
    private Integer usedCount;
}
