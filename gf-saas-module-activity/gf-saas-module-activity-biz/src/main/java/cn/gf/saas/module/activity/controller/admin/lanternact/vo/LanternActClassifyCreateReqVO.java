package cn.gf.saas.module.activity.controller.admin.lanternact.vo;

import cn.gf.saas.module.activity.controller.admin.lanternact.vo.LanternRandomSettingModel;
import cn.gf.saas.module.activity.dal.dataobject.activity.RandomSettingModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 猜灯谜分类创建请求VO
 */
@Data
@Schema(description = "猜灯谜分类创建请求VO")
public class LanternActClassifyCreateReqVO {

    /**
     * 关联猜灯谜活动ID
     */
    @Schema(description = "关联猜灯谜活动ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联猜灯谜活动ID不能为空")
    private Integer actId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    private String classifyName;

    /**
     * banner图，底图
     */
    @Schema(description = "banner图，底图")
    private String bannerUrl;


    /**
     * 灯谜总数
     */
    @Schema(description = "灯谜总数")
    private Integer questionCount;

    /**
     * 答题限时（秒）
     */
    @Schema(description = "答题限时（秒）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "答题限时不能为空")
    private Integer limitMinutes;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 是否是随机题
     */
    @Schema(description = "是否是随机题")
    private Boolean isRandom;

    /**
     * 题库Ids
     */
    @Schema(description = "题库Ids")
    private List<Integer> libknowledgeIds;

    /**
     * 随机题设置
     */
    @Schema(description = "随机题设置")
    private List<RandomSettingModel> randomSetting;

    /**
     * 重猜次数
     */
    @Schema(description = "重猜次数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "重猜次数不能为空")
    private Integer redoCount;

    /**
     * 显示答案方式 true 不显示正确答案 false 显示正确答案
     */
    @Schema(description = "显示答案方式 true 不显示正确答案 false 显示正确答案")
    private Boolean notdisplayanswer;

    /**
     * 快速进入下一题
     */
    @Schema(description = "快速进入下一题")
    private Boolean quicknext;

    /**
     * 倒计时秒数
     */
    @Schema(description = "倒计时秒数")
    private Integer countdownSeconds;
}
