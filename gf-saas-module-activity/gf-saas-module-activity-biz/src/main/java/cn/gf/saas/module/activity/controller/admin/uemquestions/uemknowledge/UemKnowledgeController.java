package cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledge;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledgequestion.vo.UemKnowledgeQuestionPageReqVO;
import cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledge.vo.UemKnowledgeQuestionSimpleRespVO;
import cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledge.vo.UemKnowledgeRespVO;
import cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledge.vo.UemKnowledgeSettingRespVO;
import cn.gf.saas.module.activity.dal.dataobject.uemknowledge.UemKnowledgeDO;
import cn.gf.saas.module.activity.service.uemknowledge.UemKnowledgeService;
import cn.gf.saas.module.activity.service.uemknowledgequestion.UemKnowledgeQuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.var;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 题库")
@RestController
@RequestMapping("/activity/knowledge/uem-knowledge")
@Validated
public class UemKnowledgeController {

    @Resource
    private UemKnowledgeService uemKnowledgeService;

    @Resource
    private UemKnowledgeQuestionService uemKnowledgeQuestionService;

    @GetMapping("/get")
    @Operation(summary = "获得题库")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('knowledge:uem-knowledge:query')")
    public CommonResult<UemKnowledgeRespVO> getUemKnowledge(@RequestParam("id") Integer id) {
        UemKnowledgeDO uemKnowledge = uemKnowledgeService.getUemKnowledge(id);
        return success(BeanUtils.toBean(uemKnowledge, UemKnowledgeRespVO.class));
    }

    @GetMapping("/parent/list")
    @Operation(summary = "获得本级及上级单位题库")
    //@PreAuthorize("@ss.hasPermission('knowledge:uem-knowledge:query:parent:list')")
    public CommonResult<List<UemKnowledgeRespVO>> getUemKnowledgeList() {
        List<UemKnowledgeDO> list = uemKnowledgeService.getParentUemKnowledgePage();
        return success(BeanUtils.toBean(list, UemKnowledgeRespVO.class));
    }


    @GetMapping("/question")
    @Operation(summary = "获得常规题分页")
    //@PreAuthorize("@ss.hasPermission('knowledge:uem-knowledge:query:parent:list')")
    public CommonResult<PageResult<UemKnowledgeQuestionSimpleRespVO>> getUemKnowledgeQuestionList(@Valid UemKnowledgeQuestionPageReqVO pageReqVO) {
        var list = uemKnowledgeQuestionService.getUemKnowledgeQuestionPage(pageReqVO);
        return success(BeanUtils.toBean(list, UemKnowledgeQuestionSimpleRespVO.class));
    }
}