package cn.gf.saas.module.activity.controller.admin.poetrytalent;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.activity.controller.admin.poetrytalent.vo.PoetryTalentUserAddReqVO;
import cn.gf.saas.module.activity.controller.admin.poetrytalent.vo.PoetryTalentUserPageReqVO;
import cn.gf.saas.module.activity.controller.admin.poetrytalent.vo.PoetryTalentUserRespVO;
import cn.gf.saas.module.activity.dal.dataobject.appuser.AppUserDO;
import cn.gf.saas.module.activity.dal.dataobject.poetrytalent.ActivityScopeUsersDO;
import cn.gf.saas.module.activity.service.appuser.AppUserService;
import cn.gf.saas.module.activity.service.poetrytalent.PoetryTalentUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 诗词秀才-参与用户")
@RestController
@RequestMapping("/activity/poetry-talent-user")
@Validated
public class PoetryTalentUserController {
    @Resource
    private PoetryTalentUserService poetryTalentUserService;

    @Resource
    private AppUserService userService;

    @PostMapping("/add")
    @Operation(summary = "诗词秀才-指定人员-添加")
    public CommonResult<Integer> add(@Valid @RequestBody PoetryTalentUserAddReqVO createReqVO) {
        poetryTalentUserService.addPoetryTalentUser(createReqVO);
        return success();
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除诗词秀才选择人员")
    @Parameter(name = "gameId", description = "编号", required = true)
    public CommonResult<Boolean> deletePoetryTalentOptUser(@RequestBody List<String> userIds,@RequestParam("gameId") Integer gameId) {
        poetryTalentUserService.deletePoetryTalentUser(userIds, gameId);
        return success(true);
    }


    @GetMapping("/page")
    @Operation(summary = "获得诗词秀才选择人员分页")
    public CommonResult<PageResult<PoetryTalentUserRespVO>> getPoetryTalentOptUserPage(@Valid PoetryTalentUserPageReqVO pageReqVO) {
        PageResult<ActivityScopeUsersDO> pageResult = poetryTalentUserService.getPoetryTalentUserPage(pageReqVO);
        PageResult<PoetryTalentUserRespVO> list = BeanUtils.toBean(pageResult, PoetryTalentUserRespVO.class);
        if(list.getTotal() == 0){
            return success(list);
        }
        List<AppUserDO> users = userService.getAppUsersByIds( pageResult.getList().stream().map(ActivityScopeUsersDO::getUserId).collect(Collectors.toList()));

        for (PoetryTalentUserRespVO item:list.getList()) {
            AppUserDO user = users.stream().filter(c->c.getUserId().equals(item.getUserId())).findFirst().orElse(null);
            if(user != null) {
                item.setDepartLevel(user.getDepartLevel1());
                item.setPhone(user.getMobilePhone());
                item.setName(user.getRealName());
            }
        }
        return success(list);
    }
}
