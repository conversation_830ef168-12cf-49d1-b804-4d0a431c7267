package cn.gf.saas.module.activity.controller.admin.wisdomgoddessmanage.vo;

import cn.gf.saas.module.activity.dal.dataobject.activity.RandomSettingModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * wisdomgoddessclassify 表的请求实体类
 */
@Data
public class WisdomGoddessClassifyUpdateReq {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 关联智趣女神活动ID
     */
    @Schema(description = "关联智趣女神活动ID")
    private Integer actID;

    @Schema(description = "答题闯关id")
    private Integer gameId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 50, message = "分类名称长度不大于50个字")
    private String classifyName;

    /**
     * 题目总数
     */
    @Schema(description = "题目总数")
    private Integer questionCount;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;


    @Schema(description = "是否是随机题")
    private Boolean isRandom;

    @Schema(description = "题库Ids")
    private List<Integer> libknowledgeIds;

    @Schema(description = "随机题设置")
    private List<RandomSettingModel> randomSetting;

    @Schema(description = "答题限时")
    @NotNull(message = "答题限时不能为空")
    private Integer limitMinutes;

    @Schema(description = "重猜次数")
    @NotNull(message = "重猜次数不能为空")
    private Integer redoCount;

    @Schema(description = "显示答案方式 true 不显示正确答案 false 显示正确答案")
    private Boolean notdisplayanswer;

    @Schema(description = "快速进入下一题")
    private Boolean quicknext;

    @Schema(description = "倒计时秒数")
    private Integer countdownSeconds;
}    