package cn.gf.saas.module.activity.controller.admin.readingact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ReadingActBookCreateReq {

    @Schema(description = "actId")
    @NotNull(message = "actId不能为空")
    private Integer actId;

    @Schema(description = "类型")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @Schema(description = "图书shids")
    @NotNull(message = "shIds不能为空")
    private List<String> shIds;
}
