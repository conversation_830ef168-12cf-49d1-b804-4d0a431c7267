package cn.gf.saas.module.activity.controller.admin.poetrytalent.vo;


import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 诗词秀才关卡分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PoetryTalentLevelPageReqVO extends PageParam {

    @Schema(description = "活动ID", example = "9554")
    @NotNull(message = "活动id不能为空")
    private Integer activityId;
}
