package cn.gf.saas.module.activity.controller.admin.questionnaires.questionnaire.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文件客户端的配置
 * 不同实现的客户端，需要不同的配置，通过子类来定义
 *
 * <AUTHOR>
 */
@Data
public class RegistConfigModel {
    @JsonProperty("limitType")
    @Schema(description = "0不限制1总人数限制2性别限制", example = "0")
    private Integer limitType;
    @JsonProperty("allLimit")
    @Schema(description = "总人数限制", example = "10")
    private Integer allLimit;
    @JsonProperty("manLimit")
    @Schema(description = "男人数", example = "10")
    private Integer manLimit;
    @JsonProperty("womenLimit")
    @Schema(description = "女人数", example = "10")
    private Integer womenLimit;


}