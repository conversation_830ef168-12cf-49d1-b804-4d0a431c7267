package cn.gf.saas.module.activity.controller.admin.readingact;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameCreateReqVO;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameRespVO;
import cn.gf.saas.module.activity.controller.admin.knowledgegames.knowledgegame.vo.KnowledgeGameUpdateReqVO;
import cn.gf.saas.module.activity.controller.admin.readingact.vo.ReadingActCreateReq;
import cn.gf.saas.module.activity.controller.admin.readingact.vo.ReadingActResp;
import cn.gf.saas.module.activity.controller.admin.readingact.vo.ReadingActUpdateReq;
import cn.gf.saas.module.activity.service.readingact.ReadingActService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 读书活动")
@RestController
@RequestMapping("/activity/readingact")
@Validated
public class ReadingActController {

    @Resource
    private ReadingActService readingActService;

    @PostMapping("/create")
    @Operation(summary = "创建读书活动")
    public CommonResult<Integer> create(@Valid @RequestBody ReadingActCreateReq createReqVO) {
        return success(readingActService.create(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新读书活动")
    public CommonResult<Integer> update(@Valid @RequestBody ReadingActUpdateReq createReqVO) {
        readingActService.update(createReqVO);
        return success();
    }

    @GetMapping("/get")
    @Operation(summary = "获取读书活动")
    public CommonResult<ReadingActResp> getDetail(@RequestParam("id") Integer id) {
        return success(readingActService.getDetail(id));
    }

}
