package cn.gf.saas.module.activity.controller.admin.lanternact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 猜灯谜参与用户请求VO
 */
@Data
@Schema(description = "猜灯谜参与用户请求VO")
public class LanternActUserReq {

    /**
     * 活动ID
     */
    @Schema(description = "活动ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer actId;

    /**
     * 参与人员IDs
     */
    @Schema(description = "参与人员IDs", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> userIds;
}
