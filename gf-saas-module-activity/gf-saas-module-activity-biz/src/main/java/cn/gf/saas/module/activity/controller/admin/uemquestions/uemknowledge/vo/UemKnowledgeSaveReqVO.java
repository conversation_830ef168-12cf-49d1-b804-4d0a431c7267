package cn.gf.saas.module.activity.controller.admin.uemquestions.uemknowledge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 题库新增/修改 Request VO")
@Data
public class UemKnowledgeSaveReqVO {

    @Schema(description = "题库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20207")
    private Integer knowledgeId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "单位id", example = "23077")
    private String companyId;

    @Schema(description = "创建人")
    private String createUser;

}