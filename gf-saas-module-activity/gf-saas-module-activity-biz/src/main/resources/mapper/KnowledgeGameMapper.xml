<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gf.saas.module.activity.dal.mysql.knowledgegame.KnowledgeGameMapper">



    <select id="getReadHasacted" resultType="Long">
        select
            count(1)
        from
            readingact
        WHERE
            Id in (SELECT actId FROM readingactcheckpoint WHERE gameid = #{gameId})
            AND publishTime  is  not NULL
    </select>

    <select id="getHasmeeted" resultType="Long">
        select  count(1)  from  annualmeeting WHERE GameID = #{gameId}
    </select>

    <select id="getChinataxPoints" resultType="Long">
        SELECT count(1)
        FROM uem_imsp.chinatax_points_relation relation
        INNER JOIN uem_imsp.chinatax_points points ON relation.actId = points.id
        WHERE integralruleId = 1010 AND points.companyId = #{companyId} AND relation.objectId = #{gameId}
    </select>

    <select id="getStaffCollege" resultType="Long">
        SELECT   COUNT(1)   from  staffcollege_video  WHERE  KnowledgeGameId = #{gameId}  AND  IsDeleted=0
    </select>


</mapper>
