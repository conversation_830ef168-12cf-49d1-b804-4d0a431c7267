<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gf.saas.module.activity.dal.mysql.advice.AdviceMapper">

    <!-- 分页查询建言献策，关联用户表获取发表人姓名 -->
    <select id="selectPageWithUserName" resultType="cn.gf.saas.module.activity.controller.admin.advice.vo.AdviceRespVO">
        SELECT a.*, u.realName as publishUserName
        FROM advice a
        LEFT JOIN user u ON a.PublishUserId = u.userId
        WHERE a.Deleted = 0
        <if test="reqVO.publishUserName != null and reqVO.publishUserName != ''">
            AND u.realName LIKE CONCAT('%', #{reqVO.publishUserName}, '%')
        </if>
        <if test="reqVO.publishStartTime != null">
            AND a.PublishTime &gt;= #{reqVO.publishStartTime}
        </if>
        <if test="reqVO.publishEndTime != null">
            AND a.PublishTime &lt;= #{reqVO.publishEndTime}
        </if>
        <if test="reqVO.companyId != null">
            AND a.CompanyId = #{reqVO.companyId}
        </if>
        ORDER BY a.ReplyStatus, a.PublishTime DESC
    </select>

    <!-- 根据ID查询建言献策详情，关联用户表获取发表人姓名 -->
    <select id="selectByIdWithUserName" resultType="cn.gf.saas.module.activity.controller.admin.advice.vo.AdviceRespVO">
        SELECT a.*, u.realName as publishUserName
        FROM advice a
        LEFT JOIN user u ON a.PublishUserId = u.userId
        WHERE a.Id = #{id} AND a.Deleted = 0
    </select>

</mapper>
