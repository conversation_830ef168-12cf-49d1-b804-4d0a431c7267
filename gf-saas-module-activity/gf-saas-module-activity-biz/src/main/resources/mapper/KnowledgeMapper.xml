<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gf.saas.module.activity.dal.mysql.activity.KnowledgeMapper">

    <!-- 定义resultMap处理字段映射 -->
    <resultMap id="KnowledgeResultMap" type="cn.gf.saas.module.activity.dal.dataobject.activity.KnowledgeDO">
        <!-- 主键字段映射 -->
        <id column="knowledgeId" property="knowledgeId"/>

        <!-- 逻辑删除字段 -->
        <result column="deleted" property="deleted"/>

        <!-- 普通字段映射（数据库字段名与实体属性不一致的需明确指定） -->
        <result column="saasPublishUserId" property="publishUserId"/>
        <result column="publishTime" property="publishTime"/>
        <result column="companyId" property="companyId"/>
        <result column="joinType" property="joinType"/>
        <result column="departLevel1" property="departLevel1"/>
        <result column="startTime" property="startTime"/>
        <result column="endTime" property="endTime"/>
        <result column="validDate" property="validDate"/>
        <result column="title" property="title"/>
        <result column="titleImageName" property="titleImageName"/>
        <result column="titleImage" property="titleImage"/>
        <result column="content" property="content"/>
        <result column="userCount" property="userCount"/>
        <result column="isCreateWalfare" property="isCreateWalfare"/>
        <result column="createWalfareRemark" property="createWalfareRemark"/>
        <result column="welfareApplyId" property="welfareApplyId"/>
        <result column="limitMinutes" property="limitMinutes"/>
        <result column="isScoreHide" property="isScoreHide"/>
        <result column="isRandom" property="isRandom"/>
        <result column="libknowledgeId" property="libknowledgeId"/>

        <!-- 特殊字段：使用自定义类型处理器 -->
        <result column="randomSetting"
                property="randomSetting"
                javaType="java.util.List"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>

        <!-- 剩余字段映射（按相同规则继续补充） -->
        <result column="showScoreTime" property="showScoreTime"/>
        <result column="showRankingTime" property="showRankingTime"/>
        <result column="coverImageName" property="coverImageName"/>
        <result column="autoSaveInterval" property="autoSaveInterval"/>
        <result column="randomPapersNum" property="randomPapersNum"/>
        <result column="isAutoGradeCalc" property="isAutoGradeCalc"/>
        <result column="rankingWay" property="rankingWay"/>
        <result column="isPaused" property="isPaused"/>
        <result column="pauseNotice" property="pauseNotice"/>
        <result column="visibleRanking" property="visibleRanking"/>
        <result column="visiablePersonalRanking" property="visiablePersonalRanking"/>
        <result column="visiableTopScore" property="visiableTopScore"/>
        <result column="visiableAwards" property="visiableAwards"/>
        <result column="checkAnswerType" property="checkAnswerType"/>
        <result column="isPrizesAnswer" property="isPrizesAnswer"/>
        <result column="prizesName" property="prizesName"/>
        <result column="isInLottery" property="isInLottery"/>
        <result column="isCompletednotice" property="isCompletednotice"/>
        <result column="completednoticetitle" property="completednoticetitle"/>
        <result column="completednotice" property="completednotice"/>
        <result column="usersexlimit" property="usersexlimit"/>
        <result column="isGame" property="isGame"/>
        <result column="isShowQuestionScore" property="isShowQuestionScore"/>
        <result column="redoscore" property="redoscore"/>
        <result column="redotime" property="redotime"/>
        <result column="isEnableOathPage" property="isEnableOathPage"/>
        <result column="rankNumber" property="rankNumber"/>
        <result column="organizer" property="organizer"/>
        <result column="learningGarden" property="learningGarden"/>
        <result column="useTopScore" property="useTopScore"/>
        <result column="themeTypeId" property="themeTypeId"/>
        <result column="showRedoTime" property="showRedoTime"/>
        <result column="layerTips" property="layerTips"/>
        <result column="isUserPause" property="isUserPause"/>
        <result column="enabledCompanyUserTotal" property="enabledCompanyUserTotal"/>
        <result column="isShowLearn" property="isShowLearn"/>
        <result column="isShowExplain" property="isShowExplain"/>
        <result column="isShowInvite" property="isShowInvite"/>
        <result column="isShowJoinCount" property="isShowJoinCount"/>
        <result column="isShowDepartRank" property="isShowDepartRank"/>
        <result column="isShowIntegralRank" property="isShowIntegralRank"/>
        <result column="isShowCertificate" property="isShowCertificate"/>
        <result column="pageType" property="pageType"/>
        <result column="processImage" property="processImage"/>

        <!-- JSON类型字段使用MyBatis-Plus内置处理器 -->
        <result column="buttonConfig"
                property="buttonConfig"
                javaType="java.util.List"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="gameConfig"
                property="gameConfig"
                javaType="java.util.Map"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <!-- 查询方法 -->
    <select id="getKnowledgeByKnowledgeId" resultMap="KnowledgeResultMap">
        SELECT *
        FROM knowledge
        WHERE knowledgeId = #{knowledgeId}
    </select>

    <select id="selectKnowledgeSetting" resultType="cn.gf.saas.module.activity.service.knowledgegame.game.dto.KnowledgeSettingDTO">
        SELECT
        B.knowledgeId,
        B.questionType,
        COUNT(1) AS questionCount
        FROM knowledge A
        LEFT JOIN knowledgequestion B
        ON A.knowledgeId = B.knowledgeId
        WHERE A.deleted = 0
        <if test="knowledgeIds != null and knowledgeIds.size() > 0">
            AND B.knowledgeId IN
            <foreach item="item" collection="knowledgeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY B.knowledgeId, B.questionType
        ORDER BY B.knowledgeId
    </select>

    <!-- 直接更新randomSetting字段为指定的JSON字符串 -->
    <update id="updateRandomSettingJson">
        UPDATE knowledge
        SET randomSetting = #{randomSettingJson}
        WHERE knowledgeId = #{knowledgeId}
    </update>

</mapper>
