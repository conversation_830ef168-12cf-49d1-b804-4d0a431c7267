<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gf.saas.module.activity.dal.mysql.activity.ActivityMapper">

<!--    <resultMap id="BaseResultMap" type="cn.gf.saas.module.activity.dal.dataobject.activity.KnowledgeDO">-->
<!--        <id property="knowledgeId" column="knowledgeId" jdbcType="INTEGER"/>-->
<!--        <result property="deleted" column="deleted" jdbcType="TINYINT"/>-->
<!--        <result property="publishUserId" column="publishUserId" jdbcType="VARCHAR"/>-->
<!--        <result property="publishTime" column="publishTime" jdbcType="TIMESTAMP"/>-->
<!--        <result property="companyId" column="companyId" jdbcType="VARCHAR"/>-->
<!--        <result property="joinType" column="joinType" jdbcType="SMALLINT"/>-->
<!--        <result property="departLevel1" column="departLevel1" jdbcType="VARCHAR"/>-->
<!--        <result property="startTime" column="startTime" jdbcType="TIMESTAMP"/>-->
<!--        <result property="endTime" column="endTime" jdbcType="TIMESTAMP"/>-->
<!--        <result property="validDate" column="ValidDate" jdbcType="TIMESTAMP"/>-->
<!--        <result property="title" column="title" jdbcType="VARCHAR"/>-->
<!--        <result property="titleImageName" column="titleImageName" jdbcType="VARCHAR"/>-->
<!--        <result property="titleImage" column="titleImage" jdbcType="VARCHAR"/>-->
<!--        <result property="content" column="content" jdbcType="VARCHAR"/>-->
<!--        <result property="userCount" column="userCount" jdbcType="INTEGER"/>-->
<!--        <result property="isCreateWalfare" column="IsCreateWalfare" jdbcType="TINYINT"/>-->
<!--        <result property="createWalfareRemark" column="CreateWalfareRemark" jdbcType="VARCHAR"/>-->
<!--        <result property="welfareApplyId" column="WelfareApplyId" jdbcType="VARCHAR"/>-->
<!--        <result property="limitMinutes" column="limitMinutes" jdbcType="INTEGER"/>-->
<!--        <result property="isScoreHide" column="IsScoreHide" jdbcType="TINYINT"/>-->
<!--        <result property="isRandom" column="IsRandom" jdbcType="TINYINT"/>-->
<!--        <result property="libknowledgeId" column="LibknowledgeId" jdbcType="VARCHAR"/>-->
<!--        <result property="randomSetting" column="RandomSetting" jdbcType="VARCHAR"/>-->
<!--        <result property="showScoreTime" column="ShowScoreTime" jdbcType="TIMESTAMP"/>-->
<!--        <result property="showRankingTime" column="ShowRankingTime" jdbcType="TIMESTAMP"/>-->
<!--        <result property="coverImageName" column="CoverImageName" jdbcType="VARCHAR"/>-->
<!--        <result property="autoSaveInterval" column="AutoSaveInterval" jdbcType="INTEGER"/>-->
<!--        <result property="randomPapersNum" column="RandomPapersNum" jdbcType="INTEGER"/>-->
<!--        <result property="isAutoGradeCalc" column="IsAutoGradeCalc" jdbcType="TINYINT"/>-->
<!--        <result property="rankingWay" column="RankingWay" jdbcType="INTEGER"/>-->
<!--        <result property="isPaused" column="IsPaused" jdbcType="TINYINT"/>-->
<!--        <result property="pauseNotice" column="PauseNotice" jdbcType="VARCHAR"/>-->
<!--        <result property="visibleRanking" column="VisibleRanking" jdbcType="TINYINT"/>-->
<!--        <result property="visiablePersonalRanking" column="VisiablePersonalRanking" jdbcType="TINYINT"/>-->
<!--        <result property="visiableTopScore" column="VisiableTopScore" jdbcType="TINYINT"/>-->
<!--        <result property="visiableAwards" column="VisiableAwards" jdbcType="TINYINT"/>-->
<!--        <result property="checkAnswerTime" column="CheckAnswerTime" jdbcType="TINYINT"/>-->
<!--        <result property="checkAnswerType" column="CheckAnswerType" jdbcType="TINYINT"/>-->
<!--        <result property="isPrizesAnswer" column="IsPrizesAnswer" jdbcType="BIT"/>-->
<!--        <result property="prizesName" column="PrizesName" jdbcType="VARCHAR"/>-->
<!--        <result property="isInLottery" column="IsInLottery" jdbcType="BIT"/>-->
<!--        <result property="isCompletednotice" column="IsCompletednotice" jdbcType="TINYINT"/>-->
<!--        <result property="completednoticetitle" column="completednoticetitle" jdbcType="VARCHAR"/>-->
<!--        <result property="completednotice" column="completednotice" jdbcType="VARCHAR"/>-->
<!--        <result property="usersexlimit" column="usersexlimit" jdbcType="VARCHAR"/>-->
<!--        <result property="isGame" column="isGame" jdbcType="BIT"/>-->
<!--        <result property="isShowQuestionScore" column="isShowQuestionScore" jdbcType="BIT"/>-->
<!--        <result property="redoscore" column="redoscore" jdbcType="INTEGER"/>-->
<!--        <result property="redotime" column="redotime" jdbcType="INTEGER"/>-->
<!--        <result property="isEnableOathPage" column="IsEnableOathPage" jdbcType="BIT"/>-->
<!--        <result property="rankNumber" column="rankNumber" jdbcType="INTEGER"/>-->
<!--        <result property="organizer" column="Organizer" jdbcType="VARCHAR"/>-->
<!--        <result property="learningGarden" column="LearningGarden" jdbcType="VARCHAR"/>-->
<!--        <result property="useTopScore" column="useTopScore" jdbcType="BIT"/>-->
<!--        <result property="themeTypeId" column="themeTypeId" jdbcType="SMALLINT"/>-->
<!--        <result property="showRedoTime" column="showRedoTime" jdbcType="TINYINT"/>-->
<!--        <result property="layerTips" column="layerTips" jdbcType="VARCHAR"/>-->
<!--        <result property="isUserPause" column="IsUserPause" jdbcType="BIT"/>-->
<!--        <result property="enabledCompanyUserTotal" column="EnabledCompanyUserTotal" jdbcType="BIT"/>-->
<!--        <result property="isShowLearn" column="IsShowLearn" jdbcType="BIT"/>-->
<!--        <result property="isShowExplain" column="IsShowExplain" jdbcType="BIT"/>-->
<!--        <result property="isShowInvite" column="IsShowInvite" jdbcType="BIT"/>-->
<!--        <result property="isShowJoinCount" column="IsShowJoinCount" jdbcType="BIT"/>-->
<!--        <result property="isShowDepartRank" column="IsShowDepartRank" jdbcType="BIT"/>-->
<!--        <result property="isShowIntegralRank" column="IsShowIntegralRank" jdbcType="BIT"/>-->
<!--        <result property="isShowCertificate" column="IsShowCertificate" jdbcType="BIT"/>-->
<!--        <result property="pageType" column="pageType" jdbcType="INTEGER"/>-->
<!--        <result property="processImage" column="processImage" jdbcType="VARCHAR"/>-->
<!--        <result property="buttonConfig" column="buttonConfig" jdbcType="VARCHAR"/>-->
<!--        <result property="gameConfig" column="gameConfig" jdbcType="VARCHAR"/>-->
<!--    </resultMap>-->

<!--    <sql id="Base_Column_List">-->
<!--        knowledgeId,deleted,publishUserId,-->
<!--        publishTime,companyId,joinType,-->
<!--        departLevel1,startTime,endTime,-->
<!--        ValidDate,title,titleImageName,-->
<!--        titleImage,content,userCount,-->
<!--        IsCreateWalfare,CreateWalfareRemark,WelfareApplyId,-->
<!--        limitMinutes,IsScoreHide,IsRandom,-->
<!--        LibknowledgeId,RandomSetting,ShowScoreTime,-->
<!--        ShowRankingTime,CoverImageName,AutoSaveInterval,-->
<!--        RandomPapersNum,IsAutoGradeCalc,RankingWay,-->
<!--        IsPaused,PauseNotice,VisibleRanking,-->
<!--        VisiablePersonalRanking,VisiableTopScore,VisiableAwards,-->
<!--        CheckAnswerTime,CheckAnswerType,IsPrizesAnswer,-->
<!--        PrizesName,IsInLottery,IsCompletednotice,-->
<!--        completednoticetitle,completednotice,usersexlimit,-->
<!--        isGame,isShowQuestionScore,redoscore,-->
<!--        redotime,IsEnableOathPage,rankNumber,-->
<!--        Organizer,LearningGarden,useTopScore,-->
<!--        themeTypeId,showRedoTime,layerTips,-->
<!--        IsUserPause,EnabledCompanyUserTotal,IsShowLearn,-->
<!--        IsShowExplain,IsShowInvite,IsShowJoinCount,-->
<!--        IsShowDepartRank,IsShowIntegralRank,IsShowCertificate,-->
<!--        pageType,processImage,buttonConfig,-->
<!--        gameConfig-->
<!--    </sql>-->

    <select id="getActivityPage" resultType="cn.gf.saas.module.activity.controller.admin.activity.vo.ActivityRespVO">
        SELECT a.* from (
        <if test="reqVO.type == null or reqVO.type.status == 1">
            <!--    知识竞赛    -->
            <include refid="knowledge"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 3">
            <!--    调查问卷    -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="questionnaire"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 2">
            <!--    答题PK    -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="pkgame"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 4">
            <!--    答题闯关    -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="knowledgegame"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 5">
            <!--    智趣答题    -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="wisdomgoddess"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 6">
            <!--    读书活动    -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="readingactivity"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 7">
            <!--    诗词秀才     -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="poetrytalent"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 8">
            <!--    投票调查     -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="vote"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 9">
            <!--    奖品领取     -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="prizes"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 10">
            <!--    学习园地     -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="learning_garden"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 11">
            <!--    职工学院     -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="staffcollege"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 12">
            <!--    猜灯谜     -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="lantern"/>
        </if>

        ) a
        where 1=1
        <if test="reqVO.ids!=null and reqVO.ids.size>0">
            AND a.id IN
            <foreach item="item" collection="reqVO.ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.status != null">
            and a.status=#{reqVO.status}
        </if>
        <if test="reqVO.type != null">
            and a.model=#{reqVO.type}
        </if>
        <if test="reqVO.igStatus!=null and reqVO.igStatus.size>0">
            AND a.status NOT IN
            <foreach item="item" collection="reqVO.igStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        and a.model in (select type from gf_saas.activity_market_application where deleted=0 and enable=1)
        ORDER BY
            CASE a.status
                WHEN 'UN_PUBLISHED' THEN 1
                WHEN 'UN_STARTED' THEN 2
                WHEN 'STARTED' THEN 3
                WHEN 'FINISHED' THEN 4
                ELSE 5
            END,
            a.id DESC
        LIMIT #{reqVO.pageNo},#{reqVO.pageSize}
    </select>

    <select id="getActivityPageCount" resultType="Long">
        SELECT count(1) from (
        <if test="reqVO.type == null or reqVO.type.status == 1">
            <!--    知识竞赛    -->
            <include refid="knowledge"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 3">
            <!--    调查问卷    -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="questionnaire"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 2">
            <!--    答题PK    -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="pkgame"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 4">
            <!--    答题闯关    -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="knowledgegame"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 5">
            <!--    智趣答题    -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="wisdomgoddess"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 6">
            <!--    读书活动    -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="readingactivity"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 7">
            <!--    诗词秀才     -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="poetrytalent"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 8">
            <!--    投票调查     -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="vote"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 9">
            <!--    奖品领取     -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="prizes"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 10">
            <!--    学习园地     -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="learning_garden"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 11">
            <!--    职工学院     -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="staffcollege"/>
        </if>

        <if test="reqVO.type == null or reqVO.type.status == 12">
            <!--    猜灯谜     -->
            <if test="reqVO.type == null">
                UNION ALL
            </if>
            <include refid="lantern"/>
        </if>

        ) a where 1=1
        <if test="reqVO.status != null">
            and a.status=#{reqVO.status}
        </if>
        <if test="reqVO.type != null">
            and a.model=#{reqVO.type}
        </if>
        <if test="reqVO.igStatus!=null and reqVO.igStatus.size>0">
            AND a.status NOT IN
            <foreach item="item" collection="reqVO.igStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and a.model in (select type from gf_saas.activity_market_application where deleted=0 and enable=1)
    </select>

    <select id="getPoetrytalentUserCounts" resultType="cn.gf.saas.module.activity.controller.admin.activity.vo.ActivityUserCountDTO">
        SELECT
        e.act_id AS actId,
        COUNT(DISTINCT e.user_id) AS userCount
        FROM
        uem_imsp.activity_scope_users e
        WHERE
        e.act_id IN
        <foreach item="actId" collection="actIds" open="(" separator="," close=")">
            #{actId}
        </foreach>
        and
        e.user_id IN
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
        GROUP BY
        e.act_id
    </select>

    <!--   猜灯谜     -->
    <sql id="lantern">
        SELECT
            Id,
            'LANTERN' AS model,
            ActName AS title,
            0 AS number,
            StartTime as startTime,
            EndTime as endTime,
            CompanyId as companyId,
            PublishTime as publishTime,
            CASE
                WHEN PublishTime IS NULL THEN
                    'UN_PUBLISHED'
                WHEN StartTime > now()
                    AND PublishTime IS NOT NULL THEN
                    'UN_STARTED'
                WHEN now() >= StartTime
                    AND EndTime >= now()
                    AND PublishTime IS NOT NULL THEN
                    'STARTED'
                WHEN now() > EndTime
                    AND PublishTime IS NOT NULL THEN
                    'FINISHED'
                END AS STATUS,
            '' AS bannerUrl
        FROM
            uemtraining.lanternact
        WHERE
            Deleted = 0
          AND CompanyId = #{companyId}
    </sql>

    <!--   职工学院     -->
    <sql id="staffcollege">
        SELECT
        CourseId AS Id,
        'STAFFCOLLEGE' AS model,
        CourseName AS title,
        0 AS number,
        StartTime as startTime,
        EndTime as endTime,
        CompanyId as companyId,
        null as publishTime,
        CASE

        WHEN StartTime > now() THEN
        'UN_STARTED'
        WHEN now() >= StartTime
        AND EndTime >= now() THEN
        'STARTED'
        WHEN now() > EndTime THEN
        'FINISHED'
        END AS STATUS,
        '' AS bannerUrl
        FROM
        uemtraining.staffcollege
        WHERE
        IsDeleted = 0
        AND CompanyId = #{companyId}
    </sql>

    <!--   学习园地     -->
    <sql id="learning_garden">
        SELECT
        noticeId AS Id,
        'LEARNINGGARDEN' AS model,
        title AS title,
        0 AS number,
        null as startTime,
        null as endTime,
        companyId,
        publishTime,
        CASE

        WHEN publishTime IS NULL THEN
        'UN_PUBLISHED'
        WHEN publishTime IS NOT NULL THEN
        'STARTED'
        END AS STATUS,
        '' AS bannerUrl
        FROM
        uemtraining.notice
        WHERE
        noticeType = 2 
        AND deleted = 0
        AND companyId = #{companyId}
    </sql>

    <!--   奖品领取     -->
    <sql id="prizes">
        SELECT
        Id,
        'PRIZES' AS model,
        title AS title,
        0 AS number,
        prizescollect.begintime as startTime,
        prizescollect.endtime as endTime,
        companyId,
        null as publishTime,
        CASE

        WHEN begintime > now() THEN
        'UN_STARTED'
        WHEN now() >= begintime
        AND endtime >= now() THEN
        'STARTED'
        WHEN now() > endtime THEN
        'FINISHED'
        END AS STATUS,
        '' AS bannerUrl
        FROM
        uemtraining.prizescollect
        WHERE
        deleted = 0
        AND companyId = #{companyId}
    </sql>


    <!--   投票调查     -->
    <sql id="vote">
        SELECT
        surveyId AS Id,
        'VOTE' AS model,
        title AS title,
        0 AS number,
        startTime,
        endTime,
        companyId,
        publishTime,
        CASE

        WHEN publishTime IS NULL THEN
        'UN_PUBLISHED'
        WHEN startTime > now()
        AND publishTime IS NOT NULL THEN
        'UN_STARTED'
        WHEN now() >= startTime
        AND endTime >= now()
        AND publishTime IS NOT NULL THEN
        'STARTED'
        WHEN now() > endTime
        AND publishTime IS NOT NULL THEN
        'FINISHED'
        END AS STATUS,
        '' AS bannerUrl
        FROM
        uemtraining.survey
        WHERE
        deleted = 0
        AND companyId = #{companyId}
    </sql>

    <!--   诗词秀才     -->
    <sql id="poetrytalent">
        SELECT
        ac.ext_id AS Id,
        'POETRYTALENT' AS model,
        ac.title AS title,
        0 AS number,
        ac.start_time as startTime,
        ac.end_time as endTime,
        ac.companyId,
        ac.publish_time as publishTime,
        CASE
        WHEN ac.publish_time IS NULL THEN 'UN_PUBLISHED'
        WHEN ac.start_time > now() AND ac.publish_time IS NOT NULL THEN 'UN_STARTED'
        WHEN now() >= ac.start_time AND ac.end_time >= now() AND ac.publish_time IS NOT NULL THEN 'STARTED'
        WHEN now() > ac.end_time AND ac.publish_time IS NOT NULL THEN 'FINISHED'
        END AS STATUS,
        '' AS bannerUrl
        FROM
        uem_imsp.activity_compontents ac
        WHERE
        ac.deleted = 0
        AND ac.companyId = #{companyId}
    </sql>

    <!--   读书活动     -->
    <sql id="readingactivity">
        SELECT
        id AS Id,
        'READINGACTIVITY' AS model,
        title AS title,
        0 AS number,
        beginTime as startTime,
        endTime,
        companyId,
        publishTime,
        CASE

        WHEN publishTime IS NULL THEN
        'UN_PUBLISHED'
        WHEN beginTime > now()
        AND publishTime IS NOT NULL THEN
        'UN_STARTED'
        WHEN now() >= beginTime
        AND endTime >= now()
        AND publishTime IS NOT NULL THEN
        'STARTED'
        WHEN now() > endTime
        AND publishTime IS NOT NULL THEN
        'FINISHED'
        END AS STATUS,
        '' AS bannerUrl
        FROM
        uemtraining.readingact
        WHERE
        deleted = 0
        AND companyId = #{companyId}
    </sql>

    <!--    智趣答题    -->
    <sql id="wisdomgoddess">
        SELECT
        id AS Id,
        'WISDOMGODDESS' AS model,
        ActName AS title,
        0 AS number,
        startTime,
        endTime,
        companyId,
        publishTime,
        CASE

        WHEN publishTime IS NULL THEN
        'UN_PUBLISHED'
        WHEN startTime > now()
        AND publishTime IS NOT NULL THEN
        'UN_STARTED'
        WHEN now() >= startTime
        AND endTime >= now()
        AND publishTime IS NOT NULL THEN
        'STARTED'
        WHEN now() > endTime
        AND publishTime IS NOT NULL THEN
        'FINISHED'
        END AS STATUS,
        '' AS bannerUrl
        FROM
        uemtraining.wisdomgoddess
        WHERE
        deleted = 0
        AND companyId = #{companyId}
    </sql>


    <!--    答题闯关    -->
    <sql id="knowledgegame">
        SELECT
        id AS Id,
        'KNOWLEDGEGAME' AS model,
        title,
        0 AS number,
        startTime,
        endTime,
        companyId,
        publishTime,
        CASE
        WHEN publishTime IS NULL THEN
        'UN_PUBLISHED'
        WHEN startTime > now()
        AND publishTime IS NOT NULL THEN
        'UN_STARTED'
        WHEN now() >= startTime
        AND endTime >= now()
        AND publishTime IS NOT NULL THEN
        'STARTED'
        WHEN now() > endTime
        AND publishTime IS NOT NULL THEN
        'FINISHED'
        END AS STATUS,
        '' AS bannerUrl
        FROM
        uemtraining.knowledgegame
        WHERE
        deleted = 0
        AND joinType != 11
        AND companyId = #{companyId}
    </sql>

    <!--    答题PK    -->
    <sql id="pkgame">
        SELECT
        id AS Id,
        'PKGAME' AS model,
        title,
        0 AS number,
        begin_time startTime,
        end_time endTime,
        company_id companyId,
        published_time publishTime,
        CASE
        WHEN published_time IS NULL THEN
        'UN_PUBLISHED'
        WHEN begin_time > now()
        AND published_time IS NOT NULL THEN
        'UN_STARTED'
        WHEN now() >= begin_time
        AND end_time >= now()
        AND published_time IS NOT NULL THEN
        'STARTED'
        WHEN now() > end_time
        AND published_time IS NOT NULL THEN
        'FINISHED'
        END AS STATUS,
        '' AS bannerUrl
        FROM
        uem_imsp.quizgame
        WHERE
        deleted = 0
        AND company_id = #{companyId}
    </sql>

    <!--    调查问卷    -->
    <sql id="questionnaire">
        SELECT
        questionnaireId AS Id,
        'QUESTIONNAIRE' AS model,
        title,
        0 AS number,
        startTime,
        endTime,
        companyId,
        publishTime,
        CASE
        WHEN publishTime IS NULL THEN
        'UN_PUBLISHED'
        WHEN startTime > now()
        AND publishTime IS NOT NULL THEN
        'UN_STARTED'
        WHEN now() >= startTime
        AND endTime >= now()
        AND publishTime IS NOT NULL THEN
        'STARTED'
        WHEN now() > endTime
        AND publishTime IS NOT NULL THEN
        'FINISHED'
        END AS STATUS,
        '' AS bannerUrl
        FROM
        uemtraining.questionnaire
        WHERE
        deleted = 0
        AND companyId = #{companyId}
    </sql>


    <!--    知识竞赛    -->
    <sql id="knowledge">
        SELECT
        KnowledgeId as Id,
        'KNOWLEDGE' AS model,
        title,
        0 AS number,
        startTime,
        endTime,
        companyId,
        publishTime,
        case
        when publishTime is null then 'UN_PUBLISHED'
        when startTime > now() and publishTime is not null then 'UN_STARTED'
        when now() >= startTime and endTime >= now() and publishTime is not null then 'STARTED'
        when now() > endTime and publishTime is not null then 'FINISHED'
        end as status,
        '' AS bannerUrl
        FROM
        uemtraining.knowledge where deleted=0
        and isgame=0
        and joinType not in (10,11)
        AND companyId = #{companyId}
    </sql>

    <!-- 职工学院课程：设置发布状态 -->
    <update id="setPublishStatus">
        UPDATE staffcollege
        SET PublishUserId = #{publishUserId}, PublishTime = #{publishTime}
        WHERE CourseId = #{courseId} AND IsDeleted = 0
    </update>

    <!-- 职工学院课程：逻辑删除活动 -->
    <update id="deleteActivity">
        UPDATE staffcollege
        SET IsDeleted = 1
        WHERE CourseId = #{courseId} AND IsDeleted = 0
    </update>

    <!-- 职工学院课程：复制活动（示例，实际实现需根据业务调整） -->
    <insert id="copyActivity">
        INSERT INTO staffcollege (
            CompanyId, CourseName, StartTime, EndTime, JoinType, Banners, ShowByEndTime, ShowMyPrize, ShowRank, LotteryId, ShowPrize, PrizeId, ShowElegantDemeanor, ElegantDemeanorId, CategoryId, DepartLevel, Rule, Remark, Tags, RankingBase, IsDeleted, CreateTime, ShowRule, ThemeId, CourseIntroTitle, ShowProgressBar, PublishUserId, PublishTime
        )
        SELECT CompanyId, CONCAT(CourseName, '_副本'), StartTime, EndTime, JoinType, Banners, ShowByEndTime, ShowMyPrize, ShowRank, LotteryId, ShowPrize, PrizeId, ShowElegantDemeanor, ElegantDemeanorId, CategoryId, DepartLevel, Rule, Remark, Tags, RankingBase, 0, NOW(), ShowRule, ThemeId, CourseIntroTitle, ShowProgressBar, NULL, NULL
        FROM staffcollege
        WHERE CourseId = #{courseId} AND IsDeleted = 0
    </insert>
</mapper>
