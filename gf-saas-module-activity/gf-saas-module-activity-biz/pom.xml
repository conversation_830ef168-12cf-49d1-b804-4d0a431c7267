<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.gf.saas</groupId>
        <artifactId>gf-saas-module-activity</artifactId>
        <version>${revision}</version>  <!-- 1. 修改 version 为 ${revision} -->
    </parent>
    <artifactId>gf-saas-module-activity-biz</artifactId>
    <packaging>jar</packaging>  <!-- 2. 新增 packaging 为 jar -->
    <name>${project.artifactId}</name> <!-- 3. 新增 name 为 ${project.artifactId} -->
    <description> <!-- 4. 新增 description 为该模块的描述 -->
        demo 模块，主要实现 XXX、YYY、ZZZ 等功能。
    </description>

    <dependencies>  <!-- 5. 新增依赖，这里引入的都是比较常用的业务组件、技术组件 -->
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-env</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-module-activity-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-web</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-rpc</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-mq</artifactId>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-protection</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-excel</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-monitor</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-module-activity-api</artifactId>
            <version>2.2.0-jdk8-snapshot</version>
            <scope>compile</scope>
        </dependency>
        
        <!-- 测试相关依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>