package cn.gf.saas.module.mall.dal.dataobject.walfareapply;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 福利券 DO
 *
 * <AUTHOR>
 */
@TableName("walfareapply")
@KeySequence("walfareapply_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WalfareApplyDO {

    /**
     * 主键guid类型
     */
    @TableId(type = IdType.INPUT)
    private String welfareApplyId;
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;
    /**
     * 券名称
     */
    private String welfareName;
    /**
     * 申请时间
     */
    private LocalDateTime applyTime;
    /**
     * 申请原由
     */
    private String reason;
    /**
     * 福利类型
     */
    private Integer welfareTypeId;
    /**
     * 抵用券类型
     */
    private Integer couponTypeId;
    /**
     * 券面价格
     */
    private BigDecimal parvalue;
    /**
     * 开始日期
     */
    private LocalDateTime beginDate;
    /**
     * 有效期
     */
    private LocalDateTime expiredDate;
    /**
     * 券可领取时间
     */
    private LocalDateTime receiveDate;
    /**
     * 申请类型：批量/个人
     */
    private String applyType;
    /**
     * 申请人
     */
    private String applyUserId;
    /**
     * 状态：待确认/发放中/退回/发放完成
     */
    private String status;
    /**
     * 总数量
     */
    private Integer totalCount;
    /**
     * 总额
     */
    private BigDecimal totalAmount;
    /*
    项目余额
     */
    @TableField(value = "balance")
    private BigDecimal balance;
    /**
     * 确认/退回时间
     */
    @TableField(value = "checkTime")
    private LocalDateTime checkTime;
    /**
     * 公司
     */
    @TableField(value = "companyId")
    private String companyId;
    /**
     * 退回理由
     */
    private String refuseReason;
    /**
     * 已领取数据
     */
    private Integer receivedCount;
    /**
     * 已领取总额
     */
    private BigDecimal receivedAmount;
    /**
     * 首付款
     */
    private BigDecimal paymentFirst;
    /**
     * 已收款总额
     */
    private BigDecimal paymentTotal;
    /**
     * 回款是否完成
     */
    private Boolean paymentIsComplete;
    /**
     * applyUserName
     */
    private String applyUserName;
    /**
     * 是否发送通知短信
     */
    private Boolean isSendNoticeMsg;
    /**
     * 是否APP推送消息：0:否,1:是
     */
    private Boolean isUMengPushMsg;
    /**
     * 通知短信模板
     */
    private String noticeMsgTemp;
    /**
     * 拆券数量
     */
    private Integer splitCount;
    /**
     * 券消费方式：1可以多次使用，2一次使用
     */
    private Integer consumeType;
    /**
     * 限制券使用项目类型 1：礼包专区 0：非礼包
     */
    private Integer projectType;
    /**
     * 限制券使用项目
     */
    private String projectId;
    /**
     * 限制券使用商城
     */
    private String shopId;
    /**
     * 是否单独使用（不允许叠加用）：1单独使用，0叠加使用
     */
    private Boolean isAloneUse;
    /**
     * 备注
     */
    private String remark;
    /**
     * couponname
     */
    private String couponname;
    /**
     * manager
     */
    private String manager;
    /**
     * 负责人手机号
     */
    private String managermobile;
    /**
     * CouponStyleID
     */
    private Integer couponStyleID;
    /**
     * 用券备注
     */
    private String couponTips;
    /**
     * 对应专区id
     */
    private String moduleid;
    /**
     * 是否演示项目（发演示券）
     */
    private Boolean isDemo;
    /**
     * 是否隐藏消费券面额，默认0，不隐藏
     */
    private Boolean hideValue;
    /**
     * 奖章项目编号
     */
    private String welfareMedalId;
    /**
     * billtype
     */
    private Integer billtype;
    /**
     * 是否允许赠送，0不允许，1允许
     */
    private Integer isAllowDonate;
    /**
     * 结算状态，0未结算，1已结算
     */
    private Integer jsstatus;
    /**
     * 关联表主题id
     */
    private Integer themeId;
    /**
     * 领券提示标语
     */
    private String receiveTips;
    /**
     * 若选择①新发券项目 为空 ，若选择②项目人员增补，要求用户选择父项目
     */
    private String parentid;
    /**
     * 承诺预付金额（不要求到账）
     */
    private BigDecimal prepay;
    /**
     * SyncStatus
     */
    private Integer syncStatus;
    /**
     * 实际金额（总额）
     */
    private BigDecimal totalAmount2;
    /**
     * parvalue2
     */
    private BigDecimal parvalue2;
    /**
     * 0-待审核，1-销管已审核，2-管理员已审核
     */
    private Integer smcheckstatus;
    /**
     * 销管用户id
     */
    private String smuserid;
    /**
     * 销管审核时间
     */
    private LocalDateTime smchecktime;
    /**
     * 是否已导出凭证
     */
    private Integer pzexported;
    /**
     * 是否电影票类项目，0否；1是
     */
    private Integer ismovie;
    /**
     * app类型
     */
    private Integer apptype;
    /**
     * 过期仍然显示 勾选：1 不勾选 0
     */
    private Boolean expiredDisplay;
    /**
     * 业务类型:常规业务、集采业务、特定（低利润折扣类）
     */
    private Integer biztype;
    /**
     * 充值卡限制单位Id
     */
    private String rechargeCompanyId;
    /**
     * 充值卡限制：0=不限制, 1=限制单位, 2= 限制单位及下属单位
     */
    private Integer rechargeScope;
    /**
     * 失效：1正常 0失效, 默认1
     */
    private Boolean enabled;
    /**
     * 领券结束时间
     */
    private LocalDateTime receiveDate2;
    /**
     * 领券后有效天数
     */
    private Integer receiveValidDays;
    /**
     * 每人限领取张数
     */
    private Integer receiveMaxCount;
    /**
     * 消费满X元可触发领取
     */
    private BigDecimal receiveMinAmount;
    /**
     * 过期提醒提前天数
     */
    private Integer expiredTipDays;
    /**
     * 选择单位类型 0不选 1按选择 2全选 3反选
     */
    private Integer companySelType;
    /**
     * 审核人员名字
     */
    private String checkUser;
    /**
     * 是否跨单位发券 0：否  1：是
     */
    private Integer isCrossUnit;

}