package cn.gf.saas.module.mall.controller.admin.skudetail.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.poi.hpsf.Decimal;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - sku分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SKUDetailPageReqVO extends PageParam {

//    @Schema(description = "skuId", example = "26322")
//    private Long skuId;

    @Schema(description = "名称", example = "李四")
    private String name;

    /**
     * 排序：default，price，sale
     */
    @Schema(description = "排序", example = "default，price，sale")
    private String sort;

    /**
     * 升降序：asc，desc
     */
    @Schema(description = "升降序", example = "asc，desc")
    private String sortType;

    /**
     * 最低价
     */
    @Schema(description = "最低价", example = "0")
    private BigDecimal priceSale1;

    /**
     * 最高价
     */
    @Schema(description = "最高价", example = "100")
    private BigDecimal priceSale2;

    /**
     * 一级分类
     */
    @Schema(description = "一级分类")
    private Integer catId1;

    /**
     * 二级分类
     */
    @Schema(description = "二级分类")
    private Integer catId2;

    /**
     * 三级分类
     */
    @Schema(description = "三级分类")
    private Integer catId3;

}