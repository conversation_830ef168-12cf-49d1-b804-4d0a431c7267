package cn.gf.saas.module.system.controller.admin.userguide;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.system.dal.dataobject.userguide.UserGuideDO;
import cn.gf.saas.module.system.service.userguide.UserGuideService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;
import static cn.gf.saas.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 用户新手引导")
@RestController
@RequestMapping("/system/user-guide")
@Validated
public class UserGuideController {

    @Resource
    private UserGuideService userGuideService;

    @PostMapping("/create")
    @Operation(summary = "创建用户新手引导记录")
    public CommonResult<Boolean> createUserGuide() {
        return success(userGuideService.createUserGuide(getLoginUserId()));
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户新手引导记录")
    public CommonResult<Boolean> getUserGuide(@RequestParam("userId") Long userId) {
        return success(userGuideService.getUserGuide(userId));
    }
} 