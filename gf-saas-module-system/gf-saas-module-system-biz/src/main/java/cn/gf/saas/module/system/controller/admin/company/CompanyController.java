package cn.gf.saas.module.system.controller.admin.company;

import cn.gf.saas.framework.apilog.core.annotation.ApiAccessLog;
import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageParam;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.framework.excel.core.util.ExcelUtils;
import cn.gf.saas.module.system.api.company.dto.AppAuthCompanyDepartmentRespDTO;
import cn.gf.saas.module.system.controller.admin.company.vo.*;
import cn.gf.saas.module.system.service.company.CompanyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.gf.saas.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.gf.saas.framework.common.pojo.CommonResult.success;
import static cn.gf.saas.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 单位")
@RestController
@RequestMapping("/system/company")
@Validated
public class CompanyController {
    @Resource
    private CompanyService companyService;

    @GetMapping("/list")
    @Operation(summary = "获取单位列表", description = "用于【单位列表】界面")
    //@PreAuthorize("@ss.hasPermission('system:company:list')")
    public CommonResult<List<CompanySimpleRespVO>> getCompanyList(@Valid CompanyListReqVO reqVO) {
        return success(companyService.getCompanyList(reqVO));
    }

    @GetMapping("/page")
    @Operation(summary = "获取单位列表分页", description = "用于【单位列表】界面")
    //@PreAuthorize("@ss.hasPermission('system:company:list')")
    public CommonResult<PageResult<CompanyRespVO>> getCompanyPage(@Valid CompanyListReqVO reqVO) {
        return success(companyService.getCompanyPage(reqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获取单位详情", description = "用于【单位列表】界面")
    //@PreAuthorize("@ss.hasPermission('system:company:query')")
    public CommonResult<CompanyRespVO> getCompany(@RequestParam("companyId") String companyId) {
        return success(companyService.getCompany(companyId));
    }

    @PostMapping("/create")
    @Operation(summary = "创建单位")
    @PreAuthorize("@ss.hasPermission('system:company:create')")
    public CommonResult<String> createCompany(@Valid @RequestBody CompanyReqVO companyReqVO) {
        return success(companyService.createCompany(companyReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "修改单位")
    @PreAuthorize("@ss.hasPermission('system:company:update')")
    public CommonResult<String> updateCompany(@Valid @RequestBody CompanyUpdateReqVO companyReqVO) {
        companyService.updateCompany(companyReqVO);
        return success();
    }

    @PutMapping("/update-user")
    @Operation(summary = "修改单位用户信息")
    @PreAuthorize("@ss.hasPermission('system:company:update')")
    public CommonResult<String> updateCompanyUser(@Valid @RequestBody CompanyUserUpdateReqVO companyReqVO) {
        companyService.updateCompanyUser(companyReqVO);
        return success();
    }

    @PutMapping("/updatePackage")
    @Operation(summary = "修改单位套餐")
    @PreAuthorize("@ss.hasPermission('system:company:update-package')")
    public CommonResult<String> updateCompanyPackage(@Valid @RequestBody CompanyPackageReqVO companyReqVO) {
        companyService.updateCompanyPackage(companyReqVO);
        return success();
    }


    @GetMapping("/list-simple")
    @Operation(summary = "获取精简单位列表", description = "单位端用于【单位选择】界面")
    //@PreAuthorize("@ss.hasPermission('system:menu:list-simple')")
    public CommonResult<PageResult<CompanySimpleRespVO>> getCompanySimpleList(@Valid CompanyListReqVO reqVO) {
        PageResult<CompanyRespVO> list = companyService.getCompanyPage(reqVO);
        return success(BeanUtils.toBean(list, CompanySimpleRespVO.class));
    }

    @GetMapping("/current-user-company-tree")
    @Operation(summary = "获取当前登录用户的数据权限树", description = "用于【渠道管理】界面")
    @PreAuthorize("@ss.hasPermission('system:menu:current-user-company-tree')")
    public CommonResult<List<CompanySimpleRespVO>> getCurrentUserCompanyTree(Long userId, String parentId, String targetCompanyId, String source) {
        return success(companyService.getCurrentUserCompanyTree(userId == null ? getLoginUserId() : userId, parentId, targetCompanyId, source));
    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出单位 Excel")
    @PreAuthorize("@ss.hasPermission('system:company:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCompanyExcel(HttpServletResponse response, @Validated CompanyListReqVO reqVO) throws IOException {
        reqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CompanyRespVO> list = companyService.exportCompanyExcel(reqVO);

        // 导出 Excel
        ExcelUtils.write(response, "单位.xls", "数据", CompanyExportRespVO.class, BeanUtils.toBean(list, CompanyExportRespVO.class));
    }

    /**
     * 获取公司部门信息
     */
    @GetMapping("/getCompanyDepartment")
    @Operation(summary = "获取公司部门信息")
    public CommonResult<AppAuthCompanyDepartmentRespDTO> getCompanyDepartment(@RequestParam("companyId") String companyId) {
        return success(companyService.getCompanyDepartment(companyId));
    }


    @GetMapping("/getCompanyPackage")
    @Operation(summary = "获取单位套餐信息")
    public CommonResult<CompanyPackageRespVO> getCompanyPackage(@RequestParam("companyId") String companyId) {
        return success(companyService.getCompanyPackage(companyId));
    }
}
