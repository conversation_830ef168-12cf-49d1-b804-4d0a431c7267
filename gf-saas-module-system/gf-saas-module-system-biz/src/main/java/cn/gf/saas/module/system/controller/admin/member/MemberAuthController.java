package cn.gf.saas.module.system.controller.admin.member;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.module.system.controller.admin.member.vo.AppVisitorAuthLoginRespVO;
import cn.gf.saas.module.system.service.member.MemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "会员 - 认证")
@RestController
@RequestMapping("/system/member/auth")
@Validated
@Slf4j
public class MemberAuthController {

    @Resource
    private MemberService authService;

    @GetMapping("/visitor-login")
    @Operation(summary = "游客登录接口")
    public CommonResult<AppVisitorAuthLoginRespVO> visitorLogin(Long templateId, String companyId) {
        return success(authService.visitorLogin(templateId, companyId));
    }
}
