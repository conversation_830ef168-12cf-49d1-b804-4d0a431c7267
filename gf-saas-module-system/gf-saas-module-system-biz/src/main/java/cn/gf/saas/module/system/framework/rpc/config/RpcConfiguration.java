package cn.gf.saas.module.system.framework.rpc.config;

import cn.gf.saas.module.activity.api.activity.ActivityApi;
import cn.gf.saas.module.app.api.member.AppMemberApi;
import cn.gf.saas.module.infra.api.config.ConfigApi;
import cn.gf.saas.module.infra.api.file.FileApi;
import cn.gf.saas.module.infra.api.websocket.WebSocketSenderApi;
import cn.gf.saas.module.mall.api.statistics.StatisticsApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {FileApi.class, WebSocketSenderApi.class, ConfigApi.class, ActivityApi.class, StatisticsApi.class
, AppMemberApi.class})
public class RpcConfiguration {
}
