package cn.gf.saas.module.system.controller.admin.company.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 单位精简信息 Response VO")
@Data
public class CompanySimpleRespVO {
    @Schema(description = "单位id")
    private String companyid;

    @Schema(description = "父级id")
    private String parentid;

    @Schema(description = "全称")
    private String fullname;

    @Schema(description = "简称")
    private String shortname;

    @Schema(description = "是否删除")
    private Integer deleted;

    @Schema(description = "是否被其他单位选中")
    private Boolean isSelected = false;

    @Schema(description = "是否展示详情")
    private Boolean isShowDetail = true;

    @Schema(description = "是否有下级")
    private Boolean isShowSub = false;

    @Schema(description = "是否是saas")
    private Boolean isSaas = false;

    @Schema(description = "来源")
    @JsonIgnore
    private String source;

    @Schema(description = "是否有权限选中，注意：不可选的说明是没有权限的，但是需要展示出来的")
    private Boolean isPermissionSelected = true;

    @Schema(description = "子级单位")
    private List<CompanySimpleRespVO> children; // 添加 children 字段

}
