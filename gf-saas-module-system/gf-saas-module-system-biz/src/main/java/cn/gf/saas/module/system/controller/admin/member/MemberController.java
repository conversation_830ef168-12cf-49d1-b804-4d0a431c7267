package cn.gf.saas.module.system.controller.admin.member;

import cn.gf.saas.framework.apilog.core.annotation.ApiAccessLog;
import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageParam;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.excel.core.util.ExcelUtils;
import cn.gf.saas.module.system.controller.admin.company.vo.CompanyRespVO;
import cn.gf.saas.module.system.controller.admin.member.vo.*;
import cn.gf.saas.module.system.framework.cloud_platform.UserApi;
import cn.gf.saas.module.system.service.company.CompanyService;
import cn.gf.saas.module.system.service.member.MemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

import static cn.gf.saas.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.gf.saas.framework.common.pojo.CommonResult.success;
import static cn.gf.saas.module.system.util.tenant.TenantUtil.getTenantId;

@Tag(name = "管理后台 - 会员")
@RestController
@RequestMapping("/system/member")
@Validated
@Slf4j
public class MemberController {

    @Resource
    private MemberService memberService;
    @Resource
    private CompanyService companyService;

    @GetMapping("/list")
    @Operation(summary = "会员列表")
    //@PreAuthorize("@ss.hasPermission('system:member:list')")
    public CommonResult<PageResult<MemeberQueryRespVO>> getMemberList(@Valid MemeberQueryReqVO queryReqVO) {
        return success(memberService.getMemberList(queryReqVO));
    }

    @GetMapping("/export")
    @Operation(summary = "导出会员")
    //@PreAuthorize("@ss.hasPermission('system:member:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMemberList(@Validated MemeberQueryReqVO queryReqVO,
                                 HttpServletResponse response) throws IOException {
        queryReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        CompanyRespVO companyRespVO = companyService.getCompany(getTenantId());
        PageResult<MemeberQueryRespVO> memberList = memberService.getMemberList(queryReqVO);
        // 输出 Excel，设置 title 参数来添加表名作为第一行
        String title = companyRespVO == null ? "会员表" : companyRespVO.getFullname() + "会员表";
        ExcelUtils.write(response, "会员数据.xls", "数据", MemeberQueryRespVO.class,
                memberList.getList(), title);
    }


    @PostMapping("create")
    @Operation(summary = "新增会员")
    //@PreAuthorize("@ss.hasPermission('system:member:create')")
    public CommonResult<Long> createMember(@Valid @RequestBody MemeberSaveReqVO createReqVO) {
        memberService.saveMember(createReqVO);
        return success();
    }

    @PostMapping("update")
    @Operation(summary = "修改会员")
    //@PreAuthorize("@ss.hasPermission('system:member:update')")
    public CommonResult<Long> updateMember(@Valid @RequestBody MemeberSaveReqVO createReqVO) {
        memberService.updateMember(createReqVO);
        return success();
    }

    @GetMapping("/get")
    @Operation(summary = "获得会员详情")
    //@PreAuthorize("@ss.hasPermission('system:member:get')")
    public CommonResult<MemeberQueryDetailRespVO> getMember(@RequestParam("userId") String userId) {
        return success(memberService.getMember(userId));
    }


    @PutMapping("batch-update-status")
    @Operation(summary = "批量禁用/启用会员")
    //@PreAuthorize("@ss.hasPermission('system:member:batch-update-status')")
    public CommonResult<Boolean> batchUpdateMember(@Valid @RequestBody MemberBatchUpdateDisableRespVO respVO) {
        memberService.batchUpdateMember(respVO);
        return success(true);
    }

    @PutMapping("batch-update-dept")
    @Operation(summary = "批量变更会员组织", description = "选一级部门：departmentId传一级部门的id，parentId不用传\n" +
            "选二级部门：departmentId传二级部门的id，parentId传选择部门的父级部门id")
    //@PreAuthorize("@ss.hasPermission('system:member:batch-update-dept')")
    public CommonResult<Boolean> batchUpdateMemberDept(@Valid @RequestBody MemberBatchUpdateDeptRespVO respVO) {
        memberService.batchUpdateMemberDept(respVO);
        return success(true);
    }

    @DeleteMapping("batch-delete-member")
    @Operation(summary = "批量删除会员")
    //@PreAuthorize("@ss.hasPermission('system:member:batch-delete-member')")
    public CommonResult<Boolean> batchDeleteMember(@Valid @RequestBody MemberBatchDeleteRespVO respVO) {
        memberService.batchDeleteMember(respVO);
        return success(true);
    }


    @GetMapping("/welfareList")
    @Operation(summary = "获取福利申请")
    public CommonResult<String> welfareList() throws Exception {
        return success(UserApi.welfareList());
    }

}
