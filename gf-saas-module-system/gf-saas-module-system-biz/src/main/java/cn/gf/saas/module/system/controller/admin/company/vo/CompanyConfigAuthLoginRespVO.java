package cn.gf.saas.module.system.controller.admin.company.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "公司认证登录配置响应对象")
public class CompanyConfigAuthLoginRespVO {

    @Schema(description = "单位ID", required = true)
    private String companyId;

    @Schema(description = "单位参数(c =\"\")", required = true)
    private String linkParameter;

    @Schema(description = "单位参数不可修改", required = true)
    private Boolean linkParameterFixed;

    @Schema(description = "登录Banner图", required = true)
    private String loginBanner;

    @Schema(description = "登录方式 一：手机号验证码登录+账号密码登录 二：仅手机号验证码登录 三：仅账号密码登录", required = true)
    private Integer loginMode;

    @Schema(description = "是否启用注册入口（1：启用；0：关闭）", required = true)
    private Boolean registEnabled;

    @Schema(description = "注册页链接", required = true)
    private String registUrl;

    @Schema(description = "注册Banner图", required = true)
    private String registBanner;

    @Schema(description = "注册模式(1:普通 2：选择部门)", required = true)
    private Integer registType;

    @Schema(description = "注册按钮文案", required = true)
    private String registText;

    @Schema(description = "登录链接", required = true)
    private String loginUrl;

}
