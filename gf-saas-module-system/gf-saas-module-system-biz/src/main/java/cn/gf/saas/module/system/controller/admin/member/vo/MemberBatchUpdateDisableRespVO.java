package cn.gf.saas.module.system.controller.admin.member.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.gf.saas.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Data
public class MemberBatchUpdateDisableRespVO {

    @Schema(description = "禁用/启用的用户")
    @NotNull(message = "用户编号列表不能为空")
    @DiffLogField(name = "禁用的用户")
    public List<String> userIds;

    @Schema(description = "禁用开始时间")
    @NotNull(message = "禁用开始时间不能为空")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class) // 反序列化（请求）
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @DiffLogField(name = "禁用开始时间")
    public LocalDateTime disableTime;
}
