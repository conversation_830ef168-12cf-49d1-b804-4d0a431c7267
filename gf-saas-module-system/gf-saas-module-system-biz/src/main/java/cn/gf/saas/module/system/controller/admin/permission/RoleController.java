package cn.gf.saas.module.system.controller.admin.permission;

import cn.gf.saas.framework.apilog.core.annotation.ApiAccessLog;
import cn.gf.saas.framework.common.enums.CommonStatusEnum;
import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageParam;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.framework.excel.core.util.ExcelUtils;
import cn.gf.saas.module.system.controller.admin.permission.vo.role.*;
import cn.gf.saas.module.system.dal.dataobject.permission.RoleDO;
import cn.gf.saas.module.system.enums.permission.RoleTypeEnum;
import cn.gf.saas.module.system.service.permission.RoleService;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;

import static cn.gf.saas.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.gf.saas.framework.common.pojo.CommonResult.success;
import static cn.gf.saas.module.system.util.tenant.TenantUtil.getTenantId;

@Tag(name = "管理后台 - 角色")
@RestController
@RequestMapping("/system/role")
@Validated
public class RoleController {

    @Resource
    private RoleService roleService;

    @PostMapping("/create")
    @Operation(summary = "创建角色")
    @PreAuthorize("@ss.hasPermission('system:role:create')")
    public CommonResult<Long> createRole(@Valid @RequestBody RoleSaveReqVO createReqVO) {
        return success(roleService.createRole(createReqVO, createReqVO.getType()));
    }

    @PostMapping("/create-tenant-role")
    @Operation(summary = "创建渠道角色")
    @PreAuthorize("@ss.hasPermission('system:role:create-tenant-role')")
    public CommonResult<Long> createTenantRole(@Valid @RequestBody RoleTenantSaveReqVO createReqVO) {
        // todo 只有渠道负责人创建的渠道角色才能生效
        createReqVO.setType(RoleTypeEnum.CHANNEL.getType());
        return success(roleService.createRole(BeanUtils.toBean(createReqVO, RoleSaveReqVO.class), createReqVO.getType()));
    }

    @PostMapping("/createBuiltRole")
    @Operation(summary = "创建内置角色")
    @PreAuthorize("@ss.hasPermission('system:role:create')")
    public CommonResult<Long> createBuiltRole(@Valid @RequestBody RoleSaveReqVO createReqVO) {
        return success(roleService.createRole(createReqVO, createReqVO.getType()));
    }

    @PutMapping("/update")
    @Operation(summary = "修改角色")
    @PreAuthorize("@ss.hasAnyPermissions('system:role:update', 'system:role:update-tenant-role')")
    public CommonResult<Boolean> updateRole(@Valid @RequestBody RoleSaveReqVO updateReqVO) {
        roleService.updateRole(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-tenant-role")
    @Operation(summary = "修改渠道角色")
    @PreAuthorize("@ss.hasAnyPermissions('system:role:update', 'system:role:update-tenant-role')")
    public CommonResult<Boolean> updateTenantRole(@Valid @RequestBody RoleTenantSaveReqVO updateReqVO) {
        roleService.updateTenantRole(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除角色")
    @Parameter(name = "id", description = "角色编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasAnyPermissions('system:role:delete', 'system:role:delete-tenant-role')")
    public CommonResult<Boolean> deleteRole(@RequestParam("id") Long id) {
        roleService.deleteRole(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得角色信息")
    //@PreAuthorize("@ss.hasPermission('system:role:query')")
    public CommonResult<RoleRespVO> getRole(@RequestParam("id") Long id) {
        RoleDO role = roleService.getRole(id);
        return success(BeanUtils.toBean(role, RoleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得角色分页")
    //@PreAuthorize("@ss.hasPermission('system:role:query')")
    public CommonResult<PageResult<RoleRespVO>> getRolePage(RolePageReqVO pageReqVO) {
        PageResult<RoleDO> pageResult = roleService.getRolePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RoleRespVO.class));
    }

    @GetMapping("/tenant-admin-role-page")
    @Operation(summary = "获得渠道管理员角色分页")
    //@PreAuthorize("@ss.hasPermission('system:role:tenant-admin-role-page')")
    public CommonResult<PageResult<RoleRespVO>> getTenantAdminRolePage(RolePageReqVO pageReqVO) {
        PageResult<RoleRespVO> pageResult = roleService.getTenantAdminRolePage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/tenant-admin-role-export")
    @Operation(summary = "导出角色 Excel")
    @ApiAccessLog(operateType = EXPORT)
    @PreAuthorize("@ss.hasAnyPermissions('system:role:export', 'system:role:export-tenant-role')")
    public void tenantAdminRoleexport(HttpServletResponse response, @Validated RolePageReqVO exportReqVO) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RoleRespVO> list = roleService.getTenantAdminRolePage(exportReqVO).getList();
        // 输出
        ExcelUtils.write(response, "角色数据.xls", "数据", RoleTenantAdminRespVO.class,
                BeanUtils.toBean(list, RoleTenantAdminRespVO.class));
    }

    @GetMapping("/inbuilt-role")
    @Operation(summary = "内置角色")
    @PreAuthorize("@ss.hasPermission('system:role:inbuilt-role')")
    public CommonResult<List<RoleRespVO>> getInbuiltRolePage() {
        return success(roleService.getInbuiltRolePage());
    }


    @GetMapping({"/list-all-simple", "/simple-list"})
    @Operation(summary = "获取角色精简信息列表", description = "只包含被开启的角色，主要用于前端的下拉选项")
    public CommonResult<List<RoleRespVO>> getSimpleRoleList() {
        RolePageReqVO pageReqVO = new RolePageReqVO();
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        PageResult<RoleDO> page = roleService.getRoleList(pageReqVO);
        List<RoleDO> list = page.getList();
        list.sort(Comparator.comparing(RoleDO::getSort));
        return success(BeanUtils.toBean(list, RoleRespVO.class));
    }

    @GetMapping("/simple-list-by-tenantId")
    @Operation(summary = "获取角色精简信息列表根据渠道id", description = "只包含被开启的角色，主要用于前端的下拉选项")
    public CommonResult<List<RoleRespVO>> getSimpleRoleListByTenantId(@RequestParam("tenantId") String tenantId) {
        if(StrUtil.isEmpty(tenantId)){
            return success(new ArrayList<>());
        }

        RolePageReqVO pageReqVO = new RolePageReqVO();
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        pageReqVO.setTenantId(tenantId);
        PageResult<RoleDO> page = roleService.getRoleList(pageReqVO);
        List<RoleDO> list = page.getList();
        list.sort(Comparator.comparing(RoleDO::getSort));
        return success(BeanUtils.toBean(list, RoleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出角色 Excel")
    @ApiAccessLog(operateType = EXPORT)
    @PreAuthorize("@ss.hasPermission('system:role:export')")
    public void export(HttpServletResponse response, @Validated RolePageReqVO exportReqVO) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RoleDO> list = roleService.getRolePage(exportReqVO).getList();
        // 输出
        ExcelUtils.write(response, "角色数据.xls", "数据", RoleRespVO.class,
                BeanUtils.toBean(list, RoleRespVO.class));
    }

}
