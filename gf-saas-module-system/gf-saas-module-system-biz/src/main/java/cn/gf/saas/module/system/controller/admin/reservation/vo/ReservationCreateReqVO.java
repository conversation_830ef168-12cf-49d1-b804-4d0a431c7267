package cn.gf.saas.module.system.controller.admin.reservation.vo;

import cn.gf.saas.framework.common.validation.RealMobile;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

@Schema(description = "管理后台 - saas官网体验预约创建 Request VO")
@Data
public class ReservationCreateReqVO {

    @Schema(description = "用户名", required = true, example = "张三")
    @NotEmpty(message = "用户名不能为空")
    @Size(max = 20, message = "用户名长度不能超过 30 个字")
    private String username;

    @Schema(description = "手机号码", required = true, example = "15601691300")
    @NotEmpty(message = "手机号码不能为空")
    @RealMobile
    private String mobile;

    @Schema(description = "工会/公司名称", required = true, example = "XXX公司")
    @NotEmpty(message = "工会/公司名称不能为空")
    @Size(max = 30, message = "工会/公司名称长度不能超过 30 个字")
    private String companyName;

    @Schema(description = "需求描述", example = "我想体验一下系统")
    @Size(max = 2000, message = "需求描述长度不能超过 2000 个字")
    private String description;
} 