package cn.gf.saas.module.system.controller.admin.company.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 单位套餐信息 Response VO")
@Data
public class CompanyPackageRespVO {

    @Schema(description = "套餐名")
    private String packageName;

    @Schema(description = "套餐id")
    private Long packageId;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @Schema(description = "套餐有效期开始日期")
    private LocalDateTime packageStartDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @Schema(description = "套餐有效期结束日期")
    private LocalDateTime packageEndDate;

    @Schema(description = "套餐有效期状态")
    private String packageStatusStr;

    @Schema(description = "功能介绍")
    private String funcDesc;
}
