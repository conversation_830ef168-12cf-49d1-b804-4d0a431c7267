package cn.gf.saas.module.system.util.tenant;

import cn.gf.saas.framework.tenant.core.context.TenantContextHolder;
import cn.gf.saas.module.system.dal.dataobject.company.Company;
import cn.gf.saas.module.system.dal.dataobject.permission.RoleDO;
import cn.gf.saas.module.system.dal.dataobject.tenant.TenantDO;
import cn.gf.saas.module.system.dal.mysql.company.CompanyMapper;
import cn.gf.saas.module.system.dal.mysql.tenant.TenantMapper;
import cn.gf.saas.module.system.dal.mysql.tenant.TenantUserMapper;
import cn.gf.saas.module.system.dal.redis.RedisKeyConstants;
import cn.gf.saas.module.system.service.permission.UserRoleService;
import cn.gf.saas.module.system.util.role.RoleUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;

import static cn.gf.saas.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

public class TenantUtil {
    public static String getTenantId() {
        return TenantContextHolder.getTenantId();
    }

    public static String getSystemTenantId() {
        return SpringUtil.getBean(Environment.class).getProperty("gf.tenant-id-system");
    }


    /**
     * 是否是渠道角色
     */
    public static boolean isChannelRole() {
        return isSuperAdmin() || isPlatFormAdmin() || isChannelManager() || isChannelAdmin();
    }

    /**
     * 是否是超级管理员
     */
    public static boolean isSuperAdmin() {
        RoleDO role = SpringUtil.getBean(UserRoleService.class).getMaxRoleByUserId(getLoginUserId());
        return role.getId().equals(RoleUtil.getSuperAdminRoleId());
    }

    /**
     * 是否是平台管理员
     */
    public static boolean isPlatFormAdmin() {
        RoleDO role = SpringUtil.getBean(UserRoleService.class).getMaxRoleByUserId(getLoginUserId());
        return role.getId().equals(RoleUtil.getPlatFormAdminRoleId());
    }

    /**
     * 是否是渠道负责人
     */
    public static boolean isChannelManager() {
        return SpringUtil.getBean(UserRoleService.class).checkUserIdIsChannelManager(getLoginUserId());
    }

    /**
     * 是否是渠道管理员
     */
    public static boolean isChannelAdmin() {
        return SpringUtil.getBean(TenantUserMapper.class).isTenantAdminByUserId(getLoginUserId());
    }

    /**
     * 是否是单位端用户
     */
    public static boolean isCompanyUser(Long userId) {
        userId = userId == null ? getLoginUserId() : userId;
        return SpringUtil.getBean(UserRoleService.class).checkUserIdIsCompanyUser(userId);
    }

    /**
     * 是否是单位管理员
     * */
    public static boolean isCompanyAdmin(Long userId) {
        userId = userId == null ? getLoginUserId() : userId;
        return SpringUtil.getBean(UserRoleService.class).checkUserIdIsCompanyAdmin(userId);
    }

    /**
     * 是否是单位端
     */
    public static boolean isCompanyManage() {
        return getCompanyManage(getTenantId());
    }

    /**
     * 设置当前租户的管理端
     * param manage 1:单位端，2:渠道端
     */
    public static void setManage(String tenantId, String companyId) {
        String key = RedisKeyConstants.TENANT_MANAGE_CURRENT_USER + ":" + tenantId;
        SpringUtil.getBean(StringRedisTemplate.class).delete(key);
        if (StrUtil.isEmpty(companyId)) {
            return;
        }
        SpringUtil.getBean(StringRedisTemplate.class).opsForValue().set(key, companyId);
    }

    /**
     * 获取当前租户的单位管理端
     */
    public static Boolean getCompanyManage(String tenantId) {
        String companyId = SpringUtil.getBean(StringRedisTemplate.class).opsForValue().get(RedisKeyConstants.TENANT_MANAGE_CURRENT_USER + ":" + tenantId);
        if (companyId == null) {
            Company company = SpringUtil.getBean(CompanyMapper.class).getCompanyByIdV2(tenantId);
            if (company != null) {
                return true;
            }
            TenantDO tenantDO = SpringUtil.getBean(TenantMapper.class).selectById(tenantId);
            if (tenantDO != null) {
                return false;
            }
            return false;
        }else {
            Company company = SpringUtil.getBean(CompanyMapper.class).getCompanyByIdV2(companyId);
            if (company != null) {
                return true;
            }

            if(TenantUtil.getCompanyManageId(TenantContextHolder.getTenantId()).equals(TenantContextHolder.getTenantId())){
                return true;
            }else {
                return false;
            }
        }
    }

    /**
     * 获取当前租户的单位管理端
     */
    public static String getCompanyManageId(String tenantId) {
        return SpringUtil.getBean(StringRedisTemplate.class).opsForValue().get(RedisKeyConstants.TENANT_MANAGE_CURRENT_USER + ":" + tenantId);

    }

    /**
     * 清除当前租户的切换信息
     */
    public static void clearCurrentManage(String tenantId) {
        String key = RedisKeyConstants.TENANT_MANAGE_CURRENT_USER + ":" + tenantId;
        SpringUtil.getBean(StringRedisTemplate.class).delete(key);
    }

}
