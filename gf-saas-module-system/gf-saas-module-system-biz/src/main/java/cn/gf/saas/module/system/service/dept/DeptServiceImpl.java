package cn.gf.saas.module.system.service.dept;

import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.framework.datapermission.core.annotation.DataPermission;
import cn.gf.saas.module.system.api.permission.dto.DeptDataPermissionRespDTO;
import cn.gf.saas.module.system.controller.admin.company.vo.CompanyReqVO;
import cn.gf.saas.module.system.controller.admin.dept.vo.dept.*;
import cn.gf.saas.module.system.dal.dataobject.dept.DepartmentDTO;
import cn.gf.saas.module.system.dal.mysql.dept.DeptViewMapper;
import cn.gf.saas.module.system.dal.mysql.member.MemberMapper;
import cn.gf.saas.module.system.dal.redis.RedisKeyConstants;
import cn.gf.saas.module.system.framework.cloud_platform.DeptApi;
import cn.gf.saas.module.system.service.logger.OperateLogService;
import cn.gf.saas.module.system.service.logger.OperateLogServiceImpl;
import cn.gf.saas.module.system.service.permission.PermissionService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.ILogRecordService;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.gf.saas.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.gf.saas.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.gf.saas.framework.security.core.util.SecurityFrameworkUtils.getLoginUserDeptId;
import static cn.gf.saas.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.gf.saas.framework.tenant.core.context.TenantContextHolder.getTenantId;
import static cn.gf.saas.module.system.dal.dataobject.dept.DepartmentDTO.NO_ATTRIBUTION_ID;
import static cn.gf.saas.module.system.enums.ErrorCodeConstants.*;
import static cn.gf.saas.module.system.enums.LogRecordConstants.*;
import static cn.gf.saas.module.system.util.tenant.TenantUtil.isCompanyManage;

/**
 * 部门 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DeptServiceImpl implements DeptService {
    @Resource
    private DeptViewMapper deptViewMapper;
    @Resource
    private MemberMapper memberMapper;
    @Resource
    @Lazy
    private PermissionService permissionService;
    @Resource
    private OperateLogService operateLogService;

    @Override
    @LogRecord(type = SYSTEM_DEPT_TYPE, subType = SYSTEM_DEPT_CREATE_SUB_TYPE, bizNo = "",
            success = SYSTEM_DEPT_CREATE_SUCCESS)
    @CacheEvict(cacheNames = RedisKeyConstants.DEPT_CHILDREN_ID_LIST,
            allEntries = true) // allEntries 清空所有缓存，因为操作一个部门，涉及到多个缓存
    public void createDept(DeptSaveReqVO createReqVO) {
        try {
            if (createReqVO.getLevel().equals(1)) {
                DeptApi.createClass1Dept(createReqVO);
            } else {
                DeptApi.createClass2Dept(createReqVO);
            }
        } catch (Exception e) {
            log.error("创建部门失败，请求参数：{},失败原因：{}", JSON.toJSONString(createReqVO), e.getMessage());
            throw exception(DEPT_CREATE_FAIL, e.getMessage());
        }

        // 记录操作日志上下文
        LogRecordContext.putVariable("createReqVO", createReqVO);
    }

    @Override
    @LogRecord(type = SYSTEM_DEPT_TYPE, subType = SYSTEM_DEPT_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.departmentId}}",
            success = SYSTEM_DEPT_UPDATE_SUCCESS)
    @CacheEvict(cacheNames = RedisKeyConstants.DEPT_CHILDREN_ID_LIST,
            allEntries = true) // allEntries 清空所有缓存，因为操作一个部门，涉及到多个缓存
    public void updateDept(DeptSaveReqVO updateReqVO) {
        try {
            if (updateReqVO.getLevel().equals(1)) {
                DeptApi.createClass1Dept(updateReqVO);
            } else {
                DeptApi.createClass2Dept(updateReqVO);
            }
        } catch (Exception e) {
            log.error("更新部门失败，请求参数：{},失败原因：{}", JSON.toJSONString(updateReqVO), e.getMessage());
            throw exception(DEPT_UPDATE_FAIL, e.getMessage());
        }

        // 记录操作日志上下文
        DepartmentDTO oldDepartmentDTO = deptViewMapper.getDepartmentById(Long.valueOf(updateReqVO.getDepartmentId()));
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(oldDepartmentDTO, DeptSaveReqVO.class));
        LogRecordContext.putVariable("oldDepartmentDTO", oldDepartmentDTO);
    }

    /**
     * 获取部门
     *
     * @param req 部门信息
     */
    @Override
    public DeptRespVO getDept(DeptDeleteReqVO req) {
        DepartmentDTO departmentByDepartmentIdAndLevel = deptViewMapper.getDepartmentByDepartmentIdAndLevel(req);
        return BeanUtils.toBean(departmentByDepartmentIdAndLevel, DeptRespVO.class);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchCreateDept(List<DeptImportExcelVO> list) {
        try {
            list.forEach(item -> {
                if (StrUtil.isNotBlank(item.getDeptName())) {
                    item.setDeptName(item.getDeptName().trim());
                }
                if (StrUtil.isNotBlank(item.getParentDeptName())) {
                    item.setParentDeptName(item.getParentDeptName().trim());
                }
            });

            String result = DeptApi.batchCreateDept(list);

            // 插入日志
            operateLogService.remoteSaveDeptLog(list);
            return result;
        } catch (Exception e) {
            log.error("批量导入部门失败，请求参数：{},失败原因：{}", JSON.toJSONString(list), e.getMessage());
            throw exception(DEPT_BATCH_CREATE_FAIL, e.getMessage());
        }
    }




    @Override
    @LogRecord(type = SYSTEM_DEPT_TYPE, subType = SYSTEM_DEPT_DELETE_SUB_TYPE, bizNo = "{{#req.departmentId}}",
            success = SYSTEM_DEPT_DELETE_SUCCESS)
    @CacheEvict(cacheNames = RedisKeyConstants.DEPT_CHILDREN_ID_LIST,
            allEntries = true) // allEntries 清空所有缓存，因为操作一个部门，涉及到多个缓存
    public void deleteDept(DeptDeleteReqVO req) {
        try {
            DeptApi.deleteDept(req);
        } catch (Exception e) {
            log.error("删除部门失败，请求参数：{},失败原因：{}", JSON.toJSONString(req), e.getMessage());
            throw exception(DEPT_DELETE_FAIL, e.getMessage());
        }

        // 记录操作日志上下文
        DepartmentDTO oldDepartmentDTO = deptViewMapper.getDepartmentById(Long.valueOf(req.getDepartmentId()));
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(oldDepartmentDTO, DeptSaveReqVO.class));
        LogRecordContext.putVariable("oldDepartmentDTO", oldDepartmentDTO);
    }

    @Override
    @LogRecord(type = SYSTEM_DEPT_TYPE, subType = SYSTEM_DEPT_DELETE_SUB_TYPE, bizNo = "",
            success = SYSTEM_DEPT_BATCH_DELETE_SUCCESS)
    @CacheEvict(cacheNames = RedisKeyConstants.DEPT_CHILDREN_ID_LIST,
            allEntries = true) // allEntries 清空所有缓存，因为操作一个部门，涉及到多个缓存
    public void batchDeleteDept(List<DeptDeleteReqVO> req) {
        try {
            DeptApi.batchDeleteDept(req);
        } catch (Exception e) {
            log.error("批量删除部门失败，请求参数：{},失败原因：{}", JSON.toJSONString(req), e.getMessage());
            throw exception(DEPT_DELETE_FAIL, e.getMessage());
        }
    }

    @Override
    @LogRecord(type = SYSTEM_DEPT_TYPE, subType = SYSTEM_DEPT_UPDATE_STATUS_SUB_TYPE, bizNo = "{{#req.departmentId}}",
            success = SYSTEM_DEPT_UPDATE_STATUS_SUCCESS)
    public void updateDeptStatus(DeptUpdateStatusReqVO req) {
        try {
            DeptApi.updateDeptStatus(req);
        } catch (Exception e) {
            log.error("更新部门状态失败，请求参数：{},失败原因：{}", JSON.toJSONString(req), e.getMessage());
            throw exception(DEPT_UPDATE_STATUS_FAIL, e.getMessage());
        }
    }


    @Override
    public DepartmentDTO getDept(Long id) {
        return deptViewMapper.getDepartmentById(id);
    }

    @Override
    public DepartmentDTO getDeptByNameAndLevel(String name, Integer level) {
        return deptViewMapper.getDeptByNameAndLevel(name, level, getTenantId());
    }

    @Override
    public List<DepartmentDTO> getDeptList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return deptViewMapper.getDepartmentListByIds(ids, getTenantId());
    }

    /**
     * 根据单位id获取部门列表
     */
    @Override
    public List<DepartmentDTO> getDepartmentListByCompanyId(String companyId) {
        return deptViewMapper.getDepartmentListByCompanyId(companyId);
    }

    @Override
    public List<DepartmentDTO> getChildDeptList(Long id) {
        List<DepartmentDTO> children = new LinkedList<>();
        // 遍历每一层
        Collection<Long> parentIds = Collections.singleton(id);
        for (int i = 0; i < Short.MAX_VALUE; i++) { // 使用 Short.MAX_VALUE 避免 bug 场景下，存在死循环
            // 查询当前层，所有的子部门
            List<DepartmentDTO> depts = deptViewMapper.selectListByParentId(parentIds);
            // 1. 如果没有子部门，则结束遍历
            if (CollUtil.isEmpty(depts)) {
                break;
            }
            // 2. 如果有子部门，继续遍历
            children.addAll(depts);
            parentIds = convertSet(depts, one -> Long.valueOf(one.getDepartmentId()));
        }
        return children;
    }

    @Override
    @DataPermission(enable = false) // 禁用数据权限，避免建立不正确的缓存
    @Cacheable(cacheNames = RedisKeyConstants.DEPT_CHILDREN_ID_LIST + "#600", key = "#id")
    public Set<Long> getChildDeptIdListFromCache(Long id) {
        List<DepartmentDTO> children = getChildDeptList(id);
        return convertSet(children, one -> Long.valueOf(one.getDepartmentId()));
    }

    @Override
    public void validateDeptList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得科室信息
        Map<Integer, DepartmentDTO> deptMap = getDeptMap(ids);
        // 校验
        ids.forEach(id -> {
            DepartmentDTO dept = deptMap.get(id.intValue());
            if (dept == null) {
                throw exception(DEPT_NOT_FOUND);
            }
            if (!dept.getEnabled()) {
                throw exception(DEPT_NOT_ENABLE, String.format(DEPT_NOT_ENABLE.getMsg(), dept.getDepartmentName()));
            }
        });
    }


    @Override
    public List<DepartmentDTO> getDepartmentTree(DeptListReqVO reqVO) {
        List<Long> deptIds = getCompanyVisibleDeptRange(getLoginUserId());
        if (CollUtil.isEmpty(deptIds)) {
            return Collections.emptyList();
        }

        List<DepartmentDTO> allDepartments = deptViewMapper.getDepartmentsByCondition(reqVO.getDepartmentName(), reqVO.getEnabled(), deptIds);
        Map<Integer, Integer> userCounts = memberMapper.getUserCountsByDepartments(allDepartments);

        // 构建树形结构
        Map<String, DepartmentDTO> departmentMap = new HashMap<>();

        List<DepartmentDTO> rootDepartments = new ArrayList<>();

        // 初始化 Map
        for (DepartmentDTO department : allDepartments) {
            department.setChildren(new ArrayList<>());
            departmentMap.put(department.getDepartmentId() + "_" + department.getLevel(), department);
            department.setUserCount(userCounts.getOrDefault(department.getDepartmentId(), 0));
        }

        // 构建父子关系
        for (DepartmentDTO department : allDepartments) {
            Integer parentId = department.getParentId();
            if (parentId == null) {
                // 一级部门，添加到根列表
                rootDepartments.add(department);
            } else {
                // 二级部门，添加到上级部门的 children 列表
                DepartmentDTO parentDepartment = departmentMap.get(parentId + "_" + 1);
                if (parentDepartment != null) {
                    parentDepartment.getChildren().add(department);
                } else {
                    // 搜索情况下：只搜到二级部门，添加到根列表
                    rootDepartments.add(department);
                }
            }
        }

        return rootDepartments;
    }


    @Override
    public List<DeptViewSimpleRespVO> getSimpleDepartmentTree(DeptListReqVO reqVO) {
        List<Long> deptIds = getCompanyVisibleDeptRange(getLoginUserId());
        if (CollUtil.isEmpty(deptIds)) {
            return Collections.emptyList();
        }

        List<DepartmentDTO> allDepartments = deptViewMapper.getDepartmentsByCondition(reqVO.getDepartmentName(), reqVO.getEnabled(), deptIds);

        List<DeptViewSimpleRespVO> simpleRespVOS = BeanUtils.toBean(allDepartments, DeptViewSimpleRespVO.class);

        // 构建树形结构
        Map<String, DeptViewSimpleRespVO> departmentMap = new HashMap<>();

        List<DeptViewSimpleRespVO> rootDepartments = new ArrayList<>();

        // 初始化 Map
        for (DeptViewSimpleRespVO department : simpleRespVOS) {
            department.setChildren(new ArrayList<>());
            departmentMap.put(department.getDepartmentId() + "_" + department.getLevel(), department);
        }

        // 添加无归属部门
        rootDepartments.add(new DeptViewSimpleRespVO()
                .setDepartmentName(DepartmentDTO.NO_ATTRIBUTION)
                .setDepartmentId(NO_ATTRIBUTION_ID)
                .setEnabled(false)
                .setLevel(1));

        // 构建父子关系
        for (DeptViewSimpleRespVO department : simpleRespVOS) {
            Integer parentId = department.getParentId();
            if (parentId == null) {
                // 一级部门，添加到根列表
                rootDepartments.add(department);
            } else {
                // 二级部门，添加到上级部门的 children 列表
                DeptViewSimpleRespVO parentDepartment = departmentMap.get(parentId + "_" + 1);
                if (parentDepartment != null) {
                    if (!parentDepartment.getEnabled()) {
                        department.setEnabled(false);
                    }
                    parentDepartment.getChildren().add(department);
                } else {
                    // 搜索情况下：只搜到二级部门，添加到根列表
                    rootDepartments.add(department);
                }
            }
        }

        return rootDepartments;
    }


    @Override
    public List<DeptViewSimpleRespVO> getOneLevelList() {
        List<DepartmentDTO> deptByCompanyIdAndLevel = deptViewMapper.getDeptByCompanyIdAndLevel(getTenantId(), 1);
        return BeanUtils.toBean(deptByCompanyIdAndLevel, DeptViewSimpleRespVO.class);
    }

    /**
     * 获取可查看的部门列表
     */
    @Nullable
    private List<Long> getCompanyVisibleDeptRange(Long userId) {
        DeptDataPermissionRespDTO deptDataPermission = permissionService.getDeptDataPermission(userId);
        if (deptDataPermission.getAll()) {
            if (isCompanyManage()) {
                List<DepartmentDTO> deptList = deptViewMapper.getDeptByCompanyIdAndLevel(getTenantId(), null);
                if (CollUtil.isEmpty(deptList)) {
                    return Collections.emptyList();
                }
                return deptList
                        .stream()
                        .map(one -> Long.valueOf(one.getDepartmentId()))
                        .collect(Collectors.toList());
            }
        }

        if (deptDataPermission.getSelf()) {
            return Collections.singletonList(getLoginUserDeptId());
        }

        if (deptDataPermission.getDeptIds() != null) {
            return CollUtil.newArrayList(deptDataPermission.getDeptIds());
        }

        return Collections.emptyList();
    }

}
