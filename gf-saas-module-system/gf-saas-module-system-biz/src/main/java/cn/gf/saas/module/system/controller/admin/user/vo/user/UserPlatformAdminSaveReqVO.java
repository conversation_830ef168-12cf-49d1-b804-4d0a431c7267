package cn.gf.saas.module.system.controller.admin.user.vo.user;

import cn.gf.saas.framework.common.validation.Mobile;
import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Schema(description = "管理后台 - 平台管理员创建/修改 Request VO")
@Data
public class UserPlatformAdminSaveReqVO {

    @Schema(description = "用户编号", example = "1024")
    private Long id;

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "开发")
    @Size(max = 20, message = "用户昵称长度不能超过20个字符")
    @DiffLogField(name = "用户昵称")
    private String nickname;

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "gf-saas")
    @NotBlank(message = "用户账号不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9]{2,20}$", message = "请输入数字或者字母，2-20个字符")
    @Size(max = 20, message = "用户账号长度不能超过20个字符")
    @DiffLogField(name = "用户账号")
    private String username;

    @Schema(description = "手机号码", example = "15601691300")
    @Mobile
    @DiffLogField(name = "手机号码")
    private String mobile;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @Length(min = 8, max = 20, message = "密码长度为 8-20 位")
    @Pattern(regexp = "^[a-zA-Z0-9]{8,20}$", message = "用户账号由 数字、字母 组成")
    private String password;

    @Schema(description = "账号状态")
    private Integer status;
}
