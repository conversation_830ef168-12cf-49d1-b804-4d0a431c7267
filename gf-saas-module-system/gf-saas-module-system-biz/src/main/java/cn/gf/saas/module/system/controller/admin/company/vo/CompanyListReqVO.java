package cn.gf.saas.module.system.controller.admin.company.vo;

import cn.gf.saas.framework.common.pojo.PageParam;
import cn.gf.saas.framework.excel.core.annotations.DictFormat;
import cn.gf.saas.module.system.enums.DictTypeConstants;
import cn.gf.saas.module.system.enums.packageenum.CompanyPackStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Set;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 单位列表 Request VO")
@Data
public class CompanyListReqVO extends PageParam {
    @Schema(description = "单位全称/单位简称，模糊匹配", example = "工福")
    private String keyWord;

    @Schema(description = "单位全称，模糊匹配", example = "工福")
    private String companyName;

    @Schema(description = "单位简称，模糊匹配", example = "工福")
    private String companyShortName;

    @Schema(description = "单位id", example = "1")
    private String companyId;

    @Schema(description = "查询目标单位id的下级单位", example = "1")
    private String parentId;

    @Schema(description = "查询目标单位id的所有子单位", example = "1")
    private String targetCompanyId;

    @Schema(description = "套餐名", example = "1")
    private String packageName;

    @Schema(description = "套餐类型", example = "true")
    private Boolean packageIsPublic;

    @Schema(description = "单位负责人账号状态")
    private Integer companyLeaderStatus;

    /**
     * see {@link CompanyPackStatusEnum}
     * */
    @Schema(description = "单位套餐状态")
    private Integer companyPackageStatus;

    @Schema(description = "单位负责人名称")
    private String companyLeaderNickName;

    @Schema(description = "单位负责人手机号")
    private String companyLeaderMobile;

    @Schema(description = "所属渠道名称")
    private String tenantName;

    @Schema(description = "当前用户数据范围")
    private Set<String> currentUserDataScope;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @Schema(description = "修改时间")
    private LocalDateTime[] updateTime;

}
