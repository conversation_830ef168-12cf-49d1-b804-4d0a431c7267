package cn.gf.saas.module.system.controller.admin.member.vo;

import cn.gf.saas.framework.common.validation.PwdPattern;
import cn.gf.saas.framework.common.validation.ValidIdCard;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

import static cn.gf.saas.framework.common.util.date.DateUtils.*;

@Schema(description = "管理后台 - 会员创建/修改 Request VO")
@Data
public class MemeberSaveReqVO {

    @Schema(description = "姓名")
    @NotBlank(message = "姓名不能为空")
    @Size(max = 20, message = "姓名长度不能超过20个字符")
    @DiffLogField(name = "姓名")
    private String realName;              // 真实姓名

    @Schema(description = "用户性别")
    @NotNull(message = "性别不能为空")
    @DiffLogField(name = "用户性别")
    private Integer gender;               // 用户性别

    @Schema(description = "用户编号", example = "1024")
    private String userId;                // 用户编号

    @Schema(description = "登录帐号")
    @NotBlank(message = "登录帐号不能为空")
    @Pattern(regexp = "^[\\p{L}\\p{N}]{2,20}$", message = "限制2-20个字，不可输入特殊字符标点符号等")
    @DiffLogField(name = "登录帐号")
    private String account;               // 登录帐号

    @Schema(description = "登录密码")
    @DiffLogField(name = "登录密码")
    private String pwdLogin;              // 登录密码
    @Schema(description = "是否允许密码方式登录")
    @NotNull(message = "是否允许密码方式登录不能为空")
    @DiffLogField(name = "是否允许密码方式登录")
    private Boolean pwdModeEnabled;
    @Schema(description = "是否是真实手机号：1 真实 2 虚拟")
    @NotNull(message = "是否是手机号不能为空")
    @DiffLogField(name = "是否是真实手机号")
    private Integer mobileMode;
    @Schema(description = "手机号码")
    @DiffLogField(name = "手机号码")
    @Length(min = 11, max = 11, message = "手机号码长度为 11 位")
    @NotBlank(message = "手机号不能为空")
    private String mobilePhone;           // 手机号码

    @Schema(description = "身份证号")
    @ValidIdCard(message = "请输入18位身份证号码")
    @DiffLogField(name = "身份证号")
    private String uid;                   // 身份证号
    @Schema(description = "出生日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE, timezone = TIME_ZONE_DEFAULT)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class) // 反序列化（请求）
    @DiffLogField(name = "出生日期")
    private LocalDateTime birthday;                // 出生日期
    @Schema(description = "用户婚姻状况")
    @DiffLogField(name = "用户婚姻状况")
    private String maritalStatus;         // 用户婚姻状况
    @Schema(description = "一级部门名称")
    @DiffLogField(name = "一级部门名称")
    private String departLevel1;          // 一级部门名称
    @Schema(description = "二级部门名称")
    @DiffLogField(name = "二级部门名称")
    private String departLevel2;          // 二级部门名称
    @Schema(description = "在职情况")
    @DiffLogField(name = "在职情况")
    private String officeStatus;          // 在职情况
    @Schema(description = "入职日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE, timezone = TIME_ZONE_DEFAULT)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class) // 反序列化（请求）
    @DiffLogField(name = "入职日期")
    private LocalDateTime entryDate;               // 入职日期
    @Schema(description = "离职日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE, timezone = TIME_ZONE_DEFAULT)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class) // 反序列化（请求）
    @DiffLogField(name = "离职日期")
    private LocalDateTime quitDate;                // 离职日期
    @Schema(description = "是否可用")
    @DiffLogField(name = "是否可用")
    private Boolean enabled;              // 是否可用
    @Schema(description = "禁用开始时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class) // 反序列化（请求）
    @DiffLogField(name = "禁用开始时间")
    private LocalDateTime disableTime;
    @TableField(value = "companyId")
    private String companyId;             // 所在公司

    @PwdPattern(regexp = "^[a-zA-Z0-9]{6,10}$")
    public String getPwdLogin() {
        if (StrUtil.isBlank(userId) && pwdModeEnabled != null && pwdModeEnabled) {
            if (StrUtil.isBlank(pwdLogin) && pwdLogin.equals("******")) {
                throw new RuntimeException("新增密码不能为******");
            } else {
                return StrUtil.isBlank(pwdLogin) ? "" : pwdLogin;
            }
        } else {
            return StrUtil.isBlank(pwdLogin) ? "" : pwdLogin;
        }
    }
}
