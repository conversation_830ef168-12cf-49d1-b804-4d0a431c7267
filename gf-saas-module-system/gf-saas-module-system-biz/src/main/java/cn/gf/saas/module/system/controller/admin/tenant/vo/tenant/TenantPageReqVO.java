package cn.gf.saas.module.system.controller.admin.tenant.vo.tenant;

import cn.gf.saas.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Set;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 租户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantPageReqVO extends PageParam {

    @Schema(description = "租户名", example = "工福")
    private String name;

    @Schema(description = "渠道负责人id", example = "开发")
    private Long leaderUserId;

    @Schema(description = "渠道负责人昵称", example = "开发")
    private String nickname;

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "gf-saas")
    private String username;

    @Schema(description = "手机号码", example = "15601691300")
    private String mobile;

    @Schema(description = "数据权限-单位名", example = "开发")
    private String companyName;

    @Schema(description = "租户状态（0正常 1停用）", example = "1")
    private Integer status;

    @Schema(description = "单位ids", example = "1")
    private Set<String> companyIds;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @Schema(description = "修改时间")
    private LocalDateTime[] updateTime;

    @Schema(description = "忽略的渠道id", example = "开发")
    private String ignoreTenantId;
}
