package cn.gf.saas.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 租户精简 Response VO")
@Data
public class TenantSimpleRespVO {

    @Schema(description = "租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String tenantId;

    @Schema(description = "租户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "工福")
    private String tenantName;

}
