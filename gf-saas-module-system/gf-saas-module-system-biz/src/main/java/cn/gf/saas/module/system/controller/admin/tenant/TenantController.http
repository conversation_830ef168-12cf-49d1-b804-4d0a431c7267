### 获取租户编号 /admin-api/system/get-id-by-name
GET {{baseUrl}}/system/tenant/get-id-by-name?name=工福源码

### 创建租户 /admin-api/system/tenant/create
POST {{baseUrl}}/system/tenant/create
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}

{
  "name": "工福",
  "contactName": "工福",
  "contactMobile": "***********",
  "status": 0,
  "domain": "https://www.iocoder.cn",
  "packageId": 110,
  "expireTime": *************,
  "accountCount": 20,
  "username": "admin",
  "password": "123321"
}
