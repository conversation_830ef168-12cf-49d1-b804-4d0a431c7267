package cn.gf.saas.module.system.controller.admin.tenant.vo.tenant;

import cn.gf.saas.framework.excel.core.annotations.DictFormat;
import cn.gf.saas.module.system.controller.admin.company.vo.CompanySimpleRespVO;
import cn.gf.saas.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Schema(description = "管理后台 - 渠道 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantRespVO {

    @Schema(description = "渠道编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("渠道编号")
    private String id;

    @Schema(description = "渠道名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "工福")
    @ExcelProperty("渠道名称")
    private String name;

    @Schema(description = "负责人id", requiredMode = Schema.RequiredMode.REQUIRED, example = "开发")
    private Long leaderUserId;

    @Schema(description = "负责人", requiredMode = Schema.RequiredMode.REQUIRED, example = "开发")
    @ExcelProperty("负责人")
    private String nickname;

    @Schema(description = "联系手机", example = "15601691300")
    @ExcelProperty("联系手机")
    private String mobile;

    @Schema(description = "登录名", requiredMode = Schema.RequiredMode.REQUIRED, example = "开发")
    @ExcelProperty("登录名")
    private String userName;

    @Schema(description = "数据权限", example = "**有限公司")
    @ExcelProperty("数据权限")
    private String companyNames;

    @Schema(description = "数据权限", example = "**")
    private Set<String> companyIds;

    @Schema(description = "渠道状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    //@ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

    @Schema(description = "数据权限结构树")
    private List<CompanySimpleRespVO> tree;

    @Schema(description = "修改时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("修改时间")
    @ColumnWidth(value = 18)
    private LocalDateTime updateTime;

    @Schema(description = "最后修改人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最后修改人")
    private String updater;

}
