package cn.gf.saas.module.system.controller.admin.company.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.gf.saas.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "管理后台 - 单位套餐信息 Request VO")
@Data
public class CompanyPackageReqVO {
    @Schema(description = "单位id")
    private String companyid;

    @Schema(description = "套餐Id")
    @NotNull(message = "套餐Id不能为空")
    private Long packageId;

    @Schema(description = "套餐类型")
    @NotNull(message = "套餐类型不能为空")
    private Boolean isPublic;

    @Schema(description = "单位负责人Id")
    private Long companyUserId;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class) // 反序列化（请求）
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @Schema(description = "套餐有效期开始日期")
    @NotNull(message = "套餐有效期开始日期不能为空")
    private LocalDateTime packageStartDate;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class) // 反序列化（请求）
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @Schema(description = "套餐有效期结束日期")
    @NotNull(message = "套餐有效期结束日期不能为空")
    private LocalDateTime packageEndDate;
}
