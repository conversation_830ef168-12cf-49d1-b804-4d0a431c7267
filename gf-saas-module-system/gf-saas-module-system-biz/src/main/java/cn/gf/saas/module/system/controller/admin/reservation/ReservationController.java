package cn.gf.saas.module.system.controller.admin.reservation;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.ratelimiter.core.annotation.RateLimiter;
import cn.gf.saas.framework.ratelimiter.core.keyresolver.impl.ClientIpRateLimiterKeyResolver;
import cn.gf.saas.module.system.controller.admin.reservation.vo.ReservationCreateReqVO;
import cn.gf.saas.module.system.service.reservation.ReservationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - saas官网")
@RestController
@RequestMapping("/system/official-website")
@Validated
public class ReservationController {

    @Resource
    private ReservationService reservationService;

    @PostMapping("/create-reservation")
    @Operation(summary = "创建体验预约")
    @PermitAll
    @RateLimiter(count = 10, time = 60, keyResolver = ClientIpRateLimiterKeyResolver.class)
    public CommonResult<Long> createReservation(@Valid @RequestBody ReservationCreateReqVO createReqVO) {
        return success(reservationService.createReservation(createReqVO));
    }
} 