package cn.gf.saas.module.system.api.company;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.framework.tenant.core.aop.TenantIgnore;
import cn.gf.saas.module.system.api.company.dto.AppAuthCompanyDepartmentRespDTO;
import cn.gf.saas.module.system.api.company.dto.CompanyConfigAuthLoginRespDTO;
import cn.gf.saas.module.system.api.company.dto.CompanyReqDTO;
import cn.gf.saas.module.system.api.company.dto.CompanyRespDTO;
import cn.gf.saas.module.system.controller.admin.company.vo.CompanyRespVO;
import cn.gf.saas.module.system.controller.admin.company.vo.CompanySimpleRespVO;
import cn.gf.saas.module.system.service.company.CompanyConfigAuthLoginService;
import cn.gf.saas.module.system.service.company.CompanyService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class CompanyApiImpl implements CompanyApi {

    @Resource
    private CompanyService companyService;
    @Resource
    private CompanyConfigAuthLoginService configService;

    @Override
    public CommonResult<CompanyRespDTO> getCompanyById(String companyId) {
        CompanyRespVO companyRespVO = companyService.getCompany(companyId);
        return success(BeanUtils.toBean(companyRespVO, CompanyRespDTO.class));
    }

    @Override
    public CommonResult<Map<Long, List<String>>> getCompanyIdsByMenuId(Set<Long> menuIds){
        return success(companyService.getCompanyIdsByMenuId(menuIds));
    }

    @Override
    public CommonResult updateCompanyMenuById(String companyId, Long menuId){
        companyService.updateCompanyMenuById(companyId, menuId);
        return success();
    }

    @Override
    public CommonResult<List<String>> getAllParentByCompanyId(String companyId){
        return success(companyService.getAllParentByCompanyId(companyId));
    }

    @Override
    public CommonResult<List<CompanyRespDTO>> getCompanyByIds(Set<String> companyIds) {
        List<CompanySimpleRespVO> companyRespVO = companyService.getCompanyListByIds(companyIds);
        return success(BeanUtils.toBean(companyRespVO, CompanyRespDTO.class));
    }

    @Override
    public CommonResult<List<CompanyRespDTO>> getCompanyByIdsAndSource(Set<String> companyIds, String source) {
        return success(companyService.getCompanyListByIdsAndSource(companyIds, source));
    }

    @Override
    public CommonResult<PageResult<CompanyRespDTO>> getCompanyPageByIdsAndSource(CompanyReqDTO companyReqDTO) {
        return success(companyService.getCompanyPageByIdsAndSource(companyReqDTO));
    }

    @Override
    @TenantIgnore
    public CommonResult<AppAuthCompanyDepartmentRespDTO> getCompanyDepartmentNgtenant(String companyId) {
        return success(companyService.getCompanyDepartment(companyId));
    }

    @Override
    @TenantIgnore
    public CommonResult<CompanyConfigAuthLoginRespDTO> getConfigByLinkParamNgtenant(String c) {
        return success(configService.getConfigByLinkParam(c));
    }

}
