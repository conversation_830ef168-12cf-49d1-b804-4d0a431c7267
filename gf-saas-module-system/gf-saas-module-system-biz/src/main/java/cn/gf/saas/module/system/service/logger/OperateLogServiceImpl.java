package cn.gf.saas.module.system.service.logger;

import cn.gf.saas.framework.common.util.monitor.TracerUtils;
import cn.gf.saas.module.system.controller.admin.dept.vo.dept.DeptImportExcelVO;
import cn.gf.saas.module.system.controller.admin.logger.vo.operatelog.OperateLogPageReqVO;
import cn.gf.saas.module.system.dal.dataobject.dept.DepartmentDTO;
import cn.gf.saas.module.system.dal.dataobject.logger.OperateLogDO;
import cn.gf.saas.module.system.dal.mysql.dept.DeptViewMapper;
import cn.gf.saas.module.system.dal.mysql.logger.OperateLogMapper;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.system.api.logger.dto.OperateLogCreateReqDTO;
import cn.gf.saas.module.system.api.logger.dto.OperateLogPageReqDTO;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.mzt.logapi.beans.LogRecord;
import com.mzt.logapi.service.ILogRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.gf.saas.framework.operatelog.core.service.LogRecordServiceImpl.*;
import static cn.gf.saas.framework.tenant.core.context.TenantContextHolder.getTenantId;
import static cn.gf.saas.module.system.enums.LogRecordConstants.SYSTEM_DEPT_CREATE_SUB_TYPE;
import static cn.gf.saas.module.system.enums.LogRecordConstants.SYSTEM_DEPT_TYPE;

/**
 * 操作日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class OperateLogServiceImpl implements OperateLogService {

    @Resource
    private OperateLogMapper operateLogMapper;
    @Resource
    private ILogRecordService iLogRecordService;
    @Resource
    private DeptViewMapper deptViewMapper;

    @Override
    public void createOperateLog(OperateLogCreateReqDTO createReqDTO) {
        OperateLogDO log = BeanUtils.toBean(createReqDTO, OperateLogDO.class);
        operateLogMapper.insert(log);
    }

    @Override
    public PageResult<OperateLogDO> getOperateLogPage(OperateLogPageReqVO pageReqVO) {
        return operateLogMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<OperateLogDO> getOperateLogPage(OperateLogPageReqDTO pageReqDTO) {
        return operateLogMapper.selectPage(pageReqDTO);
    }

    @Override
    public OperateLogDO getOperateLogByBizNo(String bizId) {
        return operateLogMapper.selectLastByBizId(bizId);
    }


    @Async
    public void remoteSaveDeptLog(List<DeptImportExcelVO> list) {
        try {
            list.forEach(item -> {
                String deptName = StrUtil.isEmpty(item.getParentDeptName())
                        ? item.getDeptName() : StrUtil.isEmpty(item.getDeptName()) ? item.getParentDeptName() : item.getDeptName();
                DepartmentDTO departmentDTO = deptViewMapper.getDeptByNameAndLevel(deptName, null, getTenantId());
                com.mzt.logapi.beans.LogRecord logRecord = new com.mzt.logapi.beans.LogRecord();
                logRecord.setAction("创建了部门【" + item.getDeptName() + "】，上级部门【" + item.getParentDeptName() + "】");
                logRecord.setType(SYSTEM_DEPT_TYPE);
                logRecord.setSubType(SYSTEM_DEPT_CREATE_SUB_TYPE);
                logRecord.setBizNo(String.valueOf(departmentDTO.getDepartmentId()));
                this.record(logRecord);
            });
        }catch (Exception e){
            log.error("保存日志失败，请求参数：{},失败原因：{}", JSON.toJSONString(list), e.getMessage());
        }
    }


    public void record(LogRecord logRecord) {
        // 1. 补全通用字段
        OperateLogCreateReqDTO reqDTO = new OperateLogCreateReqDTO();
        reqDTO.setTraceId(TracerUtils.getTraceId());
        // 补充用户信息
        fillUserFields(reqDTO);
        // 补全模块信息
        fillModuleFields(reqDTO, logRecord);
        // 补全请求信息
        fillRequestFields(reqDTO);

        // 2. 异步记录日志
        this.createOperateLog(reqDTO);
    }
}
