package cn.gf.saas.module.system.dal.dataobject.tenant;

import cn.gf.saas.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 租户单位 DO
 * 该表存的是租户与所关联单位的数据
 *
 * <AUTHOR>
 */

@TableName(value = "system_tenant_company", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantCompanyDO extends BaseDO {
    /**
     * 主键id，自增
     */
    @TableId
    private Long id;

    /**
     * 单位id
     */
    private String companyId;
    /**
     * 渠道id
     */
    private String tenantId;

    /**
     * 渠道名称
     */
    private String tenantName;

}
