package cn.gf.saas.module.system.controller.admin.member.vo;

import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class MemberBatchUpdateDeptRespVO {
    @Schema(description = "部门ID")
    @NotNull(message = "部门ID不能为空")
    @DiffLogField(name = "部门ID")
    private Integer departmentId;

    @Schema(description = "上级部门ID")
    @DiffLogField(name = "上级部门ID")
    private Integer parentId;

    @Schema(description = "用户编号列表")
    @NotNull(message = "用户编号列表不能为空")
    @DiffLogField(name = "用户编号列表")
    private List<String> userIds;

}
