package cn.gf.saas.module.system.controller.admin.dept.vo.dept;

import cn.gf.saas.module.system.dal.dataobject.dept.DepartmentDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 组织信息 Response VO")
@Data
public class DeptRespVO {
    @Schema(description = "组织ID", example = "1024")
    private Integer departmentId;

    @Schema(description = "公司ID，仅一级组织有效")
    private String companyId;

    @Schema(description = "组织名称")
    private String departmentName;

    @Schema(description = "是否启用")
    private Boolean enabled;

    @Schema(description = "组织编码")
    private String encode;

    @Schema(description = "上级组织ID")
    private Integer parentId;

    @Schema(description = "上级部门Name")
    private String parentName;

    @Schema(description = "组织级别（1：一级组织，2：二级组织）")
    private Integer level;

    @Schema(description = "组织人数")
    private Long userCount;

    @Schema(description = "部门级数，默认1级。最大允许2")
    private Long departmentClass;

    @Schema(description = "子组织列表")
    private List<DepartmentDTO> children;


}
