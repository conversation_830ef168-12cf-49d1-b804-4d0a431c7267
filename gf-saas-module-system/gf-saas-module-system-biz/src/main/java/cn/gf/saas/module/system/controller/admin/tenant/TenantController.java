package cn.gf.saas.module.system.controller.admin.tenant;

import cn.gf.saas.framework.apilog.core.annotation.ApiAccessLog;
import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageParam;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.excel.core.util.ExcelUtils;
import cn.gf.saas.module.system.controller.admin.tenant.vo.tenant.*;
import cn.gf.saas.module.system.service.tenant.TenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.gf.saas.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 渠道")
@RestController
@RequestMapping("/system/tenant")
public class TenantController {

    @Resource
    private TenantService tenantService;

    @GetMapping("/get-id-by-name")
    @PermitAll
    @Operation(summary = "使用用户名，获得渠道编号", description = "登录界面，根据用户名，获得渠道编号")
    @Parameter(name = "name", description = "渠道名", required = true, example = "1024")
    public CommonResult<String> getTenantIdByName(@RequestParam("name") String name) {
        String tenantId = tenantService.getTenantIdByName(name, true);
        return success(tenantId);
    }

    @PostMapping("/create")
    @Operation(summary = "创建渠道")
    @PreAuthorize("@ss.hasPermission('system:tenant:create')")
    public CommonResult<String> createTenant(@Valid @RequestBody TenantSaveReqVO createReqVO) {
        return success(tenantService.createTenant(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新渠道")
    @PreAuthorize("@ss.hasPermission('system:tenant:update')")
    public CommonResult<Boolean> updateTenant(@Valid @RequestBody TenantSaveReqVO updateReqVO) {
        tenantService.updateTenant(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-user")
    @Operation(summary = "更新渠道用户信息")
    @PreAuthorize("@ss.hasPermission('system:tenant:update')")
    public CommonResult<Boolean> updateTenant(@Valid @RequestBody TenantUserUpdateReqVO updateReqVO) {
        tenantService.updateTenantUserInfo(updateReqVO);
        return success(true);
    }


    @DeleteMapping("/delete")
    @Operation(summary = "删除渠道")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant:delete')")
    public CommonResult<Boolean> deleteTenant(@RequestParam("id") String id) {
        tenantService.deleteTenant(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得渠道")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('system:tenant:query')")
    public CommonResult<TenantRespVO> getTenant(@RequestParam("id") String id) {
        return success(tenantService.getTenantDetail(id));
    }

    @GetMapping("/page")
    @Operation(summary = "获得渠道分页")
    @PreAuthorize("@ss.hasPermission('system:tenant:list')")
    public CommonResult<PageResult<TenantRespVO>> getTenantPage(@Valid TenantPageReqVO pageVO) {
        return success(tenantService.getTenantPage(pageVO));
    }

    @GetMapping("/get-all-list")
    @Operation(summary = "获得所有渠道")
    //@PreAuthorize("@ss.hasPermission('system:tenant:query')")
    public CommonResult<List<TenantRespVO>> getAllTenantList() {
        return success(tenantService.getAllTenant());
    }

    @GetMapping("/get-current-list")
    @Operation(summary = "获得当前用户的精简渠道信息")
    //@PreAuthorize("@ss.hasPermission('system:tenant:query')")
    public CommonResult<List<TenantSimpleRespVO>> getCurrentTenantList() {
        return success(tenantService.getCurrentTenantList());
    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出渠道 Excel")
    @PreAuthorize("@ss.hasPermission('system:tenant:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTenantExcel(@Valid TenantPageReqVO exportReqVO,
                                  HttpServletResponse response) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TenantRespVO> list = tenantService.getTenantPage(exportReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "渠道.xls", "数据", TenantRespVO.class, list);
    }

}
