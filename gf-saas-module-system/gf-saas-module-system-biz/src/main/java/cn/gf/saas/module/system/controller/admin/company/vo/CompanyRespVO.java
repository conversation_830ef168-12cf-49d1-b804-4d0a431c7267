package cn.gf.saas.module.system.controller.admin.company.vo;

import cn.gf.saas.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 单位信息 Response VO")
@Data
public class CompanyRespVO {

    @Schema(description = "简称")
    private String shortname;

    @Schema(description = "全称")
    private String fullname;

    @Schema(description = "单位id")
    private String companyid;

    @Schema(description = "套餐名")
    private String packageName;

    @Schema(description = "套餐类型")
    private Boolean isPublic;

    @Schema(description = "套餐类型")
    private String isPublicStr;

    @Schema(description = "单位负责人名称")
    private String nickname;

    @Schema(description = "单位负责人手机号")
    private String mobile;

    @Schema(description = "单位负责人登录名")
    private String username;

    @Schema(description = "所属渠道名称")
    private String tenantName;

    @Schema(description = "是否删除")
    private Integer deleted;

    @Schema(description = "父级id")
    private String parentid;

    @Schema(description = "父级Name")
    private String parentName;

    @Schema(description = "套餐id")
    private Long packageId;

    @Schema(description = "单位负责人id")
    private Long companyUserId;

    @Schema(description = "单位负责人状态")
    private Integer status;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "最后操作时间")
    @ColumnWidth(value = 18)
    private LocalDateTime updateTime;

    @Schema(description = "最后操作人")
    private String updater;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "子级单位")
    private List<CompanyRespVO> children; // 添加 children 字段

    @Schema(description = "树级id")
    private String traceId;

    @Schema(description = "渠道信息")
    private TenantSimpleRespVO tenant;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @Schema(description = "套餐有效期开始日期")
    private LocalDateTime packageStartDate;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @Schema(description = "套餐有效期结束日期")
    private LocalDateTime packageEndDate;

    @Schema(description = "套餐状态信息")
    private String packageStatusStr;

    @Schema(description = "是否关联单位负责人")
    private Boolean isReleCompanyLeader;

    @Schema(description = "是否展示详情")
    private Boolean isShowDetail = true;

    @Schema(description = "是否有权限选中，注意：不可选的说明是没有权限的，但是需要展示出来的")
    private Boolean isPermissionSelected = true;

    @Schema(description = "默认菜单id")
    private Long menuid;

    @Schema(description = "来源")
    private String source;
}
