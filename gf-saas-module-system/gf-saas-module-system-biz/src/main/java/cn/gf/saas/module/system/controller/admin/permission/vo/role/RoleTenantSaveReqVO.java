package cn.gf.saas.module.system.controller.admin.permission.vo.role;

import cn.gf.saas.framework.common.enums.CommonStatusEnum;
import cn.gf.saas.framework.common.validation.InEnum;
import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Set;

@Schema(description = "管理后台 - 渠道角色创建/更新 Request VO")
@Data
public class RoleTenantSaveReqVO {
    @Schema(description = "角色编号", example = "1")
    private Long id;

    @Schema(description = "角色名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "管理员")
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 30, message = "角色名称长度不能超过 30 个字符")
    @DiffLogField(name = "角色名称")
    private String name;

    //@NotBlank(message = "角色标志不能为空")
    //@Size(max = 100, message = "角色标志长度不能超过 100 个字符")
    @Schema(description = "角色编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "ADMIN")
    @DiffLogField(name = "角色标志")
    private String code;

    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    //@NotNull(message = "显示顺序不能为空")
    @DiffLogField(name = "显示顺序")
    private Integer sort;

    @Schema(description = "角色类型")
    private Integer type;

    @Schema(description = "角色状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "角色状态不能为空")
    @DiffLogField(name = "角色状态")
    @InEnum(value = CommonStatusEnum.class, message = "数据范围必须是 {value}")
    private Integer status;

    @Schema(description = "数据权限")
    private Set<String> dataScopeDeptIds;

    @Schema(description = "所属渠道Id")
    @NotBlank(message = "所属渠道不能为空")
    private String tenantId;

    @Schema(description = "备注", example = "我是一个角色")
    @DiffLogField(name = "备注")
    private String remark;
}
