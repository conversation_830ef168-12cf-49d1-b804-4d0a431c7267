package cn.gf.saas.module.system.controller.admin.member.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

import static cn.gf.saas.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员详情 Request VO")
@Data
public class MemeberQueryDetailRespVO {
    @Schema(description = "姓名")
    private String realName;              // 真实姓名

    @Schema(description = "用户性别")
    private Integer gender;               // 用户性别

    @Schema(description = "用户编号", example = "1024")
    private String userId;                // 用户编号

    @Schema(description = "登录帐号")
    @NotBlank(message = "登录帐号不能为空")
    @Pattern(regexp = "^[\\p{L}\\p{N}]{2,20}$", message = "限制2-20个字，不可输入特殊字符标点符号等")
    private String account;               // 登录帐号

    @Schema(description = "登录密码")
    @Pattern(regexp = "^[a-zA-Z0-9]{6,10}$", message = "请输入6-10位数字+字母")
    private String pwdLogin;              // 登录密码

    @Schema(description = "是否允许密码方式登录")
    private Boolean pwdModeEnabled;

    @Schema(description = "是否是真实手机号：1 真实 2 虚拟")
    private Integer mobileMode;

    @Schema(description = "手机号码")
    @DiffLogField(name = "手机号码")
    @Length(min = 11, max = 11, message = "手机号码长度为 11 位")
    @NotNull(message = "手机号码")
    private String mobilePhone;           // 手机号码

    @Schema(description = "身份证号")
    //@IdentityCard(message = "请输入有效的身份证号码")
    @Pattern(regexp = "^[a-zA-Z0-9]{18}$", message = "请输入18位身份证号码")
    private String uid;                   // 身份证号

    @Schema(description = "出生日期")
    @JsonSerialize(using = LocalDateTimeSerializer.class) // 序列化（响应）
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime birthday;                // 出生日期

    @Schema(description = "用户婚姻状况")
    private String maritalStatus;         // 用户婚姻状况

    @Schema(description = "一级部门名称")
    private String departLevel1;          // 一级部门名称

    @Schema(description = "二级部门名称")
    private String departLevel2;          // 二级部门名称

    @Schema(description = "一级部门Id")
    private Integer departLevel1Id;          // 一级部门Id

    @Schema(description = "二级部门Id")
    private Integer departLevel2Id;          // 二级部门Id

    @Schema(description = "在职情况")
    private String officeStatus;          // 在职情况

    @Schema(description = "入职日期")
    @JsonSerialize(using = LocalDateTimeSerializer.class) // 序列化（响应）
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime entryDate;               // 入职日期

    @Schema(description = "离职日期")
    @JsonSerialize(using = LocalDateTimeSerializer.class) // 序列化（响应）
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime quitDate;                // 离职日期

    @Schema(description = "是否可用")
    private Boolean enabled;              // 是否可用

    @Schema(description = "禁用开始时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class) // 序列化（响应）
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime disableTime;

    @TableField(value = "companyId")
    private String companyId;
}
