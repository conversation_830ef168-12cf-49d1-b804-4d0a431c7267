// src/main/java/cn/gf/saas/module/app/controller/admin/config/CompanyConfigAuthLoginController.java
package cn.gf.saas.module.system.controller.admin.company;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.system.controller.admin.company.vo.CompanyConfigAuthLoginReqVO;
import cn.gf.saas.module.system.controller.admin.company.vo.CompanyConfigAuthLoginRespVO;
import cn.gf.saas.module.system.dal.dataobject.company.CompanyConfigAuthLoginDO;
import cn.gf.saas.module.system.service.company.CompanyConfigAuthLoginService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "公司配置 - 认证登录")
@RestController
@RequestMapping("/system/company-config-auth-login")
@Validated
@Slf4j
public class CompanyConfigAuthLoginController {

    @Resource
    private CompanyConfigAuthLoginService configService;

    @PostMapping("/add")
    @Operation(summary = "新增配置")
    public CommonResult<Void> addConfig(@RequestBody @Valid CompanyConfigAuthLoginReqVO config) {
        configService.addConfig(BeanUtils.toBean(config, CompanyConfigAuthLoginDO.class));
        return success();
    }

    @GetMapping("/get-by-company-id/{companyId}")
    @Operation(summary = "根据单位ID获取配置")
    public CommonResult<CompanyConfigAuthLoginRespVO> getConfigByCompanyId(@PathVariable String companyId) {
        return success(configService.getConfigByCompanyId(companyId));
    }


}
