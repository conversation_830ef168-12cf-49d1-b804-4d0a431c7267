package cn.gf.saas.module.system.controller.admin.company.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "公司认证登录配置请求对象")
public class CompanyConfigAuthLoginReqVO {

    @Schema(description = "单位ID", required = true)
    @NotBlank(message = "单位ID不能为空")
    private String companyId;

    @Schema(description = "单位参数(c =\"\")", required = false)
    private String linkParameter;

    @Schema(description = "单位参数不可修改", required = false)
    private Boolean linkParameterFixed;

    @Schema(description = "登录Banner图", required = false)
    private String loginBanner;

    @Schema(description = "登录方式 一：手机号验证码登录+账号密码登录 二：仅手机号验证码登录 三：仅账号密码登录", required = false)
    private Integer loginMode;

    @Schema(description = "是否启用注册入口（1：启用；0：关闭）", required = false)
    private Boolean registEnabled;

    @Schema(description = "注册页链接", required = false)
    private String registUrl;

    @Schema(description = "注册Banner图", required = false)
    private String registBanner;

    @Schema(description = "注册模式(1:普通 2：选择部门)", required = false)
    private Integer registType;

    @Schema(description = "注册按钮文案", required = false)
    private String registText;
}
