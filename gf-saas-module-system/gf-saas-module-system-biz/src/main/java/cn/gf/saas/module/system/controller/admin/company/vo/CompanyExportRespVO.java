package cn.gf.saas.module.system.controller.admin.company.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 单位信息导出 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CompanyExportRespVO {

    @Schema(description = "简称")
    @ExcelProperty("简称")
    private String shortname;

    @Schema(description = "全称")
    @ExcelProperty("全称")
    private String fullname;

    @Schema(description = "单位id")
    @ExcelProperty("单位编号")
    private String companyid;

    @Schema(description = "套餐名")
    @ExcelProperty("单位套餐")
    private String packageName;

    @Schema(description = "套餐状态信息")
    @ExcelProperty("套餐状态")
    private String packageStatusStr;

    @Schema(description = "套餐类型")
    private Boolean isPublic;

    @Schema(description = "套餐类型")
    @ExcelProperty(value = "套餐类型")
    private String isPublicStr;

    @Schema(description = "单位负责人名称")
    @ExcelProperty("联系人")
    private String nickname;

    @Schema(description = "单位负责人手机号")
    @ExcelProperty("联系手机")
    private String mobile;

    @Schema(description = "单位负责人登录名")
    @ExcelProperty("登录名")
    private String username;

    @Schema(description = "所属渠道名称")
    @ExcelProperty("所属渠道")
    private String tenantName;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String creator;

    @Schema(description = "最后操作时间")
    @ExcelProperty("最后操作时间")
    @ColumnWidth(value = 18)
    private LocalDateTime updateTime;

    @Schema(description = "最后操作人")
    @ExcelProperty("最后操作人")
    private String updater;
}
