package cn.gf.saas.module.system.controller.admin.package_con;

import cn.gf.saas.framework.common.enums.CommonStatusEnum;
import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageParam;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.system.controller.admin.package_con.vo.PackagePageReqVO;
import cn.gf.saas.module.system.controller.admin.package_con.vo.PackageRespVO;
import cn.gf.saas.module.system.controller.admin.package_con.vo.PackageSaveReqVO;
import cn.gf.saas.module.system.controller.admin.package_con.vo.PackageSimpleRespVO;
import cn.gf.saas.module.system.service.packageservice.PackageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 套餐")
@RestController
@RequestMapping("/system/package")
@Validated
public class PackageController {

    @Resource
    private PackageService packageService;

    @PostMapping("/create")
    @Operation(summary = "创建套餐")
    @PreAuthorize("@ss.hasPermission('system:package:create')")
    public CommonResult<Long> createPackage(@Valid @RequestBody PackageSaveReqVO createReqVO) {
        return success(packageService.createPackage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新套餐")
    @PreAuthorize("@ss.hasPermission('system:package:update')")
    public CommonResult<Boolean> updatePackage(@Valid @RequestBody PackageSaveReqVO updateReqVO) {
        packageService.updatePackage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除套餐")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:package:delete')")
    public CommonResult<Boolean> deletePackage(@RequestParam("id") Long id) {
        packageService.deletePackage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得套餐")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('system:package:query')")
    public CommonResult<PackageRespVO> getPackage(@RequestParam("id") Long id) {
        return success(packageService.getPackage(id));
    }

    @GetMapping("/public-page")
    @Operation(summary = "获得公共套餐分页")
    @PreAuthorize("@ss.hasPermission('system:package:public-page')")
    public CommonResult<PageResult<PackageRespVO>> getPublicPackagePage(@Valid PackagePageReqVO pageVO) {
        pageVO.setIsPublic(true);
        return success(packageService.getPackagePage(pageVO));
    }

    @GetMapping("/tenant-page")
    @Operation(summary = "获得渠道套餐分页")
    @PreAuthorize("@ss.hasPermission('system:package:tenant-page')")
    public CommonResult<PageResult<PackageRespVO>> getTenantPackagePage(@Valid PackagePageReqVO pageVO) {
        pageVO.setIsPublic(false);
        return success(packageService.getPackagePage(pageVO));
    }


    @GetMapping({"/get-simple-list", "simple-list"})
    @Operation(summary = "获取套餐精简信息列表", description = "只包含被开启的套餐，主要用于前端的下拉选项")
    public CommonResult<List<PackageSimpleRespVO>> getPackageList(@RequestParam("isPublic") Boolean isPublic) {
        PackagePageReqVO pageVO = new PackagePageReqVO();
        pageVO.setIsPublic(isPublic);
        pageVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        pageVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<PackageRespVO> packagePage = packageService.getPackageList(pageVO);
        return success(BeanUtils.toBean(packagePage.getList(), PackageSimpleRespVO.class));
    }

    @GetMapping("/simple-list-by-tenantId")
    @Operation(summary = "获取套餐精简信息列表根据渠道id")
    public CommonResult<List<PackageSimpleRespVO>> getPackageListByTenantId(@RequestParam("isPublic") Boolean isPublic, String tenantId) {
        PackagePageReqVO pageVO = new PackagePageReqVO();
        pageVO.setIsPublic(isPublic);
        pageVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        pageVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageVO.setTenantId(tenantId);
        PageResult<PackageRespVO> packagePage = packageService.getPackageList(pageVO);
        return success(BeanUtils.toBean(packagePage.getList(), PackageSimpleRespVO.class));
    }

}
