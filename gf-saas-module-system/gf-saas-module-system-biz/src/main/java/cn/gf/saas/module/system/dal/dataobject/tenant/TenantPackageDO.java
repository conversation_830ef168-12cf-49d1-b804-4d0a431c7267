package cn.gf.saas.module.system.dal.dataobject.tenant;

import cn.gf.saas.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 租户套餐 DO
 * 新建套餐时，套餐类型（1. 全系统 2.全渠道 3.指定渠道）是指定渠道时，该表存的套餐与租户关联数据
 *
 * <AUTHOR>
 */
@TableName(value = "system_tenant_package", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantPackageDO extends BaseDO {
    /**
     * 主键id，自增
     */
    @TableId
    private Long id;

    /**
     * 套餐编号
     */
    private Long packageId;
    /**
     * 渠道id
     */
    private String tenantId;

}
