package cn.gf.saas.module.system.controller.admin.company.vo;

import cn.gf.saas.framework.common.validation.Mobile;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Schema(description = "管理后台 - 单位信息 Request VO")
@Data
public class CompanyUpdateReqVO {
    @Schema(description = "单位id")
    @NotNull(message = "单位id不能为空")
    private String companyid;

    @Schema(description = "单位状态")
    private Integer deleted;

    @Schema(description = "父级id")
    @NotNull(message = "父级id不能为空")
    private String parentid;

    @Schema(description = "单位全称")
    @NotNull(message = "单位全称不能为空")
    @Size(max = 60, message = "单位全称长度不能超过60个字符")
    private String fullname;

    @Schema(description = "单位简称")
    @NotNull(message = "单位简称不能为空")
    @Size(max = 20, message = "单位简称长度不能超过20个字符")
    private String shortname;

    @Schema(description = "套餐Id")
    private Long packageId;

    @Schema(description = "套餐类型")
    private Boolean isPublic;

    @Schema(description = "手机号码", example = "15601691300")
    @Mobile
    @DiffLogField(name = "手机号码")
    private String mobile;

    @Schema(description = "单位负责人状态")
    private Integer status;

    @Schema(description = "单位负责人Id")
    private Long companyUserId;

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "开发")
    @Size(max = 20, message = "用户昵称长度不能超过20个字符")
    @DiffLogField(name = "用户昵称")
    private String nickname;

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "gf-saas")
    private String username;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    private String password;

    @Schema(description = "所属渠道Id")
    private String tenantId;

    @Schema(description = "是否关联单位负责人")
    private Boolean isReleCompanyLeader;


    @NotNull(message = "密码不能为空")
    @Length(min = 8, max = 20, message = "密码长度为 8-20 位")
    @Pattern(regexp = "^[a-zA-Z0-9]{8,20}$", message = "密码由 数字、字母 组成")
    @JsonIgnore
    public String isPasswordValid() {
       if(companyUserId == null && (StrUtil.isNotEmpty(password))){
           return password;
       }else {
           return null;
       }
    }

    @Pattern(regexp = "^[a-zA-Z0-9]{2,20}$", message = "用户账号由 数字、字母 组成")
    @Size(max = 20, message = "用户账号长度不能超过20个字符")
    public String isUsernameValid() {
        if(companyUserId != null && (StrUtil.isNotEmpty(username))){
            return username;
        }else {
            return null;
        }
    }

}
