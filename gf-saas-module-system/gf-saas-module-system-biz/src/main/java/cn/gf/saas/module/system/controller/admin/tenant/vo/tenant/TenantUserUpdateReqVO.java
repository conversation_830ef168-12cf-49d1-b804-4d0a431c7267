package cn.gf.saas.module.system.controller.admin.tenant.vo.tenant;

import cn.gf.saas.framework.common.validation.Mobile;
import cn.gf.saas.module.system.dal.dataobject.user.AdminUserDO;
import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Schema(description = "管理后台 - 租户创建/修改 Request VO")
@Data
public class TenantUserUpdateReqVO {

    @Schema(description = "租户编号", example = "1024")
    private String id;

    /**
     * 渠道负责人的用户id
     * <p>
     * 关联 {@link AdminUserDO#getId()}
     */
    @Schema(description = "渠道负责人的用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long leaderUserId;

    // ========== 仅【创建】时，需要传递的字段 ==========

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "开发")
    @Size(max = 20, message = "用户昵称长度不能超过20个字符")
    @DiffLogField(name = "用户昵称")
    private String nickname;

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "gf-saas")
    @Pattern(regexp = "^[a-zA-Z0-9]{2,20}$", message = "请输入数字或者字母，2-20个字符")
    @Size(max = 20, message = "用户账号长度不能超过20个字符")
    private String userName;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @Length(min = 8, max = 20, message = "密码长度为 8-20 位")
    @Pattern(regexp = "^[a-zA-Z0-9]{8,20}$", message = "密码由 数字、字母 组成")
    private String password;

    @Schema(description = "手机号码", example = "15601691300")
    @Mobile
    @DiffLogField(name = "手机号码")
    @NotNull(message = "手机号码")
    private String mobile;

}
