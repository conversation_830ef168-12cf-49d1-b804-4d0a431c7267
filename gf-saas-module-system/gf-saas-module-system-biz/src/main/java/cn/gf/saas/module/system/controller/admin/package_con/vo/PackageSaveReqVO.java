package cn.gf.saas.module.system.controller.admin.package_con.vo;

import cn.gf.saas.framework.common.enums.CommonStatusEnum;
import cn.gf.saas.framework.common.enums.PackageTypeEnum;
import cn.gf.saas.framework.common.validation.FileFormat;
import cn.gf.saas.framework.common.validation.FileSize;
import cn.gf.saas.framework.common.validation.InEnum;
import cn.gf.saas.framework.common.validation.ValidUrlFileSize;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Set;

@Schema(description = "管理后台 - 租户套餐创建/修改 Request VO")
@Data
public class PackageSaveReqVO {

    @Schema(description = "套餐编号", example = "1024")
    private Long id;

    @Schema(description = "套餐名", requiredMode = Schema.RequiredMode.REQUIRED, example = "VIP")
    @NotEmpty(message = "套餐名不能为空")
    private String name;

    @Schema(description = "状态，参见 CommonStatusEnum 枚举", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    @InEnum(value = CommonStatusEnum.class, message = "状态必须是 {value}")
    private Integer status;

    @Schema(description = "是否是公共套餐：0否  1是")
    @NotNull(message = "是否是公共套餐 不能为空")
    private Boolean isPublic;

    @Schema(description = "套餐类型，参见 PackageTypeEnum 枚举", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "套餐类型不能为空")
    @InEnum(value = PackageTypeEnum.class, message = "套餐类型必须是 {value}")
    private Integer type;

    @Schema(description = "指定渠道")
    private Set<String> tenantIds;

    @Schema(description = "备注", example = "好")
    private String remark;

    @Schema(description = "功能介绍", example = "http://example.com/file.jpg")
    @Pattern(regexp = "^$|^(https?|ftp)://.*\\.(jpg|png|jpeg)$", message = "文件格式必须为 'jpg', 'png', 'jpeg'")
    @ValidUrlFileSize(maxSize = 2 * 1024 * 1024, message = "文件大小不能超过2MB")
    private String funcDesc;

    @Schema(description = "关联的菜单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联的菜单编号不能为空")
    private Set<Long> menuIds;

}
