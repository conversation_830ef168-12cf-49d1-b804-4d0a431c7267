<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gf.saas.module.system.dal.mysql.tenant.TenantMapper">

    <select id="getCompanyRespList" resultType="cn.gf.saas.module.system.dal.dataobject.tenant.TenantDO"
            parameterType="cn.gf.saas.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO">
        SELECT t.*
        FROM gf_saas.system_tenant t
        LEFT JOIN gf_saas.system_users t1 ON t1.id = t.leader_user_id AND t1.deleted = 0
        LEFT JOIN gf_saas.system_tenant_company t2 ON t2.tenant_id = t.id AND t2.deleted = 0
        <where>
            t.deleted = 0
            <if test="reqVO.ignoreTenantId != null">
                AND t.id != #{reqVO.ignoreTenantId}
            </if>
            <if test="reqVO.name != null">
                AND t.name LIKE CONCAT('%', #{reqVO.name}, '%')
            </if>
            <if test="reqVO.status != null">
                AND t.status = #{reqVO.status}
            </if>
            <if test="reqVO.updateTime != null and reqVO.updateTime.length > 0">
                AND t.update_time BETWEEN #{reqVO.updateTime[0]} AND #{reqVO.updateTime[1]}
            </if>
            <if test="reqVO.nickname != null">
                AND t1.nickname LIKE CONCAT('%', #{reqVO.nickname}, '%')
            </if>
            <if test="reqVO.username != null">
                AND t1.username LIKE CONCAT('%', #{reqVO.username}, '%')
            </if>
            <if test="reqVO.mobile != null">
                AND t1.mobile LIKE CONCAT('%', #{reqVO.mobile}, '%')
            </if>
            <if test="reqVO.companyIds!=null and reqVO.companyIds.size>0">
                AND t2.company_id IN
                <foreach item="item" collection="reqVO.companyIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY t.id
        ORDER BY t.update_time DESC
    </select>

</mapper>
