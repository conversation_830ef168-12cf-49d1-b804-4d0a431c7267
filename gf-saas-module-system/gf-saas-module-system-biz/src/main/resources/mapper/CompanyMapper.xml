<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gf.saas.module.system.dal.mysql.company.CompanyMapper">
    <resultMap id="CompanyRespVOResultMap" type="cn.gf.saas.module.system.controller.admin.company.vo.CompanyRespVO">
        <id column="companyid" property="companyid"/>
        <result column="deleted" property="deleted"/>
        <result column="parentid" property="parentid"/>
        <result column="fullname" property="fullname"/>
        <result column="shortname" property="shortname"/>
        <result column="package_name" property="packageName"/>
        <result column="is_public" property="isPublic"/>
        <result column="nickname" property="nickname"/>
        <result column="mobile" property="mobile"/>
        <result column="username" property="username"/>
        <result column="status" property="status"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="update_time" property="updateTime"/>
        <result column="updater" property="updater"/>
        <result column="creator" property="creator"/>
        <result column="createtime" property="createTime"/>
        <result column="traceid" property="traceId"/>
        <result column="menuid" property="menuid"/>
    </resultMap>

    <select id="getCompanyRespList" resultMap="CompanyRespVOResultMap"
            parameterType="cn.gf.saas.module.system.controller.admin.company.vo.CompanyRespVO">
        SELECT
        c.companyid, c.deleted, c.parentid, c.fullname, c.shortname,
        cp.package_name, cp.package_id, cp.is_public, cp.package_start_date, cp.package_end_date,
        au.id,au.nickname, au.mobile, au.username, au.status,au.id as company_user_id,
        tc.tenant_name,
        tc.update_time, tc.updater, tc.creator, c.createtime,c.traceId,c.menuid
        FROM uemtraining.company c
        LEFT JOIN gf_saas.system_company_user cu ON c.companyid = cu.company_id and cu.is_company_leader = 1 and
        cu.deleted = 0
        LEFT JOIN gf_saas.system_users au ON cu.user_id = au.id
        LEFT JOIN gf_saas.system_tenant_company_package cp ON c.companyid = cp.company_id and cp.deleted = 0
        LEFT JOIN gf_saas.system_tenant_company tc ON c.companyid = tc.company_id and tc.deleted = 0
        <where>
            c.deleted = 0
            <if test="reqVO.companyId != null and reqVO.companyId != ''">
                AND c.companyid = #{reqVO.companyId}
            </if>
            <!--<if test="reqVO.parentId != null">
                AND c.parentid = #{reqVO.parentId}
            </if>-->
            <if test="reqVO.companyName != null">
                AND c.fullname LIKE CONCAT('%', #{reqVO.companyName}, '%')
            </if>
            <if test="reqVO.companyShortName != null  and reqVO.companyShortName != ''">
                AND c.shortname LIKE CONCAT('%', #{reqVO.companyShortName}, '%')
            </if>
            <if test="reqVO.updateTime != null and reqVO.updateTime.length > 0">
                AND tc.update_time BETWEEN #{reqVO.updateTime[0]} AND #{reqVO.updateTime[1]}
            </if>
            <if test="reqVO.companyLeaderNickName != null and reqVO.companyLeaderNickName != ''">
                AND au.nickname LIKE CONCAT('%', #{reqVO.companyLeaderNickName}, '%')
            </if>
            <if test="reqVO.companyLeaderMobile != null and reqVO.companyLeaderMobile != ''">
                AND au.mobile LIKE CONCAT('%', #{reqVO.companyLeaderMobile}, '%')
            </if>
            <if test="reqVO.companyLeaderStatus != null">
                AND au.status = #{reqVO.companyLeaderStatus}
            </if>
            <if test="reqVO.packageIsPublic != null">
                AND cp.is_public = #{reqVO.packageIsPublic}
            </if>
            <if test="reqVO.packageName != null  and reqVO.packageName != ''">
                AND cp.package_name LIKE CONCAT('%', #{reqVO.packageName}, '%')
            </if>
            <if test="reqVO.tenantName != null  and reqVO.tenantName != ''">
                AND tc.tenant_name LIKE CONCAT('%', #{reqVO.tenantName}, '%')
            </if>
            <if test="reqVO.currentUserDataScope!=null and reqVO.currentUserDataScope.size>0">
                AND c.companyid IN
                <foreach item="item" collection="reqVO.currentUserDataScope" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVO.companyPackageStatus != null">
                <choose>
                    <when test="reqVO.companyPackageStatus == 0">
                        AND cp.package_start_date &gt; NOW()
                    </when>
                    <when test="reqVO.companyPackageStatus == 1">
                        AND cp.package_start_date &lt;= NOW()
                        AND cp.package_end_date &gt;= NOW()
                    </when>
                    <when test="reqVO.companyPackageStatus == 2">
                        AND cp.package_end_date &lt; NOW()
                    </when>
                </choose>
            </if>
        </where>
        GROUP BY c.companyid
    </select>

</mapper>
