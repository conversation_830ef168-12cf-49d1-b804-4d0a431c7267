<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gf.saas.module.system.dal.mysql.user.AdminUserMapper">

    <select id="selectPageByCondition" resultType="cn.gf.saas.module.system.dal.dataobject.user.AdminUserDO">
        SELECT DISTINCT
            u.*
        FROM
            system_users u
            LEFT JOIN system_user_role ur ON u.id = ur.user_id
            LEFT JOIN system_role r ON ur.role_id = r.id
            LEFT JOIN uemtraining.department_view d ON u.dept_id = d.department_id
        <where>
            <if test="reqVO.username != null and reqVO.username != ''">
                AND u.username LIKE CONCAT('%', #{reqVO.username}, '%')
            </if>
            <if test="reqVO.nickname != null and reqVO.nickname != ''">
                AND u.nickname LIKE CONCAT('%', #{reqVO.nickname}, '%')
            </if>
            <if test="reqVO.mobile != null and reqVO.mobile != ''">
                AND u.mobile LIKE CONCAT('%', #{reqVO.mobile}, '%')
            </if>
            <if test="reqVO.status != null">
                AND u.status = #{reqVO.status}
            </if>
            <if test="reqVO.userIds != null and reqVO.userIds.size() > 0">
                AND u.id IN
                <foreach collection="reqVO.userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="reqVO.updateTime != null and reqVO.updateTime.size() == 2">
                AND u.update_time BETWEEN #{reqVO.updateTime[0]} AND #{reqVO.updateTime[1]}
            </if>
            <choose>
                <when test="reqVO.deptId != null and reqVO.deptId == 0">
                    AND (u.dept_id = 0 OR u.dept_id IS NULL)
                </when>
                <when test="deptIds != null and deptIds.size() > 0">
                    AND u.dept_id IN
                    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                </when>
            </choose>
            <if test="reqVO.roleName != null and reqVO.roleName != ''">
                AND r.name LIKE CONCAT('%', #{reqVO.roleName}, '%')
            </if>
            <if test="reqVO.deptName != null and reqVO.deptName != ''">
                AND d.department_name LIKE CONCAT('%', #{reqVO.deptName}, '%')
            </if>
        </where>
        ORDER BY u.update_time DESC
    </select>

</mapper> 