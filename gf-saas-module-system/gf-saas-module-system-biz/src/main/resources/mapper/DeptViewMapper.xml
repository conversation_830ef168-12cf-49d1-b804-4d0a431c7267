<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gf.saas.module.system.dal.mysql.dept.DeptViewMapper">

    <select id="getDeptByNameAndLevel" resultType="cn.gf.saas.module.system.dal.dataobject.dept.DepartmentDTO">
        SELECT d.*
        FROM uemtraining.department_view d
                 LEFT JOIN uemtraining.company c ON d.company_id = c.companyid
        WHERE d.department_name = #{name}
            <if test="level != null">
              AND d.level = #{level}
            </if>
          AND c.companyid = #{tenantId}
    </select>

    <select id="getDepartmentListByIds" resultType="cn.gf.saas.module.system.dal.dataobject.dept.DepartmentDTO">
        SELECT d.*
        FROM uemtraining.department_view d
        LEFT JOIN uemtraining.company c ON d.company_id = c.companyid
        WHERE d.department_id IN
        <foreach collection="departmentIds" item="departmentId" open="(" separator="," close=")">
            #{departmentId}
        </foreach>
        AND c.companyid = #{tenantId}
    </select>

</mapper> 