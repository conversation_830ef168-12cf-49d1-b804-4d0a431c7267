<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.gf.saas</groupId>
        <artifactId>gf-saas-module-system</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>gf-saas-module-system-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        system 模块下，我们放通用业务，支撑上层的核心业务。
        例如说：用户、部门、权限、数据字典等等
    </description>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-env</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-web</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-module-mall-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-module-activity-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-module-app-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-biz-data-permission</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-biz-tenant</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-biz-ip</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-security</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-mybatis</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-redis</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-rpc</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-job</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-mq</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-protection</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-excel</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-monitor</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>com.xingyuv</groupId>
            <artifactId>spring-boot-starter-justauth</artifactId> <!-- 社交登陆（例如说，个人微信、企业微信等等） -->
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-mp-spring-boot-starter</artifactId> <!-- 微信登录（公众号） -->
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>  <!-- 微信登录（小程序） -->
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId> <!-- 短信（阿里云） -->
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dysmsapi</artifactId> <!-- 短信（阿里云） -->
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-sms</artifactId> <!-- 短信（腾讯云） -->
        </dependency>

        <dependency>
            <groupId>com.xingyuv</groupId>
            <artifactId>spring-boot-starter-captcha-plus</artifactId> <!-- 验证码，一般用于登录使用 -->
        </dependency>

        <dependency>
            <groupId>com.twelvemonkeys.imageio</groupId>
            <artifactId>imageio-webp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>

        <!-- 视频处理依赖 -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacv</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacpp</artifactId>
        </dependency>
        <!-- OpenCV依赖，如果需要的话 -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>opencv</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>opencv</artifactId>
            <classifier>linux-x86_64</classifier>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>opencv</artifactId>
            <classifier>windows-x86_64</classifier>
        </dependency>

        <!-- FFmpeg核心组件 -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>ffmpeg</artifactId>
        </dependency>
        <!-- FFmpeg平台特定库 -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>ffmpeg</artifactId>
            <classifier>linux-x86_64</classifier>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>ffmpeg</artifactId>
            <classifier>windows-x86_64</classifier>
        </dependency>
        <!-- 如果需要支持macOS，添加如下依赖 -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>ffmpeg</artifactId>
            <classifier>macosx-x86_64</classifier>
        </dependency>
        <!-- 如果您的服务器是ARM架构（如Apple M1/M2或ARM Linux服务器） -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>ffmpeg</artifactId>
            <classifier>macosx-arm64</classifier>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
